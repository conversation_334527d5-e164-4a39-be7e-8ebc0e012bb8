<?php
require_once __DIR__ . '/../config/config.php';

// Set response header to JSON
header('Content-Type: application/json');

$targetDir = __DIR__ . '/../uploads/pdfs/';  // Path to the pdfs directory (same as admin upload.php)
$response = [];

// Create uploads directory if it doesn't exist
if (!file_exists($targetDir)) {
    mkdir($targetDir, 0777, true);
}

// Check if file was uploaded
if (isset($_FILES['pdf_file']) && $_FILES['pdf_file']['error'] === UPLOAD_ERR_OK) {
    $fileName = basename($_FILES['pdf_file']['name']);
    $targetFilePath = $targetDir . $fileName;
    $fileType = $_FILES['pdf_file']['type'];
    $fileSizeKB = $_FILES['pdf_file']['size'] / 1024;
    
    // Check if file is a PDF
    if ($fileType === "application/pdf" || pathinfo($fileName, PATHINFO_EXTENSION) === 'pdf') {
        // Move the uploaded file to the target directory
        if (move_uploaded_file($_FILES["pdf_file"]["tmp_name"], $targetFilePath)) {
            $db = new Database();
            $conn = $db->getConnection();
            
            if ($conn) {
                // Store file information in the database
                $stmt = $conn->prepare("INSERT INTO documents (file_name, file_type, file_size_kb, uploaded_by, upload_date, file_path) VALUES (?, ?, ?, ?, NOW(), ?)");
                $uploadedBy = isset($_POST['uploaded_by']) ? $_POST['uploaded_by'] : "Unknown";
                $stmt->execute([$fileName, $fileType, $fileSizeKB, $uploadedBy, $targetFilePath]);
                
                // Get the file ID for the response
                $fileId = $conn->lastInsertId();
                
                $response = [
                    "success" => true, 
                    "message" => "PDF file uploaded successfully.",
                    "file_id" => $fileId,
                    "file_name" => $fileName,
                    "file_path" => $targetFilePath
                ];
            } else {
                $response = ["success" => false, "message" => "Database connection failed."];
            }
        } else {
            $response = ["success" => false, "message" => "Failed to move uploaded file."];
        }
    } else {
        $response = ["success" => false, "message" => "Only PDF files are allowed."];
    }
} else {
    $errorMessage = "No file uploaded or upload error occurred.";
    if (isset($_FILES['pdf_file'])) {
        switch ($_FILES['pdf_file']['error']) {
            case UPLOAD_ERR_INI_SIZE:
                $errorMessage = "The uploaded file exceeds the upload_max_filesize directive in php.ini";
                break;
            case UPLOAD_ERR_FORM_SIZE:
                $errorMessage = "The uploaded file exceeds the MAX_FILE_SIZE directive in the HTML form";
                break;
            case UPLOAD_ERR_PARTIAL:
                $errorMessage = "The uploaded file was only partially uploaded";
                break;
            case UPLOAD_ERR_NO_FILE:
                $errorMessage = "No file was uploaded";
                break;
            case UPLOAD_ERR_NO_TMP_DIR:
                $errorMessage = "Missing a temporary folder";
                break;
            case UPLOAD_ERR_CANT_WRITE:
                $errorMessage = "Failed to write file to disk";
                break;
            case UPLOAD_ERR_EXTENSION:
                $errorMessage = "File upload stopped by extension";
                break;
        }
    }
    $response = ["success" => false, "message" => $errorMessage];
}

echo json_encode($response);
