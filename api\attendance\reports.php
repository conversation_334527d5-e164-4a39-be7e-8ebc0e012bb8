<?php
/**
 * Reports API Endpoint
 * 
 * @package MtcInvoice Attendance
 * @version 1.0
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once '../config/config.php';
require_once '../../classes/AttendanceManager.php';
require_once '../../classes/AnalyticsEngine.php';

// Get request method and path
$method = $_SERVER['REQUEST_METHOD'];
$path = $_SERVER['REQUEST_URI'];
$segments = explode('/', trim(parse_url($path, PHP_URL_PATH), '/'));

// Remove 'api/attendance/reports.php' from segments
$segments = array_slice($segments, 3);

/**
 * Send JSON response
 */
function sendJsonResponse($status, $data = null, $message = null) {
    http_response_code($status);
    echo json_encode([
        'success' => $status >= 200 && $status < 300,
        'data' => $data,
        'message' => $message,
        'timestamp' => date('Y-m-d H:i:s')
    ]);
    exit();
}

/**
 * Send CSV response
 */
function sendCsvResponse($data, $filename) {
    header('Content-Type: text/csv');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Cache-Control: no-cache, must-revalidate');
    header('Expires: Mon, 26 Jul 1997 05:00:00 GMT');
    
    $output = fopen('php://output', 'w');
    
    if (!empty($data)) {
        // Write headers
        fputcsv($output, array_keys($data[0]));
        
        // Write data
        foreach ($data as $row) {
            fputcsv($output, $row);
        }
    }
    
    fclose($output);
    exit();
}

// Initialize managers
try {
    $attendanceManager = new AttendanceManager();
    $analyticsEngine = new AnalyticsEngine();
} catch (Exception $e) {
    sendJsonResponse(500, null, 'Service initialization failed');
}

// Handle the request based on method and endpoint
try {
    if ($method !== 'GET') {
        sendJsonResponse(405, null, 'Method not allowed');
    }
    
    if (isset($segments[0])) {
        switch ($segments[0]) {
            case 'daily':
                generateDailyReport();
                break;
                
            case 'weekly':
                generateWeeklyReport();
                break;
                
            case 'monthly':
                generateMonthlyReport();
                break;
                
            case 'worker':
                if (isset($segments[1]) && is_numeric($segments[1])) {
                    generateWorkerReport($segments[1]);
                } else {
                    sendJsonResponse(400, null, 'Worker ID is required');
                }
                break;
                
            case 'department':
                if (isset($segments[1])) {
                    generateDepartmentReport($segments[1]);
                } else {
                    sendJsonResponse(400, null, 'Department name is required');
                }
                break;
                
            case 'summary':
                generateSummaryReport();
                break;
                
            case 'export':
                exportReport();
                break;
                
            default:
                sendJsonResponse(404, null, 'Report type not found');
        }
    } else {
        // Default: return available report types
        getAvailableReports();
    }
    
} catch (Exception $e) {
    error_log("Reports API error: " . $e->getMessage());
    sendJsonResponse(500, null, 'Internal server error');
}

/**
 * Get available report types
 */
function getAvailableReports() {
    $reports = [
        'daily' => [
            'name' => 'Daily Attendance Report',
            'description' => 'Daily attendance records with detailed information',
            'endpoint' => '/api/attendance/reports/daily'
        ],
        'weekly' => [
            'name' => 'Weekly Attendance Report',
            'description' => 'Weekly attendance summary and trends',
            'endpoint' => '/api/attendance/reports/weekly'
        ],
        'monthly' => [
            'name' => 'Monthly Attendance Report',
            'description' => 'Monthly attendance statistics and analytics',
            'endpoint' => '/api/attendance/reports/monthly'
        ],
        'worker' => [
            'name' => 'Individual Worker Report',
            'description' => 'Detailed attendance report for specific worker',
            'endpoint' => '/api/attendance/reports/worker/{worker_id}'
        ],
        'department' => [
            'name' => 'Department Report',
            'description' => 'Department-wise attendance analytics',
            'endpoint' => '/api/attendance/reports/department/{department_name}'
        ],
        'summary' => [
            'name' => 'Executive Summary Report',
            'description' => 'High-level attendance overview and insights',
            'endpoint' => '/api/attendance/reports/summary'
        ]
    ];
    
    sendJsonResponse(200, $reports);
}

/**
 * Generate daily attendance report
 */
function generateDailyReport() {
    global $attendanceManager;
    
    try {
        $date = $_GET['date'] ?? date('Y-m-d');
        $department = $_GET['department'] ?? null;
        
        $filters = [
            'start_date' => $date,
            'end_date' => $date,
            'limit' => 1000
        ];
        
        if ($department) {
            $filters['department'] = $department;
        }
        
        $result = $attendanceManager->getAttendanceRecords($filters);
        
        // Calculate daily statistics
        $stats = [
            'date' => $date,
            'total_workers' => count(array_unique(array_column($result['records'], 'worker_id'))),
            'present' => count(array_filter($result['records'], fn($r) => $r['status'] === 'present')),
            'late' => count(array_filter($result['records'], fn($r) => $r['status'] === 'late')),
            'absent' => count(array_filter($result['records'], fn($r) => $r['status'] === 'absent')),
            'sick_leave' => count(array_filter($result['records'], fn($r) => $r['status'] === 'sick_leave')),
            'vacation' => count(array_filter($result['records'], fn($r) => $r['status'] === 'vacation')),
            'remote_work' => count(array_filter($result['records'], fn($r) => $r['status'] === 'remote_work')),
            'total_overtime_hours' => array_sum(array_column($result['records'], 'overtime_minutes')) / 60
        ];
        
        $stats['attendance_rate'] = $stats['total_workers'] > 0 ? 
            round((($stats['present'] + $stats['late']) / $stats['total_workers']) * 100, 2) : 0;
        
        sendJsonResponse(200, [
            'report_type' => 'daily',
            'date' => $date,
            'department' => $department,
            'statistics' => $stats,
            'records' => $result['records']
        ]);
        
    } catch (Exception $e) {
        error_log("Generate daily report error: " . $e->getMessage());
        sendJsonResponse(500, null, 'Failed to generate daily report');
    }
}

/**
 * Generate weekly attendance report
 */
function generateWeeklyReport() {
    global $analyticsEngine;
    
    try {
        $startDate = $_GET['start_date'] ?? date('Y-m-d', strtotime('monday this week'));
        $endDate = $_GET['end_date'] ?? date('Y-m-d', strtotime('sunday this week'));
        $department = $_GET['department'] ?? null;
        
        $filters = [
            'start_date' => $startDate,
            'end_date' => $endDate
        ];
        
        if ($department) {
            $filters['department'] = $department;
        }
        
        $overview = $analyticsEngine->getAttendanceOverview($filters);
        $trends = $analyticsEngine->getAttendanceTrends('daily', $filters);
        $performance = $analyticsEngine->getWorkerPerformance(null, $filters);
        
        sendJsonResponse(200, [
            'report_type' => 'weekly',
            'period' => [
                'start_date' => $startDate,
                'end_date' => $endDate
            ],
            'department' => $department,
            'overview' => $overview,
            'daily_trends' => $trends,
            'worker_performance' => $performance
        ]);
        
    } catch (Exception $e) {
        error_log("Generate weekly report error: " . $e->getMessage());
        sendJsonResponse(500, null, 'Failed to generate weekly report');
    }
}

/**
 * Generate monthly attendance report
 */
function generateMonthlyReport() {
    global $analyticsEngine;
    
    try {
        $month = $_GET['month'] ?? date('Y-m');
        $startDate = $month . '-01';
        $endDate = date('Y-m-t', strtotime($startDate));
        $department = $_GET['department'] ?? null;
        
        $filters = [
            'start_date' => $startDate,
            'end_date' => $endDate
        ];
        
        if ($department) {
            $filters['department'] = $department;
        }
        
        $overview = $analyticsEngine->getAttendanceOverview($filters);
        $trends = $analyticsEngine->getAttendanceTrends('weekly', $filters);
        $departmentAnalytics = $analyticsEngine->getDepartmentAnalytics($filters);
        $performance = $analyticsEngine->getWorkerPerformance(null, $filters);
        $emojiStats = $analyticsEngine->getEmojiAnalytics($filters);
        $productivityInsights = $analyticsEngine->getProductivityInsights($filters);
        
        sendJsonResponse(200, [
            'report_type' => 'monthly',
            'month' => $month,
            'period' => [
                'start_date' => $startDate,
                'end_date' => $endDate
            ],
            'department' => $department,
            'overview' => $overview,
            'weekly_trends' => $trends,
            'department_analytics' => $departmentAnalytics,
            'worker_performance' => $performance,
            'emoji_statistics' => $emojiStats,
            'productivity_insights' => $productivityInsights
        ]);
        
    } catch (Exception $e) {
        error_log("Generate monthly report error: " . $e->getMessage());
        sendJsonResponse(500, null, 'Failed to generate monthly report');
    }
}

/**
 * Generate individual worker report
 */
function generateWorkerReport($workerId) {
    global $attendanceManager, $analyticsEngine;
    
    try {
        $startDate = $_GET['start_date'] ?? date('Y-m-d', strtotime('-30 days'));
        $endDate = $_GET['end_date'] ?? date('Y-m-d');
        
        $filters = [
            'start_date' => $startDate,
            'end_date' => $endDate
        ];
        
        // Get worker info
        $pdo = (new Database())->getConnection();
        $stmt = $pdo->prepare("
            SELECT w.*, m.name as manager_name 
            FROM workers w 
            LEFT JOIN workers m ON w.manager_id = m.id 
            WHERE w.id = ?
        ");
        $stmt->execute([$workerId]);
        $worker = $stmt->fetch();
        
        if (!$worker) {
            sendJsonResponse(404, null, 'Worker not found');
            return;
        }
        
        // Get attendance records
        $attendanceResult = $attendanceManager->getAttendanceRecords(['worker_id' => $workerId] + $filters);
        
        // Get performance analytics
        $performance = $analyticsEngine->getWorkerPerformance($workerId, $filters);
        
        // Get heatmap data
        $heatmap = $analyticsEngine->getAttendanceHeatmap($workerId, $filters);
        
        sendJsonResponse(200, [
            'report_type' => 'worker',
            'worker' => $worker,
            'period' => [
                'start_date' => $startDate,
                'end_date' => $endDate
            ],
            'attendance_records' => $attendanceResult['records'],
            'performance_metrics' => $performance[0] ?? null,
            'heatmap_data' => $heatmap
        ]);
        
    } catch (Exception $e) {
        error_log("Generate worker report error: " . $e->getMessage());
        sendJsonResponse(500, null, 'Failed to generate worker report');
    }
}

/**
 * Generate department report
 */
function generateDepartmentReport($department) {
    global $analyticsEngine;
    
    try {
        $startDate = $_GET['start_date'] ?? date('Y-m-d', strtotime('-30 days'));
        $endDate = $_GET['end_date'] ?? date('Y-m-d');
        
        $filters = [
            'start_date' => $startDate,
            'end_date' => $endDate,
            'department' => urldecode($department)
        ];
        
        $overview = $analyticsEngine->getAttendanceOverview($filters);
        $trends = $analyticsEngine->getAttendanceTrends('daily', $filters);
        $performance = $analyticsEngine->getWorkerPerformance(null, $filters);
        $emojiStats = $analyticsEngine->getEmojiAnalytics($filters);
        
        sendJsonResponse(200, [
            'report_type' => 'department',
            'department' => urldecode($department),
            'period' => [
                'start_date' => $startDate,
                'end_date' => $endDate
            ],
            'overview' => $overview,
            'daily_trends' => $trends,
            'worker_performance' => $performance,
            'emoji_statistics' => $emojiStats
        ]);
        
    } catch (Exception $e) {
        error_log("Generate department report error: " . $e->getMessage());
        sendJsonResponse(500, null, 'Failed to generate department report');
    }
}

/**
 * Generate executive summary report
 */
function generateSummaryReport() {
    global $analyticsEngine;
    
    try {
        $startDate = $_GET['start_date'] ?? date('Y-m-d', strtotime('-30 days'));
        $endDate = $_GET['end_date'] ?? date('Y-m-d');
        
        $filters = [
            'start_date' => $startDate,
            'end_date' => $endDate
        ];
        
        $overview = $analyticsEngine->getAttendanceOverview($filters);
        $departmentAnalytics = $analyticsEngine->getDepartmentAnalytics($filters);
        $topPerformers = array_slice($analyticsEngine->getWorkerPerformance(null, $filters), 0, 10);
        $productivityInsights = $analyticsEngine->getProductivityInsights($filters);
        
        // Calculate key insights
        $insights = [
            'highest_attendance_department' => null,
            'lowest_attendance_department' => null,
            'most_productive_day' => null,
            'total_overtime_hours' => ($overview['total_overtime_minutes'] ?? 0) / 60
        ];
        
        if (!empty($departmentAnalytics)) {
            usort($departmentAnalytics, fn($a, $b) => $b['attendance_rate'] <=> $a['attendance_rate']);
            $insights['highest_attendance_department'] = $departmentAnalytics[0];
            $insights['lowest_attendance_department'] = end($departmentAnalytics);
        }
        
        if (!empty($productivityInsights)) {
            usort($productivityInsights, fn($a, $b) => $b['avg_productivity'] <=> $a['avg_productivity']);
            $insights['most_productive_day'] = $productivityInsights[0];
        }
        
        sendJsonResponse(200, [
            'report_type' => 'summary',
            'period' => [
                'start_date' => $startDate,
                'end_date' => $endDate
            ],
            'overview' => $overview,
            'department_analytics' => $departmentAnalytics,
            'top_performers' => $topPerformers,
            'productivity_insights' => $productivityInsights,
            'key_insights' => $insights
        ]);
        
    } catch (Exception $e) {
        error_log("Generate summary report error: " . $e->getMessage());
        sendJsonResponse(500, null, 'Failed to generate summary report');
    }
}

/**
 * Export report as CSV
 */
function exportReport() {
    global $attendanceManager;
    
    try {
        $format = $_GET['format'] ?? 'csv';
        $type = $_GET['type'] ?? 'daily';
        
        if ($format !== 'csv') {
            sendJsonResponse(400, null, 'Only CSV format is currently supported');
            return;
        }
        
        $startDate = $_GET['start_date'] ?? date('Y-m-d', strtotime('-30 days'));
        $endDate = $_GET['end_date'] ?? date('Y-m-d');
        $department = $_GET['department'] ?? null;
        
        $filters = [
            'start_date' => $startDate,
            'end_date' => $endDate,
            'limit' => 10000
        ];
        
        if ($department) {
            $filters['department'] = $department;
        }
        
        $result = $attendanceManager->getAttendanceRecords($filters);
        
        // Prepare data for CSV export
        $csvData = [];
        foreach ($result['records'] as $record) {
            $csvData[] = [
                'Employee ID' => $record['employee_id'],
                'Worker Name' => $record['worker_name'],
                'Department' => $record['department'],
                'Date' => $record['attendance_date'],
                'Status' => $record['status'],
                'Check In' => $record['check_in_time'] ?? '',
                'Check Out' => $record['check_out_time'] ?? '',
                'Break Duration (min)' => $record['break_duration_minutes'] ?? 0,
                'Overtime (min)' => $record['overtime_minutes'] ?? 0,
                'Productivity Score' => $record['productivity_score'] ?? '',
                'Emoji Tags' => is_array($record['emoji_tags']) ? implode(', ', $record['emoji_tags']) : '',
                'Notes' => $record['notes'] ?? ''
            ];
        }
        
        $filename = "attendance_report_{$type}_{$startDate}_to_{$endDate}.csv";
        sendCsvResponse($csvData, $filename);
        
    } catch (Exception $e) {
        error_log("Export report error: " . $e->getMessage());
        sendJsonResponse(500, null, 'Failed to export report');
    }
}
