<?php
/**
 * Attendance Records API Endpoint
 * 
 * @package MtcInvoice Attendance
 * @version 1.0
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once '../config/config.php';
require_once '../../classes/AttendanceManager.php';
require_once '../../classes/AlertSystem.php';

// Get request method and path
$method = $_SERVER['REQUEST_METHOD'];
$path = $_SERVER['REQUEST_URI'];
$segments = explode('/', trim(parse_url($path, PHP_URL_PATH), '/'));

// Remove 'api/attendance/records.php' from segments
$segments = array_slice($segments, 3);

/**
 * Send JSON response
 */
function sendJsonResponse($status, $data = null, $message = null) {
    http_response_code($status);
    echo json_encode([
        'success' => $status >= 200 && $status < 300,
        'data' => $data,
        'message' => $message,
        'timestamp' => date('Y-m-d H:i:s')
    ]);
    exit();
}

/**
 * Validate required fields
 */
function validateRequired($data, $fields) {
    $missing = [];
    foreach ($fields as $field) {
        if (!isset($data[$field]) || empty(trim($data[$field]))) {
            $missing[] = $field;
        }
    }
    return $missing;
}

/**
 * Sanitize input data
 */
function sanitizeInput($data) {
    if (is_array($data)) {
        return array_map('sanitizeInput', $data);
    }
    return htmlspecialchars(strip_tags(trim($data)), ENT_QUOTES, 'UTF-8');
}

// Initialize managers
try {
    $attendanceManager = new AttendanceManager();
    $alertSystem = new AlertSystem();
    $attendanceManager->setAlertSystem($alertSystem);
} catch (Exception $e) {
    sendJsonResponse(500, null, 'Service initialization failed');
}

// Handle the request based on method and endpoint
try {
    switch ($method) {
        case 'GET':
            if (isset($segments[0]) && is_numeric($segments[0])) {
                // Get single attendance record
                getAttendanceRecord($segments[0]);
            } elseif (isset($segments[0]) && $segments[0] === 'timeline') {
                // Get timeline data for calendar view
                getTimelineData();
            } elseif (isset($segments[0]) && $segments[0] === 'worker' && isset($segments[1])) {
                // Get attendance records for specific worker
                getWorkerAttendance($segments[1]);
            } else {
                // Get attendance records list
                getAttendanceRecords();
            }
            break;
            
        case 'POST':
            if (isset($segments[0]) && $segments[0] === 'bulk') {
                // Bulk attendance recording
                bulkRecordAttendance();
            } else {
                // Record single attendance
                recordAttendance();
            }
            break;
            
        case 'PUT':
        case 'PATCH':
            // Update existing attendance record
            if (isset($segments[0]) && is_numeric($segments[0])) {
                updateAttendanceRecord($segments[0]);
            } else {
                sendJsonResponse(400, null, 'Attendance record ID is required for update');
            }
            break;
            
        case 'DELETE':
            // Delete attendance record
            if (isset($segments[0]) && is_numeric($segments[0])) {
                deleteAttendanceRecord($segments[0]);
            } else {
                sendJsonResponse(400, null, 'Attendance record ID is required for deletion');
            }
            break;
            
        default:
            sendJsonResponse(405, null, 'Method not allowed');
    }
} catch (Exception $e) {
    error_log("Attendance Records API error: " . $e->getMessage());
    sendJsonResponse(500, null, 'Internal server error');
}

/**
 * Get attendance records with filtering
 */
function getAttendanceRecords() {
    global $attendanceManager;
    
    try {
        $filters = [];
        
        // Apply filters from query parameters
        if (isset($_GET['worker_id']) && !empty($_GET['worker_id'])) {
            $filters['worker_id'] = $_GET['worker_id'];
        }
        
        if (isset($_GET['department']) && !empty($_GET['department'])) {
            $filters['department'] = $_GET['department'];
        }
        
        if (isset($_GET['status']) && !empty($_GET['status'])) {
            $filters['status'] = $_GET['status'];
        }
        
        if (isset($_GET['start_date']) && !empty($_GET['start_date'])) {
            $filters['start_date'] = $_GET['start_date'];
        }
        
        if (isset($_GET['end_date']) && !empty($_GET['end_date'])) {
            $filters['end_date'] = $_GET['end_date'];
        }
        
        // Pagination
        if (isset($_GET['page']) && !empty($_GET['page'])) {
            $filters['page'] = $_GET['page'];
        }
        
        if (isset($_GET['limit']) && !empty($_GET['limit'])) {
            $filters['limit'] = $_GET['limit'];
        }
        
        $result = $attendanceManager->getAttendanceRecords($filters);
        sendJsonResponse(200, $result);
        
    } catch (Exception $e) {
        error_log("Get attendance records error: " . $e->getMessage());
        sendJsonResponse(500, null, 'Failed to retrieve attendance records');
    }
}

/**
 * Get single attendance record by ID
 */
function getAttendanceRecord($id) {
    global $attendanceManager;
    
    try {
        $record = $attendanceManager->getAttendanceById($id);
        
        if (!$record) {
            sendJsonResponse(404, null, 'Attendance record not found');
            return;
        }
        
        sendJsonResponse(200, $record);
        
    } catch (Exception $e) {
        error_log("Get attendance record error: " . $e->getMessage());
        sendJsonResponse(500, null, 'Failed to retrieve attendance record');
    }
}

/**
 * Get timeline data for calendar view
 */
function getTimelineData() {
    global $attendanceManager;
    
    try {
        $filters = [];
        
        // Date range (default to current month)
        $startDate = $_GET['start_date'] ?? date('Y-m-01');
        $endDate = $_GET['end_date'] ?? date('Y-m-t');
        
        $filters['start_date'] = $startDate;
        $filters['end_date'] = $endDate;
        
        if (isset($_GET['department']) && !empty($_GET['department'])) {
            $filters['department'] = $_GET['department'];
        }
        
        // Get all records for the period
        $filters['limit'] = 1000; // Large limit for timeline view
        $result = $attendanceManager->getAttendanceRecords($filters);
        
        // Transform data for timeline calendar
        $timelineData = [];
        foreach ($result['records'] as $record) {
            $timelineData[] = [
                'id' => $record['id'],
                'worker_id' => $record['worker_id'],
                'worker_name' => $record['worker_name'],
                'employee_id' => $record['employee_id'],
                'department' => $record['department'],
                'date' => $record['attendance_date'],
                'status' => $record['status'],
                'check_in' => $record['check_in_time'],
                'check_out' => $record['check_out_time'],
                'break_duration' => $record['break_duration_minutes'],
                'overtime' => $record['overtime_minutes'],
                'emoji_tags' => $record['emoji_tags'],
                'notes' => $record['notes'],
                'productivity_score' => $record['productivity_score']
            ];
        }
        
        sendJsonResponse(200, [
            'timeline_data' => $timelineData,
            'date_range' => [
                'start_date' => $startDate,
                'end_date' => $endDate
            ]
        ]);
        
    } catch (Exception $e) {
        error_log("Get timeline data error: " . $e->getMessage());
        sendJsonResponse(500, null, 'Failed to retrieve timeline data');
    }
}

/**
 * Get attendance records for specific worker
 */
function getWorkerAttendance($workerId) {
    global $attendanceManager;
    
    try {
        $filters = ['worker_id' => $workerId];
        
        if (isset($_GET['start_date']) && !empty($_GET['start_date'])) {
            $filters['start_date'] = $_GET['start_date'];
        }
        
        if (isset($_GET['end_date']) && !empty($_GET['end_date'])) {
            $filters['end_date'] = $_GET['end_date'];
        }
        
        $result = $attendanceManager->getAttendanceRecords($filters);
        sendJsonResponse(200, $result);
        
    } catch (Exception $e) {
        error_log("Get worker attendance error: " . $e->getMessage());
        sendJsonResponse(500, null, 'Failed to retrieve worker attendance');
    }
}

/**
 * Record new attendance
 */
function recordAttendance() {
    global $attendanceManager;
    
    try {
        $data = json_decode(file_get_contents('php://input'), true);
        
        if (empty($data)) {
            sendJsonResponse(400, null, 'No data provided');
            return;
        }
        
        // Validate required fields
        $required = ['worker_id', 'attendance_date', 'status'];
        $missing = validateRequired($data, $required);
        if (!empty($missing)) {
            sendJsonResponse(400, null, 'Missing required fields: ' . implode(', ', $missing));
            return;
        }
        
        // Add created_by from session (TODO: implement proper authentication)
        $data['created_by'] = 1;
        
        $record = $attendanceManager->recordAttendance($data);
        sendJsonResponse(201, $record, 'Attendance recorded successfully');
        
    } catch (Exception $e) {
        error_log("Record attendance error: " . $e->getMessage());
        sendJsonResponse(500, null, 'Failed to record attendance');
    }
}

/**
 * Bulk record attendance
 */
function bulkRecordAttendance() {
    global $attendanceManager;
    
    try {
        $data = json_decode(file_get_contents('php://input'), true);
        
        if (empty($data) || !isset($data['records']) || !is_array($data['records'])) {
            sendJsonResponse(400, null, 'Invalid bulk data format');
            return;
        }
        
        $results = [];
        $errors = [];
        
        foreach ($data['records'] as $index => $recordData) {
            try {
                // Validate required fields
                $required = ['worker_id', 'attendance_date', 'status'];
                $missing = validateRequired($recordData, $required);
                if (!empty($missing)) {
                    $errors[] = "Record $index: Missing required fields: " . implode(', ', $missing);
                    continue;
                }
                
                // Add created_by
                $recordData['created_by'] = 1;
                
                $record = $attendanceManager->recordAttendance($recordData);
                $results[] = $record;
                
            } catch (Exception $e) {
                $errors[] = "Record $index: " . $e->getMessage();
            }
        }
        
        sendJsonResponse(200, [
            'successful_records' => $results,
            'errors' => $errors,
            'total_processed' => count($data['records']),
            'successful_count' => count($results),
            'error_count' => count($errors)
        ], 'Bulk attendance processing completed');
        
    } catch (Exception $e) {
        error_log("Bulk record attendance error: " . $e->getMessage());
        sendJsonResponse(500, null, 'Failed to process bulk attendance');
    }
}

/**
 * Update existing attendance record
 */
function updateAttendanceRecord($id) {
    global $attendanceManager;
    
    try {
        $data = json_decode(file_get_contents('php://input'), true);

        if (empty($data)) {
            sendJsonResponse(400, null, 'No data provided for update');
            return;
        }

        $record = $attendanceManager->updateAttendance($id, $data);
        
        if (!$record) {
            sendJsonResponse(404, null, 'Attendance record not found');
            return;
        }
        
        sendJsonResponse(200, $record, 'Attendance record updated successfully');

    } catch (Exception $e) {
        error_log("Update attendance record error: " . $e->getMessage());
        sendJsonResponse(500, null, 'Failed to update attendance record');
    }
}

/**
 * Delete attendance record
 */
function deleteAttendanceRecord($id) {
    try {
        $pdo = (new Database())->getConnection();
        
        // Check if record exists
        $stmt = $pdo->prepare("SELECT id FROM attendance_records WHERE id = ?");
        $stmt->execute([$id]);
        if (!$stmt->fetch()) {
            sendJsonResponse(404, null, 'Attendance record not found');
            return;
        }
        
        // Delete the record
        $stmt = $pdo->prepare("DELETE FROM attendance_records WHERE id = ?");
        $stmt->execute([$id]);
        
        sendJsonResponse(200, null, 'Attendance record deleted successfully');

    } catch (Exception $e) {
        error_log("Delete attendance record error: " . $e->getMessage());
        sendJsonResponse(500, null, 'Failed to delete attendance record');
    }
}
