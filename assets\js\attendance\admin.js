/**
 * Attendance Admin Interface
 * 
 * JavaScript functionality for the attendance admin dashboard
 * including worker management, reports, alerts, and settings.
 * 
 * @package MtcInvoice Attendance
 * @version 1.0
 */

class AttendanceAdmin {
    constructor() {
        this.apiBaseUrl = '/MtcInvoiceNewProject/api/attendance';
        this.workers = [];
        this.alerts = [];
        this.settings = {};
        
        this.init();
    }
    
    async init() {
        try {
            await this.loadInitialData();
            this.setupEventListeners();
            this.initializeTabs();
        } catch (error) {
            console.error('Failed to initialize attendance admin:', error);
            this.showError('Failed to initialize admin interface');
        }
    }
    
    async loadInitialData() {
        try {
            // Load workers
            await this.loadWorkers();
            
            // Load alerts
            await this.loadAlerts();
            
            // Load settings
            await this.loadSettings();
            
            // Update alerts count
            this.updateAlertsCount();
            
        } catch (error) {
            console.error('Failed to load initial data:', error);
        }
    }
    
    setupEventListeners() {
        // Tab change events
        document.querySelectorAll('#attendanceTabs button[data-bs-toggle="tab"]').forEach(tab => {
            tab.addEventListener('shown.bs.tab', (e) => {
                const targetTab = e.target.getAttribute('data-bs-target');
                this.handleTabChange(targetTab);
            });
        });
        
        // Workers search
        const workersSearch = document.getElementById('workersSearch');
        if (workersSearch) {
            workersSearch.addEventListener('input', (e) => {
                this.filterWorkers(e.target.value);
            });
        }
        
        // Alert filters
        const alertSeverityFilter = document.getElementById('alertSeverityFilter');
        const alertTypeFilter = document.getElementById('alertTypeFilter');
        
        if (alertSeverityFilter) {
            alertSeverityFilter.addEventListener('change', () => this.filterAlerts());
        }
        
        if (alertTypeFilter) {
            alertTypeFilter.addEventListener('change', () => this.filterAlerts());
        }
        
        // Settings form
        const settingsForm = document.getElementById('attendanceSettingsForm');
        if (settingsForm) {
            settingsForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.saveSettings();
            });
        }
    }
    
    initializeTabs() {
        // Initialize with calendar tab active
        this.handleTabChange('#calendar');
    }
    
    handleTabChange(targetTab) {
        switch (targetTab) {
            case '#workers':
                this.renderWorkersTable();
                break;
            case '#alerts':
                this.renderAlertsList();
                break;
            case '#settings':
                this.populateSettingsForm();
                break;
        }
    }
    
    async loadWorkers() {
        try {
            const response = await fetch(`${this.apiBaseUrl}/workers.php`);
            const data = await response.json();
            
            if (data.success) {
                this.workers = data.data.workers || [];
            } else {
                throw new Error(data.message || 'Failed to load workers');
            }
        } catch (error) {
            console.error('Failed to load workers:', error);
            this.workers = [];
        }
    }
    
    async loadAlerts() {
        try {
            const response = await fetch(`${this.apiBaseUrl}/alerts.php`);
            const data = await response.json();
            
            if (data.success) {
                this.alerts = data.data.alerts || [];
            } else {
                throw new Error(data.message || 'Failed to load alerts');
            }
        } catch (error) {
            console.error('Failed to load alerts:', error);
            this.alerts = [];
        }
    }
    
    async loadSettings() {
        try {
            // Load settings from API (would need to implement settings endpoint)
            // For now, use default values
            this.settings = {
                work_start_time: '09:00:00',
                work_end_time: '17:00:00',
                late_threshold_minutes: 15,
                half_day_hours: 4,
                overtime_threshold_hours: 8,
                consecutive_absence_alert: 3,
                monthly_working_days: 22,
                require_approval: false
            };
        } catch (error) {
            console.error('Failed to load settings:', error);
        }
    }
    
    renderWorkersTable() {
        const tableBody = document.querySelector('#workersTable tbody');
        if (!tableBody) return;
        
        if (this.workers.length === 0) {
            tableBody.innerHTML = '<tr><td colspan="7" class="text-center">No workers found</td></tr>';
            return;
        }
        
        tableBody.innerHTML = this.workers.map(worker => `
            <tr>
                <td>${worker.employee_id}</td>
                <td>
                    <div class="d-flex align-items-center">
                        <div class="worker-avatar me-2">
                            ${worker.profile_image ? 
                                `<img src="${worker.profile_image}" alt="${worker.name}" class="rounded-circle" width="32" height="32">` :
                                `<div class="avatar-circle">${worker.name.charAt(0)}</div>`
                            }
                        </div>
                        <div>
                            <div class="fw-medium">${worker.name}</div>
                            <small class="text-muted">${worker.email || ''}</small>
                        </div>
                    </div>
                </td>
                <td>${worker.department}</td>
                <td>${worker.position}</td>
                <td>
                    <div class="attendance-rate">
                        <span class="badge bg-${this.getAttendanceRateBadgeClass(worker.recent_attendance_count || 0)}">
                            ${this.calculateAttendanceRate(worker.recent_attendance_count || 0)}%
                        </span>
                    </div>
                </td>
                <td>
                    <span class="badge bg-${worker.status === 'active' ? 'success' : 'secondary'}">
                        ${worker.status}
                    </span>
                </td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="editWorker(${worker.id})" title="Edit">
                            <i class="bi bi-pencil"></i>
                        </button>
                        <button class="btn btn-outline-info" onclick="viewWorkerAttendance(${worker.id})" title="View Attendance">
                            <i class="bi bi-calendar-check"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="deleteWorker(${worker.id})" title="Delete">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    }
    
    renderAlertsList() {
        const alertsList = document.getElementById('alertsList');
        if (!alertsList) return;
        
        const filteredAlerts = this.getFilteredAlerts();
        
        if (filteredAlerts.length === 0) {
            alertsList.innerHTML = '<div class="text-center py-4">No alerts found</div>';
            return;
        }
        
        alertsList.innerHTML = filteredAlerts.map(alert => `
            <div class="alert-card severity-${alert.severity}" data-alert-id="${alert.id}">
                <div class="alert-header">
                    <div class="alert-severity">
                        <span class="badge bg-${this.getSeverityBadgeClass(alert.severity)}">
                            ${alert.severity.toUpperCase()}
                        </span>
                    </div>
                    <div class="alert-actions">
                        <button class="btn btn-sm btn-outline-success" onclick="resolveAlert(${alert.id})">
                            <i class="bi bi-check-lg"></i> Resolve
                        </button>
                    </div>
                </div>
                <div class="alert-body">
                    <div class="alert-worker">
                        <strong>${alert.worker_name}</strong> (${alert.employee_id})
                    </div>
                    <div class="alert-message">${alert.alert_message}</div>
                    <div class="alert-meta">
                        <small class="text-muted">
                            <i class="bi bi-clock"></i> ${this.formatDateTime(alert.created_at)}
                        </small>
                    </div>
                </div>
            </div>
        `).join('');
    }
    
    populateSettingsForm() {
        document.getElementById('workStartTime').value = this.settings.work_start_time?.substring(0, 5) || '09:00';
        document.getElementById('workEndTime').value = this.settings.work_end_time?.substring(0, 5) || '17:00';
        document.getElementById('lateThreshold').value = this.settings.late_threshold_minutes || 15;
        document.getElementById('halfDayHours').value = this.settings.half_day_hours || 4;
        document.getElementById('overtimeThreshold').value = this.settings.overtime_threshold_hours || 8;
        document.getElementById('consecutiveAbsenceAlert').value = this.settings.consecutive_absence_alert || 3;
        document.getElementById('monthlyWorkingDays').value = this.settings.monthly_working_days || 22;
        document.getElementById('requireApproval').checked = this.settings.require_approval || false;
    }
    
    filterWorkers(searchTerm) {
        const filteredWorkers = this.workers.filter(worker => 
            worker.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            worker.employee_id.toLowerCase().includes(searchTerm.toLowerCase()) ||
            worker.department.toLowerCase().includes(searchTerm.toLowerCase()) ||
            worker.position.toLowerCase().includes(searchTerm.toLowerCase())
        );
        
        // Temporarily update workers array for rendering
        const originalWorkers = this.workers;
        this.workers = filteredWorkers;
        this.renderWorkersTable();
        this.workers = originalWorkers;
    }
    
    getFilteredAlerts() {
        const severityFilter = document.getElementById('alertSeverityFilter')?.value;
        const typeFilter = document.getElementById('alertTypeFilter')?.value;
        
        return this.alerts.filter(alert => {
            if (severityFilter && alert.severity !== severityFilter) return false;
            if (typeFilter && alert.alert_type !== typeFilter) return false;
            return true;
        });
    }
    
    filterAlerts() {
        this.renderAlertsList();
    }
    
    updateAlertsCount() {
        const alertsCount = document.getElementById('alertsCount');
        if (alertsCount) {
            const activeAlerts = this.alerts.filter(alert => !alert.is_resolved).length;
            alertsCount.textContent = activeAlerts;
            alertsCount.style.display = activeAlerts > 0 ? 'inline' : 'none';
        }
    }
    
    getAttendanceRateBadgeClass(attendanceCount) {
        const rate = this.calculateAttendanceRate(attendanceCount);
        if (rate >= 90) return 'success';
        if (rate >= 80) return 'warning';
        return 'danger';
    }
    
    calculateAttendanceRate(attendanceCount) {
        // Assuming 22 working days per month
        const workingDays = 22;
        return Math.round((attendanceCount / workingDays) * 100);
    }
    
    getSeverityBadgeClass(severity) {
        switch (severity) {
            case 'critical': return 'danger';
            case 'high': return 'warning';
            case 'medium': return 'info';
            case 'low': return 'secondary';
            default: return 'secondary';
        }
    }
    
    formatDateTime(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
    }
    
    async saveSettings() {
        try {
            const formData = {
                work_start_time: document.getElementById('workStartTime').value + ':00',
                work_end_time: document.getElementById('workEndTime').value + ':00',
                late_threshold_minutes: parseInt(document.getElementById('lateThreshold').value),
                half_day_hours: parseInt(document.getElementById('halfDayHours').value),
                overtime_threshold_hours: parseInt(document.getElementById('overtimeThreshold').value),
                consecutive_absence_alert: parseInt(document.getElementById('consecutiveAbsenceAlert').value),
                monthly_working_days: parseInt(document.getElementById('monthlyWorkingDays').value),
                require_approval: document.getElementById('requireApproval').checked
            };
            
            // Save settings via API (would need to implement settings save endpoint)
            // For now, just update local settings
            this.settings = { ...this.settings, ...formData };
            
            this.showSuccess('Settings saved successfully');
            
        } catch (error) {
            console.error('Failed to save settings:', error);
            this.showError('Failed to save settings');
        }
    }
    
    showError(message) {
        this.showNotification(message, 'danger');
    }
    
    showSuccess(message) {
        this.showNotification(message, 'success');
    }
    
    showNotification(message, type = 'info') {
        const notificationDiv = document.createElement('div');
        notificationDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        notificationDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; max-width: 400px;';
        notificationDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(notificationDiv);
        
        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (notificationDiv.parentNode) {
                notificationDiv.parentNode.removeChild(notificationDiv);
            }
        }, 5000);
    }
}

// Global functions for button onclick handlers
async function saveWorker() {
    try {
        const form = document.getElementById('addWorkerForm');
        const formData = new FormData(form);
        const data = Object.fromEntries(formData.entries());
        
        const response = await fetch('/MtcInvoiceNewProject/api/attendance/workers.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(data)
        });
        
        const result = await response.json();
        
        if (result.success) {
            // Close modal
            bootstrap.Modal.getInstance(document.getElementById('addWorkerModal')).hide();
            
            // Refresh workers list
            window.attendanceAdmin.loadWorkers().then(() => {
                window.attendanceAdmin.renderWorkersTable();
            });
            
            // Reset form
            form.reset();
            
            window.attendanceAdmin.showSuccess('Worker added successfully');
        } else {
            throw new Error(result.message || 'Failed to add worker');
        }
        
    } catch (error) {
        console.error('Failed to save worker:', error);
        window.attendanceAdmin.showError('Failed to add worker: ' + error.message);
    }
}

async function editWorker(workerId) {
    // Implementation for editing worker
    console.log('Edit worker:', workerId);
}

async function viewWorkerAttendance(workerId) {
    // Implementation for viewing worker attendance
    console.log('View worker attendance:', workerId);
}

async function deleteWorker(workerId) {
    if (!confirm('Are you sure you want to delete this worker?')) return;
    
    try {
        const response = await fetch(`/MtcInvoiceNewProject/api/attendance/workers.php/${workerId}`, {
            method: 'DELETE'
        });
        
        const result = await response.json();
        
        if (result.success) {
            window.attendanceAdmin.loadWorkers().then(() => {
                window.attendanceAdmin.renderWorkersTable();
            });
            
            window.attendanceAdmin.showSuccess('Worker deleted successfully');
        } else {
            throw new Error(result.message || 'Failed to delete worker');
        }
        
    } catch (error) {
        console.error('Failed to delete worker:', error);
        window.attendanceAdmin.showError('Failed to delete worker: ' + error.message);
    }
}

async function resolveAlert(alertId) {
    try {
        const response = await fetch(`/MtcInvoiceNewProject/api/attendance/alerts.php/${alertId}/resolve`, {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ resolved_by: 1 }) // TODO: Get from session
        });
        
        const result = await response.json();
        
        if (result.success) {
            window.attendanceAdmin.loadAlerts().then(() => {
                window.attendanceAdmin.renderAlertsList();
                window.attendanceAdmin.updateAlertsCount();
            });
            
            window.attendanceAdmin.showSuccess('Alert resolved successfully');
        } else {
            throw new Error(result.message || 'Failed to resolve alert');
        }
        
    } catch (error) {
        console.error('Failed to resolve alert:', error);
        window.attendanceAdmin.showError('Failed to resolve alert: ' + error.message);
    }
}

async function analyzeAllPatterns() {
    try {
        const response = await fetch('/MtcInvoiceNewProject/api/attendance/alerts.php/analyze', {
            method: 'POST'
        });
        
        const result = await response.json();
        
        if (result.success) {
            window.attendanceAdmin.loadAlerts().then(() => {
                window.attendanceAdmin.renderAlertsList();
                window.attendanceAdmin.updateAlertsCount();
            });
            
            window.attendanceAdmin.showSuccess(`Pattern analysis completed. ${result.data.total_new_alerts} new alerts generated.`);
        } else {
            throw new Error(result.message || 'Failed to analyze patterns');
        }
        
    } catch (error) {
        console.error('Failed to analyze patterns:', error);
        window.attendanceAdmin.showError('Failed to analyze patterns: ' + error.message);
    }
}

function refreshAlerts() {
    window.attendanceAdmin.loadAlerts().then(() => {
        window.attendanceAdmin.renderAlertsList();
        window.attendanceAdmin.updateAlertsCount();
    });
}

function generateReport(type) {
    const url = `/MtcInvoiceNewProject/api/attendance/reports.php/${type}`;
    window.open(url, '_blank');
}

function generateCustomReport() {
    const startDate = document.getElementById('reportStartDate').value;
    const endDate = document.getElementById('reportEndDate').value;
    const department = document.getElementById('reportDepartment').value;
    const format = document.getElementById('reportFormat').value;
    
    if (!startDate || !endDate) {
        window.attendanceAdmin.showError('Please select start and end dates');
        return;
    }
    
    const params = new URLSearchParams({
        start_date: startDate,
        end_date: endDate,
        format: format
    });
    
    if (department) params.append('department', department);
    
    const url = `/MtcInvoiceNewProject/api/attendance/reports.php/export?${params}`;
    window.open(url, '_blank');
}

function downloadTemplate() {
    // Create and download CSV template
    const csvContent = 'employee_id,date,status,check_in,check_out,notes\nEMP001,2024-01-15,present,09:00,17:00,Sample entry';
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'attendance_template.csv';
    a.click();
    window.URL.revokeObjectURL(url);
}

function uploadBulkAttendance() {
    const fileInput = document.getElementById('bulkAttendanceFile');
    const file = fileInput.files[0];
    
    if (!file) {
        window.attendanceAdmin.showError('Please select a CSV file');
        return;
    }
    
    // Implementation for bulk upload would go here
    window.attendanceAdmin.showSuccess('Bulk upload functionality coming soon');
}

// Initialize admin interface when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    window.attendanceAdmin = new AttendanceAdmin();
});
