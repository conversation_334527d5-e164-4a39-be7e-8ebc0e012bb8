#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 377487360 bytes. Error detail: G1 virtual space
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (os_windows.cpp:3898), pid=3444, tid=16668
#
# JRE version: OpenJDK Runtime Environment (21.0.6) (build 21.0.6+-13368085-b895.109)
# Java VM: OpenJDK 64-Bit Server VM (21.0.6+-13368085-b895.109, mixed mode, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.xml/javax.xml.namespace=ALL-UNNAMED -Xmx2048m -Dfile.encoding=UTF-8 -Duser.country=US -Duser.language=en -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.14.2-bin\2pb3mgt1p815evrl3weanttgr\gradle-8.14.2\lib\agents\gradle-instrumentation-agent-8.14.2.jar org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.14.2

Host: AMD Ryzen 5 3400G with Radeon Vega Graphics    , 8 cores, 21G,  Windows 10 , 64 bit Build 19041 (10.0.19041.5915)
Time: Tue Jun 17 15:53:29 2025 Bangladesh Standard Time elapsed time: 76.164252 seconds (0d 0h 1m 16s)

---------------  T H R E A D  ---------------

Current thread (0x00000279dd5dfcc0):  VMThread "VM Thread"          [id=16668, stack(0x0000008788c00000,0x0000008788d00000) (1024K)]

Stack: [0x0000008788c00000,0x0000008788d00000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6d0639]
V  [jvm.dll+0x85eb03]
V  [jvm.dll+0x86105e]
V  [jvm.dll+0x861743]
V  [jvm.dll+0x27e6e6]
V  [jvm.dll+0x6ccfd5]
V  [jvm.dll+0x6c0a8a]
V  [jvm.dll+0x35537b]
V  [jvm.dll+0x35cfd6]
V  [jvm.dll+0x3aef86]
V  [jvm.dll+0x3af258]
V  [jvm.dll+0x327a2c]
V  [jvm.dll+0x327ab7]
V  [jvm.dll+0x36d93c]
V  [jvm.dll+0x36c0ee]
V  [jvm.dll+0x327181]
V  [jvm.dll+0x36b7f0]
V  [jvm.dll+0x8674c8]
V  [jvm.dll+0x868844]
V  [jvm.dll+0x868d80]
V  [jvm.dll+0x869013]
V  [jvm.dll+0x806ed8]
V  [jvm.dll+0x6cef0d]
C  [ucrtbase.dll+0x21bb2]
C  [KERNEL32.DLL+0x17374]
C  [ntdll.dll+0x4cc91]

VM_Operation (0x000000878c0fe4a0): G1CollectForAllocation, mode: safepoint, requested by thread 0x00000279f21b5990


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x00000279e56d4a80, length=156, elements={
0x00000279c5b6e160, 0x00000279dd906320, 0x00000279dd9075c0, 0x00000279dd908a40,
0x00000279dd90b160, 0x00000279dd923eb0, 0x00000279dd9284e0, 0x00000279dd9345c0,
0x00000279dd93d950, 0x00000279ddd5f220, 0x00000279ddd5f8f0, 0x00000279ddd60690,
0x00000279e406a750, 0x00000279e4068540, 0x00000279e4067e70, 0x00000279e406bbc0,
0x00000279ddd5ffc0, 0x00000279ddd5d6e0, 0x00000279e406ae20, 0x00000279e406e4a0,
0x00000279e406c960, 0x00000279ddd5ddb0, 0x00000279ddd5eb50, 0x00000279e406eb70,
0x00000279e406c290, 0x00000279e59cfe60, 0x00000279e59cc110, 0x00000279e7d3eb30,
0x00000279e7d45f00, 0x00000279e7d41410, 0x00000279e59c9f00, 0x00000279ea0f9680,
0x00000279f0b5ed30, 0x00000279f0b58030, 0x00000279f0b58dd0, 0x00000279f0b5cb20,
0x00000279f0b594a0, 0x00000279f0b5b6b0, 0x00000279f0b5f400, 0x00000279f0b5fad0,
0x00000279f0b58700, 0x00000279f0b601a0, 0x00000279f0b59b70, 0x00000279f0b5c450,
0x00000279f0b5d1f0, 0x00000279f0b5a240, 0x00000279f0b5bd80, 0x00000279f0b60870,
0x00000279f0b5d8c0, 0x00000279f0b61ce0, 0x00000279f0b5df90, 0x00000279ea0fd3d0,
0x00000279ea0fe170, 0x00000279e87a8a20, 0x00000279e87ae2b0, 0x00000279e87aac30,
0x00000279e87ace40, 0x00000279e87a90f0, 0x00000279e87aa560, 0x00000279e9c82bb0,
0x00000279e9c824e0, 0x00000279e4d42b90, 0x00000279e4d41df0, 0x00000279e4d44000,
0x00000279e4d41050, 0x00000279e4d43260, 0x00000279e4d402b0, 0x00000279e4d424c0,
0x00000279e4d41720, 0x00000279e4d45b40, 0x00000279e7d42880, 0x00000279e7d3f200,
0x00000279e7d40d40, 0x00000279e7d41ae0, 0x00000279e7d421b0, 0x00000279e7d42f50,
0x00000279e7d43620, 0x00000279e7d44a90, 0x00000279e7d43cf0, 0x00000279e8ddb4e0,
0x00000279e8ddc950, 0x00000279e8ddbbb0, 0x00000279e8ddd020, 0x00000279e8ddddc0,
0x00000279e8dde490, 0x00000279e8de0d70, 0x00000279e59cf0c0, 0x00000279e87b17d0,
0x00000279e87b7e00, 0x00000279e4d44da0, 0x00000279e4d40980, 0x00000279e4d468e0,
0x00000279e4d45470, 0x00000279e4d46210, 0x00000279e4d3f510, 0x00000279e406ddd0,
0x00000279e406a080, 0x00000279e4068c10, 0x00000279e40692e0, 0x00000279e406d700,
0x00000279e8de06a0, 0x00000279e8ddeb60, 0x00000279e8ddffd0, 0x00000279e8ddae10,
0x00000279e8dda070, 0x00000279e7d443c0, 0x00000279e7d45160, 0x00000279e7d3f8d0,
0x00000279e7d45830, 0x00000279ddd60d60, 0x00000279e59cdc50, 0x00000279eb8a15c0,
0x00000279eb89df40, 0x00000279eb89ece0, 0x00000279eb89c400, 0x00000279eb89f3b0,
0x00000279eb89cad0, 0x00000279eb89fa80, 0x00000279eb89bd30, 0x00000279eb8a1c90,
0x00000279eb8a0ef0, 0x00000279eb8a2360, 0x00000279eb89af90, 0x00000279eb89e610,
0x00000279eb8a6780, 0x00000279eb8a37d0, 0x00000279eb8a3ea0, 0x00000279eb8a60b0,
0x00000279eb8a6e50, 0x00000279eb8a4570, 0x00000279eb8a5310, 0x00000279eb8a9060,
0x00000279eb8a7bf0, 0x00000279eb8a8990, 0x00000279eb8a9e00, 0x00000279eb8a3100,
0x00000279eb8a7520, 0x00000279eb8a59e0, 0x00000279eb8a9730, 0x00000279eb8a82c0,
0x00000279e9c7f530, 0x00000279ea0f44c0, 0x00000279ea0f4b90, 0x00000279e87afdf0,
0x00000279e87ab300, 0x00000279e87ac770, 0x00000279e732efa0, 0x00000279e732fdc0,
0x00000279f21b5990, 0x00000279f21b52c0, 0x00000279f21b9db0, 0x00000279f21b6e00,
0x00000279f21bd430, 0x00000279f21bc690, 0x00000279f21bcd60, 0x00000279f21ba480
}

Java Threads: ( => current thread )
  0x00000279c5b6e160 JavaThread "main"                              [_thread_blocked, id=17320, stack(0x0000008788500000,0x0000008788600000) (1024K)]
  0x00000279dd906320 JavaThread "Reference Handler"          daemon [_thread_blocked, id=7360, stack(0x0000008788d00000,0x0000008788e00000) (1024K)]
  0x00000279dd9075c0 JavaThread "Finalizer"                  daemon [_thread_blocked, id=15568, stack(0x0000008788e00000,0x0000008788f00000) (1024K)]
  0x00000279dd908a40 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=9936, stack(0x0000008788f00000,0x0000008789000000) (1024K)]
  0x00000279dd90b160 JavaThread "Attach Listener"            daemon [_thread_blocked, id=8772, stack(0x0000008789000000,0x0000008789100000) (1024K)]
  0x00000279dd923eb0 JavaThread "Service Thread"             daemon [_thread_blocked, id=5284, stack(0x0000008789100000,0x0000008789200000) (1024K)]
  0x00000279dd9284e0 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=1164, stack(0x0000008789200000,0x0000008789300000) (1024K)]
  0x00000279dd9345c0 JavaThread "C2 CompilerThread0"         daemon [_thread_blocked, id=8540, stack(0x0000008789300000,0x0000008789400000) (1024K)]
  0x00000279dd93d950 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=18776, stack(0x0000008789400000,0x0000008789500000) (1024K)]
  0x00000279ddd5f220 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=17680, stack(0x0000008789500000,0x0000008789600000) (1024K)]
  0x00000279ddd5f8f0 JavaThread "Notification Thread"        daemon [_thread_blocked, id=15176, stack(0x0000008789600000,0x0000008789700000) (1024K)]
  0x00000279ddd60690 JavaThread "Daemon health stats"               [_thread_blocked, id=18068, stack(0x0000008789800000,0x0000008789900000) (1024K)]
  0x00000279e406a750 JavaThread "Incoming local TCP Connector on port 51873"        [_thread_in_native, id=18052, stack(0x000000878a000000,0x000000878a100000) (1024K)]
  0x00000279e4068540 JavaThread "Daemon periodic checks"            [_thread_blocked, id=2944, stack(0x000000878a100000,0x000000878a200000) (1024K)]
  0x00000279e4067e70 JavaThread "Daemon"                            [_thread_blocked, id=17688, stack(0x000000878a200000,0x000000878a300000) (1024K)]
  0x00000279e406bbc0 JavaThread "Daemon worker"                     [_thread_blocked, id=2372, stack(0x000000878a500000,0x000000878a600000) (1024K)]
  0x00000279ddd5ffc0 JavaThread "Cache worker for journal cache (C:\Users\<USER>\.gradle\caches\journal-1)"        [_thread_blocked, id=18072, stack(0x000000878aa00000,0x000000878ab00000) (1024K)]
  0x00000279ddd5d6e0 JavaThread "File lock request listener"        [_thread_in_native, id=11360, stack(0x000000878ab00000,0x000000878ac00000) (1024K)]
  0x00000279e406ae20 JavaThread "Cache worker for file hash cache (C:\Users\<USER>\.gradle\caches\8.14.2\fileHashes)"        [_thread_blocked, id=16624, stack(0x000000878ac00000,0x000000878ad00000) (1024K)]
  0x00000279e406e4a0 JavaThread "File watcher server"        daemon [_thread_blocked, id=11024, stack(0x000000878b000000,0x000000878b100000) (1024K)]
  0x00000279e406c960 JavaThread "File watcher consumer"      daemon [_thread_blocked, id=4352, stack(0x000000878b100000,0x000000878b200000) (1024K)]
  0x00000279ddd5ddb0 JavaThread "jar transforms"                    [_thread_blocked, id=13996, stack(0x000000878b200000,0x000000878b300000) (1024K)]
  0x00000279ddd5eb50 JavaThread "jar transforms Thread 2"           [_thread_blocked, id=11516, stack(0x000000878b300000,0x000000878b400000) (1024K)]
  0x00000279e406eb70 JavaThread "jar transforms Thread 3"           [_thread_blocked, id=16196, stack(0x000000878b400000,0x000000878b500000) (1024K)]
  0x00000279e406c290 JavaThread "jar transforms Thread 4"           [_thread_blocked, id=18676, stack(0x000000878b500000,0x000000878b600000) (1024K)]
  0x00000279e59cfe60 JavaThread "jar transforms Thread 5"           [_thread_blocked, id=18924, stack(0x000000878b600000,0x000000878b700000) (1024K)]
  0x00000279e59cc110 JavaThread "jar transforms Thread 6"           [_thread_blocked, id=16680, stack(0x000000878b700000,0x000000878b800000) (1024K)]
  0x00000279e7d3eb30 JavaThread "jar transforms Thread 7"           [_thread_blocked, id=14932, stack(0x000000878b800000,0x000000878b900000) (1024K)]
  0x00000279e7d45f00 JavaThread "jar transforms Thread 8"           [_thread_blocked, id=18240, stack(0x000000878b900000,0x000000878ba00000) (1024K)]
  0x00000279e7d41410 JavaThread "Cache worker for file content cache (C:\Users\<USER>\.gradle\caches\8.14.2\fileContent)"        [_thread_blocked, id=15112, stack(0x000000878bb00000,0x000000878bc00000) (1024K)]
  0x00000279e59c9f00 JavaThread "Memory manager"                    [_thread_blocked, id=9620, stack(0x000000878ad00000,0x000000878ae00000) (1024K)]
  0x00000279ea0f9680 JavaThread "pool-3-thread-1"                   [_thread_blocked, id=8892, stack(0x000000878e700000,0x000000878e800000) (1024K)]
  0x00000279f0b5ed30 JavaThread "VFS cleanup"                       [_thread_blocked, id=15908, stack(0x000000878a600000,0x000000878a700000) (1024K)]
  0x00000279f0b58030 JavaThread "Handler for socket connection from /127.0.0.1:51873 to /127.0.0.1:51952"        [_thread_in_native, id=19300, stack(0x0000008788200000,0x0000008788300000) (1024K)]
  0x00000279f0b58dd0 JavaThread "Cancel handler"                    [_thread_blocked, id=6068, stack(0x0000008788300000,0x0000008788400000) (1024K)]
  0x00000279f0b5cb20 JavaThread "Asynchronous log dispatcher for DefaultDaemonConnection: socket connection from /127.0.0.1:51873 to /127.0.0.1:51952"        [_thread_blocked, id=13200, stack(0x0000008788400000,0x0000008788500000) (1024K)]
  0x00000279f0b594a0 JavaThread "Stdin handler"                     [_thread_blocked, id=17504, stack(0x000000878a700000,0x000000878a800000) (1024K)]
  0x00000279f0b5b6b0 JavaThread "Daemon client event forwarder"        [_thread_blocked, id=5076, stack(0x000000878a800000,0x000000878a900000) (1024K)]
  0x00000279f0b5f400 JavaThread "Cache worker for file hash cache (C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\.gradle\8.14.2\fileHashes)"        [_thread_blocked, id=18900, stack(0x000000878ae00000,0x000000878af00000) (1024K)]
  0x00000279f0b5fad0 JavaThread "Cache worker for Build Output Cleanup Cache (C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\.gradle\buildOutputCleanup)"        [_thread_blocked, id=5884, stack(0x000000878af00000,0x000000878b000000) (1024K)]
  0x00000279f0b58700 JavaThread "Cache worker for checksums cache (C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\.gradle\8.14.2\checksums)"        [_thread_blocked, id=4316, stack(0x000000878ba00000,0x000000878bb00000) (1024K)]
  0x00000279f0b601a0 JavaThread "Cache worker for cache directory md-supplier (C:\Users\<USER>\.gradle\caches\8.14.2\md-supplier)"        [_thread_blocked, id=1768, stack(0x000000878bc00000,0x000000878bd00000) (1024K)]
  0x00000279f0b59b70 JavaThread "Cache worker for cache directory md-rule (C:\Users\<USER>\.gradle\caches\8.14.2\md-rule)"        [_thread_blocked, id=8120, stack(0x000000878bd00000,0x000000878be00000) (1024K)]
  0x00000279f0b5c450 JavaThread "Problems report writer"            [_thread_blocked, id=16356, stack(0x000000878bf00000,0x000000878c000000) (1024K)]
  0x00000279f0b5d1f0 JavaThread "Unconstrained build operations"        [_thread_blocked, id=12976, stack(0x000000878c100000,0x000000878c200000) (1024K)]
  0x00000279f0b5a240 JavaThread "Unconstrained build operations Thread 2"        [_thread_blocked, id=1472, stack(0x000000878c200000,0x000000878c300000) (1024K)]
  0x00000279f0b5bd80 JavaThread "Unconstrained build operations Thread 3"        [_thread_blocked, id=6484, stack(0x000000878c300000,0x000000878c400000) (1024K)]
  0x00000279f0b60870 JavaThread "Unconstrained build operations Thread 4"        [_thread_blocked, id=16724, stack(0x000000878c400000,0x000000878c500000) (1024K)]
  0x00000279f0b5d8c0 JavaThread "Unconstrained build operations Thread 5"        [_thread_blocked, id=16504, stack(0x000000878c500000,0x000000878c600000) (1024K)]
  0x00000279f0b61ce0 JavaThread "Unconstrained build operations Thread 6"        [_thread_blocked, id=6804, stack(0x000000878c600000,0x000000878c700000) (1024K)]
  0x00000279f0b5df90 JavaThread "Unconstrained build operations Thread 7"        [_thread_blocked, id=7592, stack(0x000000878c700000,0x000000878c800000) (1024K)]
  0x00000279ea0fd3d0 JavaThread "Unconstrained build operations Thread 8"        [_thread_blocked, id=4328, stack(0x000000878c800000,0x000000878c900000) (1024K)]
  0x00000279ea0fe170 JavaThread "Unconstrained build operations Thread 9"        [_thread_blocked, id=10772, stack(0x000000878c900000,0x000000878ca00000) (1024K)]
  0x00000279e87a8a20 JavaThread "Unconstrained build operations Thread 10"        [_thread_blocked, id=14228, stack(0x000000878ca00000,0x000000878cb00000) (1024K)]
  0x00000279e87ae2b0 JavaThread "Unconstrained build operations Thread 11"        [_thread_blocked, id=10960, stack(0x000000878cb00000,0x000000878cc00000) (1024K)]
  0x00000279e87aac30 JavaThread "Unconstrained build operations Thread 12"        [_thread_blocked, id=15628, stack(0x000000878cc00000,0x000000878cd00000) (1024K)]
  0x00000279e87ace40 JavaThread "Unconstrained build operations Thread 13"        [_thread_blocked, id=16720, stack(0x000000878cd00000,0x000000878ce00000) (1024K)]
  0x00000279e87a90f0 JavaThread "Unconstrained build operations Thread 14"        [_thread_blocked, id=9016, stack(0x000000878ce00000,0x000000878cf00000) (1024K)]
  0x00000279e87aa560 JavaThread "build event listener"              [_thread_blocked, id=13056, stack(0x000000878cf00000,0x000000878d000000) (1024K)]
  0x00000279e9c82bb0 JavaThread "pool-6-thread-1"                   [_thread_blocked, id=1060, stack(0x000000878d000000,0x000000878d100000) (1024K)]
  0x00000279e9c824e0 JavaThread "build event listener"              [_thread_blocked, id=14204, stack(0x000000878d100000,0x000000878d200000) (1024K)]
  0x00000279e4d42b90 JavaThread "included builds"                   [_thread_blocked, id=6504, stack(0x000000878d200000,0x000000878d300000) (1024K)]
  0x00000279e4d41df0 JavaThread "Execution worker"                  [_thread_blocked, id=7256, stack(0x000000878d300000,0x000000878d400000) (1024K)]
  0x00000279e4d44000 JavaThread "Execution worker Thread 2"         [_thread_blocked, id=10312, stack(0x000000878d400000,0x000000878d500000) (1024K)]
  0x00000279e4d41050 JavaThread "Execution worker Thread 3"         [_thread_blocked, id=11612, stack(0x000000878d500000,0x000000878d600000) (1024K)]
  0x00000279e4d43260 JavaThread "Execution worker Thread 4"         [_thread_blocked, id=8416, stack(0x000000878d600000,0x000000878d700000) (1024K)]
  0x00000279e4d402b0 JavaThread "Execution worker Thread 5"         [_thread_blocked, id=15184, stack(0x000000878d700000,0x000000878d800000) (1024K)]
  0x00000279e4d424c0 JavaThread "Execution worker Thread 6"         [_thread_blocked, id=17568, stack(0x000000878d800000,0x000000878d900000) (1024K)]
  0x00000279e4d41720 JavaThread "Execution worker Thread 7"         [_thread_blocked, id=4796, stack(0x000000878d900000,0x000000878da00000) (1024K)]
  0x00000279e4d45b40 JavaThread "Cache worker for execution history cache (C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\.gradle\8.14.2\executionHistory)"        [_thread_blocked, id=10120, stack(0x000000878da00000,0x000000878db00000) (1024K)]
  0x00000279e7d42880 JavaThread "Unconstrained build operations Thread 15"        [_thread_blocked, id=13940, stack(0x000000878db00000,0x000000878dc00000) (1024K)]
  0x00000279e7d3f200 JavaThread "Unconstrained build operations Thread 16"        [_thread_blocked, id=12668, stack(0x000000878dc00000,0x000000878dd00000) (1024K)]
  0x00000279e7d40d40 JavaThread "Unconstrained build operations Thread 17"        [_thread_blocked, id=10700, stack(0x000000878dd00000,0x000000878de00000) (1024K)]
  0x00000279e7d41ae0 JavaThread "Unconstrained build operations Thread 18"        [_thread_blocked, id=2136, stack(0x000000878de00000,0x000000878df00000) (1024K)]
  0x00000279e7d421b0 JavaThread "Unconstrained build operations Thread 19"        [_thread_blocked, id=8092, stack(0x000000878df00000,0x000000878e000000) (1024K)]
  0x00000279e7d42f50 JavaThread "Unconstrained build operations Thread 20"        [_thread_blocked, id=7740, stack(0x000000878e000000,0x000000878e100000) (1024K)]
  0x00000279e7d43620 JavaThread "Unconstrained build operations Thread 21"        [_thread_blocked, id=16376, stack(0x000000878e100000,0x000000878e200000) (1024K)]
  0x00000279e7d44a90 JavaThread "Unconstrained build operations Thread 22"        [_thread_blocked, id=13240, stack(0x000000878e200000,0x000000878e300000) (1024K)]
  0x00000279e7d43cf0 JavaThread "Unconstrained build operations Thread 23"        [_thread_blocked, id=17116, stack(0x000000878e300000,0x000000878e400000) (1024K)]
  0x00000279e8ddb4e0 JavaThread "Unconstrained build operations Thread 24"        [_thread_blocked, id=8128, stack(0x000000878e400000,0x000000878e500000) (1024K)]
  0x00000279e8ddc950 JavaThread "Unconstrained build operations Thread 25"        [_thread_blocked, id=18056, stack(0x000000878e500000,0x000000878e600000) (1024K)]
  0x00000279e8ddbbb0 JavaThread "Unconstrained build operations Thread 26"        [_thread_blocked, id=7448, stack(0x000000878e600000,0x000000878e700000) (1024K)]
  0x00000279e8ddd020 JavaThread "Unconstrained build operations Thread 27"        [_thread_blocked, id=11148, stack(0x000000878e800000,0x000000878e900000) (1024K)]
  0x00000279e8ddddc0 JavaThread "Unconstrained build operations Thread 28"        [_thread_blocked, id=14148, stack(0x000000878e900000,0x000000878ea00000) (1024K)]
  0x00000279e8dde490 JavaThread "Unconstrained build operations Thread 29"        [_thread_blocked, id=12844, stack(0x000000878ea00000,0x000000878eb00000) (1024K)]
  0x00000279e8de0d70 JavaThread "Unconstrained build operations Thread 30"        [_thread_blocked, id=13672, stack(0x000000878eb00000,0x000000878ec00000) (1024K)]
  0x00000279e59cf0c0 JavaThread "WorkerExecutor Queue"              [_thread_blocked, id=11736, stack(0x000000878ec00000,0x000000878ed00000) (1024K)]
  0x00000279e87b17d0 JavaThread "Unconstrained build operations Thread 31"        [_thread_blocked, id=5032, stack(0x000000878ee00000,0x000000878ef00000) (1024K)]
  0x00000279e87b7e00 JavaThread "Unconstrained build operations Thread 32"        [_thread_blocked, id=15412, stack(0x000000878ef00000,0x000000878f000000) (1024K)]
  0x00000279e4d44da0 JavaThread "Unconstrained build operations Thread 33"        [_thread_blocked, id=12864, stack(0x000000878f000000,0x000000878f100000) (1024K)]
  0x00000279e4d40980 JavaThread "WorkerExecutor Queue Thread 2"        [_thread_blocked, id=18588, stack(0x000000878f100000,0x000000878f200000) (1024K)]
  0x00000279e4d468e0 JavaThread "Unconstrained build operations Thread 34"        [_thread_blocked, id=17740, stack(0x000000878f200000,0x000000878f300000) (1024K)]
  0x00000279e4d45470 JavaThread "Unconstrained build operations Thread 35"        [_thread_blocked, id=9592, stack(0x000000878f300000,0x000000878f400000) (1024K)]
  0x00000279e4d46210 JavaThread "Unconstrained build operations Thread 36"        [_thread_blocked, id=7320, stack(0x000000878f400000,0x000000878f500000) (1024K)]
  0x00000279e4d3f510 JavaThread "Unconstrained build operations Thread 37"        [_thread_blocked, id=19304, stack(0x000000878f500000,0x000000878f600000) (1024K)]
  0x00000279e406ddd0 JavaThread "Unconstrained build operations Thread 38"        [_thread_blocked, id=19340, stack(0x000000878f600000,0x000000878f700000) (1024K)]
  0x00000279e406a080 JavaThread "Unconstrained build operations Thread 39"        [_thread_blocked, id=11568, stack(0x000000878f700000,0x000000878f800000) (1024K)]
  0x00000279e4068c10 JavaThread "Unconstrained build operations Thread 40"        [_thread_blocked, id=15372, stack(0x000000878f800000,0x000000878f900000) (1024K)]
  0x00000279e40692e0 JavaThread "Unconstrained build operations Thread 41"        [_thread_blocked, id=13352, stack(0x000000878f900000,0x000000878fa00000) (1024K)]
  0x00000279e406d700 JavaThread "Unconstrained build operations Thread 42"        [_thread_blocked, id=10160, stack(0x000000878fa00000,0x000000878fb00000) (1024K)]
  0x00000279e8de06a0 JavaThread "Unconstrained build operations Thread 43"        [_thread_blocked, id=7472, stack(0x000000878fb00000,0x000000878fc00000) (1024K)]
  0x00000279e8ddeb60 JavaThread "Unconstrained build operations Thread 44"        [_thread_blocked, id=11080, stack(0x000000878fc00000,0x000000878fd00000) (1024K)]
  0x00000279e8ddffd0 JavaThread "Unconstrained build operations Thread 45"        [_thread_blocked, id=4572, stack(0x000000878fd00000,0x000000878fe00000) (1024K)]
  0x00000279e8ddae10 JavaThread "Unconstrained build operations Thread 46"        [_thread_blocked, id=18376, stack(0x000000878fe00000,0x000000878ff00000) (1024K)]
  0x00000279e8dda070 JavaThread "Unconstrained build operations Thread 47"        [_thread_blocked, id=6720, stack(0x000000878ff00000,0x0000008790000000) (1024K)]
  0x00000279e7d443c0 JavaThread "Unconstrained build operations Thread 48"        [_thread_blocked, id=4980, stack(0x0000008790000000,0x0000008790100000) (1024K)]
  0x00000279e7d45160 JavaThread "Unconstrained build operations Thread 49"        [_thread_blocked, id=12488, stack(0x0000008790100000,0x0000008790200000) (1024K)]
  0x00000279e7d3f8d0 JavaThread "Unconstrained build operations Thread 50"        [_thread_blocked, id=15228, stack(0x0000008790200000,0x0000008790300000) (1024K)]
  0x00000279e7d45830 JavaThread "Unconstrained build operations Thread 51"        [_thread_blocked, id=10900, stack(0x0000008790300000,0x0000008790400000) (1024K)]
  0x00000279ddd60d60 JavaThread "Unconstrained build operations Thread 52"        [_thread_blocked, id=5792, stack(0x0000008790400000,0x0000008790500000) (1024K)]
  0x00000279e59cdc50 JavaThread "Unconstrained build operations Thread 53"        [_thread_blocked, id=18620, stack(0x0000008790500000,0x0000008790600000) (1024K)]
  0x00000279eb8a15c0 JavaThread "Unconstrained build operations Thread 54"        [_thread_blocked, id=6796, stack(0x0000008790600000,0x0000008790700000) (1024K)]
  0x00000279eb89df40 JavaThread "Unconstrained build operations Thread 55"        [_thread_blocked, id=11504, stack(0x0000008790700000,0x0000008790800000) (1024K)]
  0x00000279eb89ece0 JavaThread "Unconstrained build operations Thread 56"        [_thread_blocked, id=2148, stack(0x0000008790800000,0x0000008790900000) (1024K)]
  0x00000279eb89c400 JavaThread "Unconstrained build operations Thread 57"        [_thread_blocked, id=2228, stack(0x0000008790900000,0x0000008790a00000) (1024K)]
  0x00000279eb89f3b0 JavaThread "Unconstrained build operations Thread 58"        [_thread_blocked, id=11588, stack(0x0000008790a00000,0x0000008790b00000) (1024K)]
  0x00000279eb89cad0 JavaThread "Unconstrained build operations Thread 59"        [_thread_blocked, id=15080, stack(0x0000008790b00000,0x0000008790c00000) (1024K)]
  0x00000279eb89fa80 JavaThread "Unconstrained build operations Thread 60"        [_thread_blocked, id=4748, stack(0x0000008790c00000,0x0000008790d00000) (1024K)]
  0x00000279eb89bd30 JavaThread "Unconstrained build operations Thread 61"        [_thread_blocked, id=10156, stack(0x0000008790d00000,0x0000008790e00000) (1024K)]
  0x00000279eb8a1c90 JavaThread "WorkerExecutor Queue Thread 3"        [_thread_blocked, id=1204, stack(0x0000008790e00000,0x0000008790f00000) (1024K)]
  0x00000279eb8a0ef0 JavaThread "Unconstrained build operations Thread 62"        [_thread_blocked, id=16352, stack(0x0000008790f00000,0x0000008791000000) (1024K)]
  0x00000279eb8a2360 JavaThread "Unconstrained build operations Thread 63"        [_thread_blocked, id=10668, stack(0x0000008791000000,0x0000008791100000) (1024K)]
  0x00000279eb89af90 JavaThread "Unconstrained build operations Thread 64"        [_thread_blocked, id=17372, stack(0x0000008791100000,0x0000008791200000) (1024K)]
  0x00000279eb89e610 JavaThread "Unconstrained build operations Thread 65"        [_thread_blocked, id=7116, stack(0x0000008791200000,0x0000008791300000) (1024K)]
  0x00000279eb8a6780 JavaThread "Unconstrained build operations Thread 66"        [_thread_blocked, id=1960, stack(0x0000008791300000,0x0000008791400000) (1024K)]
  0x00000279eb8a37d0 JavaThread "Unconstrained build operations Thread 67"        [_thread_blocked, id=14976, stack(0x0000008791400000,0x0000008791500000) (1024K)]
  0x00000279eb8a3ea0 JavaThread "Unconstrained build operations Thread 68"        [_thread_blocked, id=7232, stack(0x0000008791500000,0x0000008791600000) (1024K)]
  0x00000279eb8a60b0 JavaThread "Unconstrained build operations Thread 69"        [_thread_blocked, id=14784, stack(0x0000008791600000,0x0000008791700000) (1024K)]
  0x00000279eb8a6e50 JavaThread "Unconstrained build operations Thread 70"        [_thread_blocked, id=16012, stack(0x0000008791700000,0x0000008791800000) (1024K)]
  0x00000279eb8a4570 JavaThread "Unconstrained build operations Thread 71"        [_thread_blocked, id=17064, stack(0x0000008791800000,0x0000008791900000) (1024K)]
  0x00000279eb8a5310 JavaThread "Unconstrained build operations Thread 72"        [_thread_blocked, id=14628, stack(0x0000008791900000,0x0000008791a00000) (1024K)]
  0x00000279eb8a9060 JavaThread "Unconstrained build operations Thread 73"        [_thread_blocked, id=2032, stack(0x0000008791a00000,0x0000008791b00000) (1024K)]
  0x00000279eb8a7bf0 JavaThread "Unconstrained build operations Thread 74"        [_thread_blocked, id=4916, stack(0x0000008791c00000,0x0000008791d00000) (1024K)]
  0x00000279eb8a8990 JavaThread "Unconstrained build operations Thread 75"        [_thread_blocked, id=18444, stack(0x0000008791d00000,0x0000008791e00000) (1024K)]
  0x00000279eb8a9e00 JavaThread "Unconstrained build operations Thread 76"        [_thread_blocked, id=3272, stack(0x0000008791e00000,0x0000008791f00000) (1024K)]
  0x00000279eb8a3100 JavaThread "WorkerExecutor Queue Thread 4"        [_thread_blocked, id=13272, stack(0x0000008791f00000,0x0000008792000000) (1024K)]
  0x00000279eb8a7520 JavaThread "Unconstrained build operations Thread 77"        [_thread_blocked, id=15140, stack(0x0000008792000000,0x0000008792100000) (1024K)]
  0x00000279eb8a59e0 JavaThread "Unconstrained build operations Thread 78"        [_thread_blocked, id=17288, stack(0x0000008792100000,0x0000008792200000) (1024K)]
  0x00000279eb8a9730 JavaThread "Unconstrained build operations Thread 79"        [_thread_blocked, id=4088, stack(0x0000008792200000,0x0000008792300000) (1024K)]
  0x00000279eb8a82c0 JavaThread "Unconstrained build operations Thread 80"        [_thread_blocked, id=19264, stack(0x0000008792300000,0x0000008792400000) (1024K)]
  0x00000279e9c7f530 JavaThread "pool-7-thread-1"                   [_thread_blocked, id=19040, stack(0x0000008792400000,0x0000008792500000) (1024K)]
  0x00000279ea0f44c0 JavaThread "stderr"                            [_thread_in_native, id=9464, stack(0x0000008792500000,0x0000008792600000) (1024K)]
  0x00000279ea0f4b90 JavaThread "stdout"                            [_thread_in_native, id=11036, stack(0x0000008792600000,0x0000008792700000) (1024K)]
  0x00000279e87afdf0 JavaThread "WorkerExecutor Queue Thread 5"        [_thread_blocked, id=14124, stack(0x0000008792700000,0x0000008792800000) (1024K)]
  0x00000279e87ab300 JavaThread "WorkerExecutor Queue Thread 6"        [_thread_blocked, id=17732, stack(0x0000008792800000,0x0000008792900000) (1024K)]
  0x00000279e87ac770 JavaThread "WorkerExecutor Queue Thread 7"        [_thread_blocked, id=1740, stack(0x0000008792900000,0x0000008792a00000) (1024K)]
  0x00000279e732efa0 JavaThread "C2 CompilerThread1"         daemon [_thread_blocked, id=4280, stack(0x0000008789700000,0x0000008789800000) (1024K)]
  0x00000279e732fdc0 JavaThread "C2 CompilerThread2"         daemon [_thread_blocked, id=11272, stack(0x000000878be00000,0x000000878bf00000) (1024K)]
  0x00000279f21b5990 JavaThread "Cache worker for Java compile cache (C:\Users\<USER>\.gradle\caches\8.14.2\javaCompile)"        [_thread_blocked, id=8448, stack(0x000000878c000000,0x000000878c100000) (1024K)]
  0x00000279f21b52c0 JavaThread "Build operations"                  [_thread_blocked, id=5352, stack(0x0000008792a00000,0x0000008792b00000) (1024K)]
  0x00000279f21b9db0 JavaThread "Build operations Thread 2"         [_thread_blocked, id=15088, stack(0x0000008792b00000,0x0000008792c00000) (1024K)]
  0x00000279f21b6e00 JavaThread "Build operations Thread 3"         [_thread_blocked, id=14884, stack(0x0000008792c00000,0x0000008792d00000) (1024K)]
  0x00000279f21bd430 JavaThread "Build operations Thread 4"         [_thread_blocked, id=12884, stack(0x0000008792d00000,0x0000008792e00000) (1024K)]
  0x00000279f21bc690 JavaThread "Build operations Thread 5"         [_thread_blocked, id=19272, stack(0x0000008792e00000,0x0000008792f00000) (1024K)]
  0x00000279f21bcd60 JavaThread "Build operations Thread 6"         [_thread_blocked, id=13216, stack(0x0000008792f00000,0x0000008793000000) (1024K)]
  0x00000279f21ba480 JavaThread "Build operations Thread 7"         [_thread_blocked, id=10624, stack(0x0000008793000000,0x0000008793100000) (1024K)]
Total: 156

Other Threads:
=>0x00000279dd5dfcc0 VMThread "VM Thread"                           [id=16668, stack(0x0000008788c00000,0x0000008788d00000) (1024K)]
  0x00000279dd32b480 WatcherThread "VM Periodic Task Thread"        [id=4152, stack(0x0000008788b00000,0x0000008788c00000) (1024K)]
  0x00000279c5bae4d0 WorkerThread "GC Thread#0"                     [id=3212, stack(0x0000008788600000,0x0000008788700000) (1024K)]
  0x00000279e273e4e0 WorkerThread "GC Thread#1"                     [id=2628, stack(0x0000008789900000,0x0000008789a00000) (1024K)]
  0x00000279ddb3f4a0 WorkerThread "GC Thread#2"                     [id=14060, stack(0x0000008789a00000,0x0000008789b00000) (1024K)]
  0x00000279e2973d30 WorkerThread "GC Thread#3"                     [id=5500, stack(0x0000008789b00000,0x0000008789c00000) (1024K)]
  0x00000279e2974110 WorkerThread "GC Thread#4"                     [id=13300, stack(0x0000008789c00000,0x0000008789d00000) (1024K)]
  0x00000279e29744f0 WorkerThread "GC Thread#5"                     [id=8296, stack(0x0000008789d00000,0x0000008789e00000) (1024K)]
  0x00000279dd923290 WorkerThread "GC Thread#6"                     [id=5548, stack(0x0000008789e00000,0x0000008789f00000) (1024K)]
  0x00000279e267dff0 WorkerThread "GC Thread#7"                     [id=3004, stack(0x0000008789f00000,0x000000878a000000) (1024K)]
  0x00000279da7a97f0 ConcurrentGCThread "G1 Main Marker"            [id=10128, stack(0x0000008788700000,0x0000008788800000) (1024K)]
  0x00000279da7aa7f0 WorkerThread "G1 Conc#0"                       [id=5988, stack(0x0000008788800000,0x0000008788900000) (1024K)]
  0x00000279e2383a30 WorkerThread "G1 Conc#1"                       [id=5984, stack(0x000000878a900000,0x000000878aa00000) (1024K)]
  0x00000279da96e260 ConcurrentGCThread "G1 Refine#0"               [id=19240, stack(0x0000008788900000,0x0000008788a00000) (1024K)]
  0x00000279dd1fa0a0 ConcurrentGCThread "G1 Service"                [id=8, stack(0x0000008788a00000,0x0000008788b00000) (1024K)]
Total: 15

Threads with active compile tasks:
C2 CompilerThread0  76200 25331 %     4       com.google.common.collect.MapMakerInternalMap$Segment::expand @ 56 (276 bytes)
C2 CompilerThread1  76200 25335       4       org.gradle.api.internal.tasks.compile.incremental.compilerapi.deps.DependentSetSerializer::read (198 bytes)
C2 CompilerThread2  76200 25299       4       org.gradle.internal.serialize.HierarchicalNameSerializer::readName (51 bytes)
Total: 3

VM state: at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x00007ffd82a3e3a0] Threads_lock - owner thread: 0x00000279dd5dfcc0
[0x00007ffd82a3e4a0] Heap_lock - owner thread: 0x00000279f21b5990

Heap address: 0x0000000080000000, size: 2048 MB, Compressed Oops mode: 32-bit

CDS archive(s) not mapped
Compressed class space mapped at: 0x0000000100000000-0x0000000140000000, reserved size: 1073741824
Narrow klass base: 0x0000000000000000, Narrow klass shift: 3, Narrow klass range: 0x140000000

GC Precious Log:
 CardTable entry size: 512
 Card Set container configuration: InlinePtr #cards 5 size 8 Array Of Cards #cards 12 size 40 Howl #buckets 4 coarsen threshold 1843 Howl Bitmap #cards 512 size 80 coarsen threshold 460 Card regions per heap region 1 cards per card region 2048
 CPUs: 8 total, 8 available
 Memory: 22476M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Heap Region Size: 1M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 352M
 Heap Max Capacity: 2G
 Pre-touch: Disabled
 Parallel Workers: 8
 Concurrent Workers: 2
 Concurrent Refinement Workers: 8
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 647168K, used 382746K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 18 young (18432K), 18 survivors (18432K)
 Metaspace       used 173965K, committed 176512K, reserved 1245184K
  class space    used 22769K, committed 24064K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, TAMS=top-at-mark-start, PB=parsable bottom
|   0|0x0000000080000000, 0x0000000080100000, 0x0000000080100000|100%|HS|  |TAMS 0x0000000080000000| PB 0x0000000080000000| Complete 
|   1|0x0000000080100000, 0x0000000080200000, 0x0000000080200000|100%|HC|  |TAMS 0x0000000080100000| PB 0x0000000080100000| Complete 
|   2|0x0000000080200000, 0x0000000080300000, 0x0000000080300000|100%|HC|  |TAMS 0x0000000080200000| PB 0x0000000080200000| Complete 
|   3|0x0000000080300000, 0x0000000080400000, 0x0000000080400000|100%| O|  |TAMS 0x0000000080300000| PB 0x0000000080300000| Untracked 
|   4|0x0000000080400000, 0x0000000080500000, 0x0000000080500000|100%| O|  |TAMS 0x0000000080400000| PB 0x0000000080400000| Untracked 
|   5|0x0000000080500000, 0x0000000080600000, 0x0000000080600000|100%| O|  |TAMS 0x0000000080500000| PB 0x0000000080500000| Untracked 
|   6|0x0000000080600000, 0x0000000080700000, 0x0000000080700000|100%|HS|  |TAMS 0x0000000080600000| PB 0x0000000080600000| Complete 
|   7|0x0000000080700000, 0x0000000080800000, 0x0000000080800000|100%| O|  |TAMS 0x0000000080700000| PB 0x0000000080700000| Untracked 
|   8|0x0000000080800000, 0x0000000080900000, 0x0000000080900000|100%| O|  |TAMS 0x0000000080800000| PB 0x0000000080800000| Untracked 
|   9|0x0000000080900000, 0x0000000080a00000, 0x0000000080a00000|100%| O|  |TAMS 0x0000000080900000| PB 0x0000000080900000| Untracked 
|  10|0x0000000080a00000, 0x0000000080b00000, 0x0000000080b00000|100%| O|  |TAMS 0x0000000080a00000| PB 0x0000000080a00000| Untracked 
|  11|0x0000000080b00000, 0x0000000080c00000, 0x0000000080c00000|100%| O|  |TAMS 0x0000000080b00000| PB 0x0000000080b00000| Untracked 
|  12|0x0000000080c00000, 0x0000000080d00000, 0x0000000080d00000|100%| O|  |TAMS 0x0000000080c00000| PB 0x0000000080c00000| Untracked 
|  13|0x0000000080d00000, 0x0000000080e00000, 0x0000000080e00000|100%| O|  |TAMS 0x0000000080d00000| PB 0x0000000080d00000| Untracked 
|  14|0x0000000080e00000, 0x0000000080f00000, 0x0000000080f00000|100%| O|  |TAMS 0x0000000080e00000| PB 0x0000000080e00000| Untracked 
|  15|0x0000000080f00000, 0x0000000081000000, 0x0000000081000000|100%| O|  |TAMS 0x0000000080f00000| PB 0x0000000080f00000| Untracked 
|  16|0x0000000081000000, 0x0000000081100000, 0x0000000081100000|100%| O|  |TAMS 0x0000000081000000| PB 0x0000000081000000| Untracked 
|  17|0x0000000081100000, 0x0000000081200000, 0x0000000081200000|100%| O|  |TAMS 0x0000000081100000| PB 0x0000000081100000| Untracked 
|  18|0x0000000081200000, 0x0000000081300000, 0x0000000081300000|100%| O|  |TAMS 0x0000000081200000| PB 0x0000000081200000| Untracked 
|  19|0x0000000081300000, 0x0000000081400000, 0x0000000081400000|100%| O|  |TAMS 0x0000000081300000| PB 0x0000000081300000| Untracked 
|  20|0x0000000081400000, 0x0000000081500000, 0x0000000081500000|100%| O|  |TAMS 0x0000000081400000| PB 0x0000000081400000| Untracked 
|  21|0x0000000081500000, 0x0000000081600000, 0x0000000081600000|100%| O|  |TAMS 0x0000000081500000| PB 0x0000000081500000| Untracked 
|  22|0x0000000081600000, 0x0000000081700000, 0x0000000081700000|100%|HS|  |TAMS 0x0000000081600000| PB 0x0000000081600000| Complete 
|  23|0x0000000081700000, 0x0000000081800000, 0x0000000081800000|100%|HC|  |TAMS 0x0000000081700000| PB 0x0000000081700000| Complete 
|  24|0x0000000081800000, 0x0000000081900000, 0x0000000081900000|100%|HC|  |TAMS 0x0000000081800000| PB 0x0000000081800000| Complete 
|  25|0x0000000081900000, 0x0000000081a00000, 0x0000000081a00000|100%|HC|  |TAMS 0x0000000081900000| PB 0x0000000081900000| Complete 
|  26|0x0000000081a00000, 0x0000000081b00000, 0x0000000081b00000|100%|HC|  |TAMS 0x0000000081a00000| PB 0x0000000081a00000| Complete 
|  27|0x0000000081b00000, 0x0000000081c00000, 0x0000000081c00000|100%|HC|  |TAMS 0x0000000081b00000| PB 0x0000000081b00000| Complete 
|  28|0x0000000081c00000, 0x0000000081d00000, 0x0000000081d00000|100%|HS|  |TAMS 0x0000000081c00000| PB 0x0000000081c00000| Complete 
|  29|0x0000000081d00000, 0x0000000081e00000, 0x0000000081e00000|100%| O|  |TAMS 0x0000000081d00000| PB 0x0000000081d00000| Untracked 
|  30|0x0000000081e00000, 0x0000000081f00000, 0x0000000081f00000|100%|HS|  |TAMS 0x0000000081e00000| PB 0x0000000081e00000| Complete 
|  31|0x0000000081f00000, 0x0000000082000000, 0x0000000082000000|100%| O|  |TAMS 0x0000000081f00000| PB 0x0000000081f00000| Untracked 
|  32|0x0000000082000000, 0x0000000082100000, 0x0000000082100000|100%|HS|  |TAMS 0x0000000082000000| PB 0x0000000082000000| Complete 
|  33|0x0000000082100000, 0x0000000082200000, 0x0000000082200000|100%|HC|  |TAMS 0x0000000082100000| PB 0x0000000082100000| Complete 
|  34|0x0000000082200000, 0x0000000082300000, 0x0000000082300000|100%|HC|  |TAMS 0x0000000082200000| PB 0x0000000082200000| Complete 
|  35|0x0000000082300000, 0x0000000082400000, 0x0000000082400000|100%|HC|  |TAMS 0x0000000082300000| PB 0x0000000082300000| Complete 
|  36|0x0000000082400000, 0x0000000082500000, 0x0000000082500000|100%| O|  |TAMS 0x0000000082400000| PB 0x0000000082400000| Untracked 
|  37|0x0000000082500000, 0x0000000082600000, 0x0000000082600000|100%| O|  |TAMS 0x0000000082500000| PB 0x0000000082500000| Untracked 
|  38|0x0000000082600000, 0x0000000082700000, 0x0000000082700000|100%|HS|  |TAMS 0x0000000082600000| PB 0x0000000082600000| Complete 
|  39|0x0000000082700000, 0x0000000082800000, 0x0000000082800000|100%|HC|  |TAMS 0x0000000082700000| PB 0x0000000082700000| Complete 
|  40|0x0000000082800000, 0x0000000082900000, 0x0000000082900000|100%| O|  |TAMS 0x0000000082800000| PB 0x0000000082800000| Untracked 
|  41|0x0000000082900000, 0x0000000082a00000, 0x0000000082a00000|100%| O|  |TAMS 0x0000000082900000| PB 0x0000000082900000| Untracked 
|  42|0x0000000082a00000, 0x0000000082b00000, 0x0000000082b00000|100%| O|  |TAMS 0x0000000082a00000| PB 0x0000000082a00000| Untracked 
|  43|0x0000000082b00000, 0x0000000082c00000, 0x0000000082c00000|100%| O|  |TAMS 0x0000000082b00000| PB 0x0000000082b00000| Untracked 
|  44|0x0000000082c00000, 0x0000000082d00000, 0x0000000082d00000|100%|HS|  |TAMS 0x0000000082c00000| PB 0x0000000082c00000| Complete 
|  45|0x0000000082d00000, 0x0000000082e00000, 0x0000000082e00000|100%|HC|  |TAMS 0x0000000082d00000| PB 0x0000000082d00000| Complete 
|  46|0x0000000082e00000, 0x0000000082f00000, 0x0000000082f00000|100%| O|  |TAMS 0x0000000082e00000| PB 0x0000000082e00000| Untracked 
|  47|0x0000000082f00000, 0x0000000083000000, 0x0000000083000000|100%| O|  |TAMS 0x0000000082f00000| PB 0x0000000082f00000| Untracked 
|  48|0x0000000083000000, 0x0000000083100000, 0x0000000083100000|100%| O|  |TAMS 0x0000000083000000| PB 0x0000000083000000| Untracked 
|  49|0x0000000083100000, 0x0000000083200000, 0x0000000083200000|100%| O|  |TAMS 0x0000000083100000| PB 0x0000000083100000| Untracked 
|  50|0x0000000083200000, 0x0000000083300000, 0x0000000083300000|100%| O|  |TAMS 0x0000000083200000| PB 0x0000000083200000| Untracked 
|  51|0x0000000083300000, 0x0000000083400000, 0x0000000083400000|100%| O|  |TAMS 0x0000000083300000| PB 0x0000000083300000| Untracked 
|  52|0x0000000083400000, 0x0000000083500000, 0x0000000083500000|100%|HS|  |TAMS 0x0000000083400000| PB 0x0000000083400000| Complete 
|  53|0x0000000083500000, 0x0000000083600000, 0x0000000083600000|100%|HC|  |TAMS 0x0000000083500000| PB 0x0000000083500000| Complete 
|  54|0x0000000083600000, 0x0000000083700000, 0x0000000083700000|100%|HC|  |TAMS 0x0000000083600000| PB 0x0000000083600000| Complete 
|  55|0x0000000083700000, 0x0000000083800000, 0x0000000083800000|100%|HC|  |TAMS 0x0000000083700000| PB 0x0000000083700000| Complete 
|  56|0x0000000083800000, 0x0000000083900000, 0x0000000083900000|100%|HC|  |TAMS 0x0000000083800000| PB 0x0000000083800000| Complete 
|  57|0x0000000083900000, 0x0000000083a00000, 0x0000000083a00000|100%|HC|  |TAMS 0x0000000083900000| PB 0x0000000083900000| Complete 
|  58|0x0000000083a00000, 0x0000000083b00000, 0x0000000083b00000|100%|HS|  |TAMS 0x0000000083a00000| PB 0x0000000083a00000| Complete 
|  59|0x0000000083b00000, 0x0000000083c00000, 0x0000000083c00000|100%|HC|  |TAMS 0x0000000083b00000| PB 0x0000000083b00000| Complete 
|  60|0x0000000083c00000, 0x0000000083d00000, 0x0000000083d00000|100%|HC|  |TAMS 0x0000000083c00000| PB 0x0000000083c00000| Complete 
|  61|0x0000000083d00000, 0x0000000083e00000, 0x0000000083e00000|100%|HC|  |TAMS 0x0000000083d00000| PB 0x0000000083d00000| Complete 
|  62|0x0000000083e00000, 0x0000000083f00000, 0x0000000083f00000|100%|HC|  |TAMS 0x0000000083e00000| PB 0x0000000083e00000| Complete 
|  63|0x0000000083f00000, 0x0000000084000000, 0x0000000084000000|100%|HC|  |TAMS 0x0000000083f00000| PB 0x0000000083f00000| Complete 
|  64|0x0000000084000000, 0x0000000084100000, 0x0000000084100000|100%| O|  |TAMS 0x0000000084000000| PB 0x0000000084000000| Untracked 
|  65|0x0000000084100000, 0x0000000084200000, 0x0000000084200000|100%| O|  |TAMS 0x0000000084100000| PB 0x0000000084100000| Untracked 
|  66|0x0000000084200000, 0x0000000084300000, 0x0000000084300000|100%| O|  |TAMS 0x0000000084200000| PB 0x0000000084200000| Untracked 
|  67|0x0000000084300000, 0x0000000084400000, 0x0000000084400000|100%| O|  |TAMS 0x0000000084300000| PB 0x0000000084300000| Untracked 
|  68|0x0000000084400000, 0x0000000084500000, 0x0000000084500000|100%| O|  |TAMS 0x0000000084400000| PB 0x0000000084400000| Untracked 
|  69|0x0000000084500000, 0x0000000084600000, 0x0000000084600000|100%| O|  |TAMS 0x0000000084500000| PB 0x0000000084500000| Untracked 
|  70|0x0000000084600000, 0x0000000084700000, 0x0000000084700000|100%| O|  |TAMS 0x0000000084600000| PB 0x0000000084600000| Untracked 
|  71|0x0000000084700000, 0x0000000084700000, 0x0000000084800000|  0%| F|  |TAMS 0x0000000084700000| PB 0x0000000084700000| Untracked 
|  72|0x0000000084800000, 0x0000000084900000, 0x0000000084900000|100%| O|  |TAMS 0x0000000084800000| PB 0x0000000084800000| Untracked 
|  73|0x0000000084900000, 0x0000000084a00000, 0x0000000084a00000|100%| O|  |TAMS 0x0000000084900000| PB 0x0000000084900000| Untracked 
|  74|0x0000000084a00000, 0x0000000084b00000, 0x0000000084b00000|100%| O|  |TAMS 0x0000000084a00000| PB 0x0000000084a00000| Untracked 
|  75|0x0000000084b00000, 0x0000000084c00000, 0x0000000084c00000|100%| O|  |TAMS 0x0000000084b00000| PB 0x0000000084b00000| Untracked 
|  76|0x0000000084c00000, 0x0000000084d00000, 0x0000000084d00000|100%| O|  |TAMS 0x0000000084c00000| PB 0x0000000084c00000| Untracked 
|  77|0x0000000084d00000, 0x0000000084e00000, 0x0000000084e00000|100%| O|  |TAMS 0x0000000084d00000| PB 0x0000000084d00000| Untracked 
|  78|0x0000000084e00000, 0x0000000084f00000, 0x0000000084f00000|100%| O|  |TAMS 0x0000000084e00000| PB 0x0000000084e00000| Untracked 
|  79|0x0000000084f00000, 0x0000000085000000, 0x0000000085000000|100%| O|  |TAMS 0x0000000084f00000| PB 0x0000000084f00000| Untracked 
|  80|0x0000000085000000, 0x0000000085100000, 0x0000000085100000|100%|HS|  |TAMS 0x0000000085000000| PB 0x0000000085000000| Complete 
|  81|0x0000000085100000, 0x0000000085200000, 0x0000000085200000|100%|HS|  |TAMS 0x0000000085100000| PB 0x0000000085100000| Complete 
|  82|0x0000000085200000, 0x0000000085300000, 0x0000000085300000|100%|HS|  |TAMS 0x0000000085200000| PB 0x0000000085200000| Complete 
|  83|0x0000000085300000, 0x0000000085400000, 0x0000000085400000|100%|HS|  |TAMS 0x0000000085300000| PB 0x0000000085300000| Complete 
|  84|0x0000000085400000, 0x0000000085500000, 0x0000000085500000|100%|HS|  |TAMS 0x0000000085400000| PB 0x0000000085400000| Complete 
|  85|0x0000000085500000, 0x0000000085600000, 0x0000000085600000|100%| O|  |TAMS 0x0000000085500000| PB 0x0000000085500000| Untracked 
|  86|0x0000000085600000, 0x0000000085700000, 0x0000000085700000|100%|HS|  |TAMS 0x0000000085600000| PB 0x0000000085600000| Complete 
|  87|0x0000000085700000, 0x0000000085800000, 0x0000000085800000|100%| O|  |TAMS 0x0000000085700000| PB 0x0000000085700000| Untracked 
|  88|0x0000000085800000, 0x0000000085900000, 0x0000000085900000|100%| O|  |TAMS 0x0000000085800000| PB 0x0000000085800000| Untracked 
|  89|0x0000000085900000, 0x0000000085a00000, 0x0000000085a00000|100%| O|  |TAMS 0x0000000085900000| PB 0x0000000085900000| Untracked 
|  90|0x0000000085a00000, 0x0000000085b00000, 0x0000000085b00000|100%| O|  |TAMS 0x0000000085a00000| PB 0x0000000085a00000| Untracked 
|  91|0x0000000085b00000, 0x0000000085c00000, 0x0000000085c00000|100%| O|  |TAMS 0x0000000085b00000| PB 0x0000000085b00000| Untracked 
|  92|0x0000000085c00000, 0x0000000085d00000, 0x0000000085d00000|100%| O|  |TAMS 0x0000000085c00000| PB 0x0000000085c00000| Untracked 
|  93|0x0000000085d00000, 0x0000000085e00000, 0x0000000085e00000|100%| O|  |TAMS 0x0000000085d00000| PB 0x0000000085d00000| Untracked 
|  94|0x0000000085e00000, 0x0000000085f00000, 0x0000000085f00000|100%| O|  |TAMS 0x0000000085e00000| PB 0x0000000085e00000| Untracked 
|  95|0x0000000085f00000, 0x0000000086000000, 0x0000000086000000|100%| O|  |TAMS 0x0000000085f00000| PB 0x0000000085f00000| Untracked 
|  96|0x0000000086000000, 0x0000000086100000, 0x0000000086100000|100%| O|  |TAMS 0x0000000086000000| PB 0x0000000086000000| Untracked 
|  97|0x0000000086100000, 0x0000000086200000, 0x0000000086200000|100%|HS|  |TAMS 0x0000000086100000| PB 0x0000000086100000| Complete 
|  98|0x0000000086200000, 0x0000000086300000, 0x0000000086300000|100%| O|  |TAMS 0x0000000086200000| PB 0x0000000086200000| Untracked 
|  99|0x0000000086300000, 0x0000000086400000, 0x0000000086400000|100%| O|  |TAMS 0x0000000086300000| PB 0x0000000086300000| Untracked 
| 100|0x0000000086400000, 0x0000000086500000, 0x0000000086500000|100%| O|  |TAMS 0x0000000086400000| PB 0x0000000086400000| Untracked 
| 101|0x0000000086500000, 0x0000000086600000, 0x0000000086600000|100%| O|  |TAMS 0x0000000086500000| PB 0x0000000086500000| Untracked 
| 102|0x0000000086600000, 0x0000000086700000, 0x0000000086700000|100%| O|  |TAMS 0x0000000086600000| PB 0x0000000086600000| Untracked 
| 103|0x0000000086700000, 0x0000000086800000, 0x0000000086800000|100%| O|  |TAMS 0x0000000086700000| PB 0x0000000086700000| Untracked 
| 104|0x0000000086800000, 0x0000000086900000, 0x0000000086900000|100%| O|  |TAMS 0x0000000086800000| PB 0x0000000086800000| Untracked 
| 105|0x0000000086900000, 0x0000000086a00000, 0x0000000086a00000|100%| O|  |TAMS 0x0000000086900000| PB 0x0000000086900000| Untracked 
| 106|0x0000000086a00000, 0x0000000086b00000, 0x0000000086b00000|100%| O|  |TAMS 0x0000000086a00000| PB 0x0000000086a00000| Untracked 
| 107|0x0000000086b00000, 0x0000000086c00000, 0x0000000086c00000|100%|HS|  |TAMS 0x0000000086b00000| PB 0x0000000086b00000| Complete 
| 108|0x0000000086c00000, 0x0000000086d00000, 0x0000000086d00000|100%|HC|  |TAMS 0x0000000086c00000| PB 0x0000000086c00000| Complete 
| 109|0x0000000086d00000, 0x0000000086e00000, 0x0000000086e00000|100%|HS|  |TAMS 0x0000000086d00000| PB 0x0000000086d00000| Complete 
| 110|0x0000000086e00000, 0x0000000086f00000, 0x0000000086f00000|100%|HC|  |TAMS 0x0000000086e00000| PB 0x0000000086e00000| Complete 
| 111|0x0000000086f00000, 0x0000000087000000, 0x0000000087000000|100%| O|  |TAMS 0x0000000086f00000| PB 0x0000000086f00000| Untracked 
| 112|0x0000000087000000, 0x0000000087100000, 0x0000000087100000|100%| O|  |TAMS 0x0000000087000000| PB 0x0000000087000000| Untracked 
| 113|0x0000000087100000, 0x0000000087200000, 0x0000000087200000|100%| O|  |TAMS 0x0000000087100000| PB 0x0000000087100000| Untracked 
| 114|0x0000000087200000, 0x0000000087300000, 0x0000000087300000|100%| O|  |TAMS 0x0000000087200000| PB 0x0000000087200000| Untracked 
| 115|0x0000000087300000, 0x0000000087400000, 0x0000000087400000|100%| O|  |TAMS 0x0000000087300000| PB 0x0000000087300000| Untracked 
| 116|0x0000000087400000, 0x0000000087500000, 0x0000000087500000|100%| O|  |TAMS 0x0000000087400000| PB 0x0000000087400000| Untracked 
| 117|0x0000000087500000, 0x0000000087600000, 0x0000000087600000|100%| O|  |TAMS 0x0000000087500000| PB 0x0000000087500000| Untracked 
| 118|0x0000000087600000, 0x0000000087700000, 0x0000000087700000|100%| O|  |TAMS 0x0000000087600000| PB 0x0000000087600000| Untracked 
| 119|0x0000000087700000, 0x0000000087800000, 0x0000000087800000|100%| O|  |TAMS 0x0000000087700000| PB 0x0000000087700000| Untracked 
| 120|0x0000000087800000, 0x0000000087900000, 0x0000000087900000|100%| O|  |TAMS 0x0000000087800000| PB 0x0000000087800000| Untracked 
| 121|0x0000000087900000, 0x0000000087a00000, 0x0000000087a00000|100%| O|  |TAMS 0x0000000087900000| PB 0x0000000087900000| Untracked 
| 122|0x0000000087a00000, 0x0000000087b00000, 0x0000000087b00000|100%| O|  |TAMS 0x0000000087a00000| PB 0x0000000087a00000| Untracked 
| 123|0x0000000087b00000, 0x0000000087c00000, 0x0000000087c00000|100%| O|  |TAMS 0x0000000087b00000| PB 0x0000000087b00000| Untracked 
| 124|0x0000000087c00000, 0x0000000087d00000, 0x0000000087d00000|100%| O|  |TAMS 0x0000000087c00000| PB 0x0000000087c00000| Untracked 
| 125|0x0000000087d00000, 0x0000000087e00000, 0x0000000087e00000|100%| O|  |TAMS 0x0000000087d00000| PB 0x0000000087d00000| Untracked 
| 126|0x0000000087e00000, 0x0000000087f00000, 0x0000000087f00000|100%| O|  |TAMS 0x0000000087e00000| PB 0x0000000087e00000| Untracked 
| 127|0x0000000087f00000, 0x0000000088000000, 0x0000000088000000|100%| O|  |TAMS 0x0000000087f00000| PB 0x0000000087f00000| Untracked 
| 128|0x0000000088000000, 0x0000000088100000, 0x0000000088100000|100%| O|  |TAMS 0x0000000088000000| PB 0x0000000088000000| Untracked 
| 129|0x0000000088100000, 0x0000000088200000, 0x0000000088200000|100%| O|  |TAMS 0x0000000088100000| PB 0x0000000088100000| Untracked 
| 130|0x0000000088200000, 0x0000000088300000, 0x0000000088300000|100%| O|  |TAMS 0x0000000088200000| PB 0x0000000088200000| Untracked 
| 131|0x0000000088300000, 0x0000000088400000, 0x0000000088400000|100%| O|  |TAMS 0x0000000088300000| PB 0x0000000088300000| Untracked 
| 132|0x0000000088400000, 0x0000000088500000, 0x0000000088500000|100%|HS|  |TAMS 0x0000000088400000| PB 0x0000000088400000| Complete 
| 133|0x0000000088500000, 0x0000000088600000, 0x0000000088600000|100%|HC|  |TAMS 0x0000000088500000| PB 0x0000000088500000| Complete 
| 134|0x0000000088600000, 0x0000000088700000, 0x0000000088700000|100%|HC|  |TAMS 0x0000000088600000| PB 0x0000000088600000| Complete 
| 135|0x0000000088700000, 0x0000000088800000, 0x0000000088800000|100%|HC|  |TAMS 0x0000000088700000| PB 0x0000000088700000| Complete 
| 136|0x0000000088800000, 0x0000000088900000, 0x0000000088900000|100%|HC|  |TAMS 0x0000000088800000| PB 0x0000000088800000| Complete 
| 137|0x0000000088900000, 0x0000000088a00000, 0x0000000088a00000|100%|HC|  |TAMS 0x0000000088900000| PB 0x0000000088900000| Complete 
| 138|0x0000000088a00000, 0x0000000088b00000, 0x0000000088b00000|100%| O|  |TAMS 0x0000000088a00000| PB 0x0000000088a00000| Untracked 
| 139|0x0000000088b00000, 0x0000000088c00000, 0x0000000088c00000|100%|HS|  |TAMS 0x0000000088b00000| PB 0x0000000088b00000| Complete 
| 140|0x0000000088c00000, 0x0000000088d00000, 0x0000000088d00000|100%|HC|  |TAMS 0x0000000088c00000| PB 0x0000000088c00000| Complete 
| 141|0x0000000088d00000, 0x0000000088e00000, 0x0000000088e00000|100%|HC|  |TAMS 0x0000000088d00000| PB 0x0000000088d00000| Complete 
| 142|0x0000000088e00000, 0x0000000088f00000, 0x0000000088f00000|100%|HC|  |TAMS 0x0000000088e00000| PB 0x0000000088e00000| Complete 
| 143|0x0000000088f00000, 0x0000000089000000, 0x0000000089000000|100%|HS|  |TAMS 0x0000000088f00000| PB 0x0000000088f00000| Complete 
| 144|0x0000000089000000, 0x0000000089100000, 0x0000000089100000|100%| O|  |TAMS 0x0000000089000000| PB 0x0000000089000000| Untracked 
| 145|0x0000000089100000, 0x0000000089200000, 0x0000000089200000|100%| O|  |TAMS 0x0000000089100000| PB 0x0000000089100000| Untracked 
| 146|0x0000000089200000, 0x0000000089300000, 0x0000000089300000|100%| O|  |TAMS 0x0000000089200000| PB 0x0000000089200000| Untracked 
| 147|0x0000000089300000, 0x0000000089400000, 0x0000000089400000|100%| O|  |TAMS 0x0000000089300000| PB 0x0000000089300000| Untracked 
| 148|0x0000000089400000, 0x0000000089500000, 0x0000000089500000|100%| O|  |TAMS 0x0000000089400000| PB 0x0000000089400000| Untracked 
| 149|0x0000000089500000, 0x0000000089600000, 0x0000000089600000|100%| O|  |TAMS 0x0000000089500000| PB 0x0000000089500000| Untracked 
| 150|0x0000000089600000, 0x0000000089700000, 0x0000000089700000|100%| O|  |TAMS 0x0000000089600000| PB 0x0000000089600000| Untracked 
| 151|0x0000000089700000, 0x0000000089800000, 0x0000000089800000|100%| O|  |TAMS 0x0000000089700000| PB 0x0000000089700000| Untracked 
| 152|0x0000000089800000, 0x0000000089900000, 0x0000000089900000|100%| O|  |TAMS 0x0000000089800000| PB 0x0000000089800000| Untracked 
| 153|0x0000000089900000, 0x0000000089a00000, 0x0000000089a00000|100%| O|  |TAMS 0x0000000089900000| PB 0x0000000089900000| Untracked 
| 154|0x0000000089a00000, 0x0000000089b00000, 0x0000000089b00000|100%| O|  |TAMS 0x0000000089a00000| PB 0x0000000089a00000| Untracked 
| 155|0x0000000089b00000, 0x0000000089c00000, 0x0000000089c00000|100%| O|  |TAMS 0x0000000089b00000| PB 0x0000000089b00000| Untracked 
| 156|0x0000000089c00000, 0x0000000089d00000, 0x0000000089d00000|100%| O|  |TAMS 0x0000000089c00000| PB 0x0000000089c00000| Untracked 
| 157|0x0000000089d00000, 0x0000000089e00000, 0x0000000089e00000|100%| O|  |TAMS 0x0000000089d00000| PB 0x0000000089d00000| Untracked 
| 158|0x0000000089e00000, 0x0000000089f00000, 0x0000000089f00000|100%| O|  |TAMS 0x0000000089e00000| PB 0x0000000089e00000| Untracked 
| 159|0x0000000089f00000, 0x000000008a000000, 0x000000008a000000|100%| O|  |TAMS 0x0000000089f00000| PB 0x0000000089f00000| Untracked 
| 160|0x000000008a000000, 0x000000008a100000, 0x000000008a100000|100%| O|  |TAMS 0x000000008a000000| PB 0x000000008a000000| Untracked 
| 161|0x000000008a100000, 0x000000008a100000, 0x000000008a200000|  0%| F|  |TAMS 0x000000008a100000| PB 0x000000008a100000| Untracked 
| 162|0x000000008a200000, 0x000000008a300000, 0x000000008a300000|100%| O|  |TAMS 0x000000008a200000| PB 0x000000008a200000| Untracked 
| 163|0x000000008a300000, 0x000000008a400000, 0x000000008a400000|100%| O|  |TAMS 0x000000008a300000| PB 0x000000008a300000| Untracked 
| 164|0x000000008a400000, 0x000000008a500000, 0x000000008a500000|100%| O|  |TAMS 0x000000008a400000| PB 0x000000008a400000| Untracked 
| 165|0x000000008a500000, 0x000000008a600000, 0x000000008a600000|100%| O|  |TAMS 0x000000008a500000| PB 0x000000008a500000| Untracked 
| 166|0x000000008a600000, 0x000000008a700000, 0x000000008a700000|100%| O|  |TAMS 0x000000008a600000| PB 0x000000008a600000| Untracked 
| 167|0x000000008a700000, 0x000000008a800000, 0x000000008a800000|100%| O|  |TAMS 0x000000008a700000| PB 0x000000008a700000| Untracked 
| 168|0x000000008a800000, 0x000000008a900000, 0x000000008a900000|100%| O|  |TAMS 0x000000008a800000| PB 0x000000008a800000| Untracked 
| 169|0x000000008a900000, 0x000000008aa00000, 0x000000008aa00000|100%| O|  |TAMS 0x000000008a900000| PB 0x000000008a900000| Untracked 
| 170|0x000000008aa00000, 0x000000008ab00000, 0x000000008ab00000|100%| O|  |TAMS 0x000000008aa00000| PB 0x000000008aa00000| Untracked 
| 171|0x000000008ab00000, 0x000000008ac00000, 0x000000008ac00000|100%| O|  |TAMS 0x000000008ab00000| PB 0x000000008ab00000| Untracked 
| 172|0x000000008ac00000, 0x000000008ad00000, 0x000000008ad00000|100%| O|  |TAMS 0x000000008ac00000| PB 0x000000008ac00000| Untracked 
| 173|0x000000008ad00000, 0x000000008ae00000, 0x000000008ae00000|100%| O|  |TAMS 0x000000008ad00000| PB 0x000000008ad00000| Untracked 
| 174|0x000000008ae00000, 0x000000008af00000, 0x000000008af00000|100%| O|  |TAMS 0x000000008ae00000| PB 0x000000008ae00000| Untracked 
| 175|0x000000008af00000, 0x000000008b000000, 0x000000008b000000|100%| O|  |TAMS 0x000000008af00000| PB 0x000000008af00000| Untracked 
| 176|0x000000008b000000, 0x000000008b100000, 0x000000008b100000|100%| O|  |TAMS 0x000000008b000000| PB 0x000000008b000000| Untracked 
| 177|0x000000008b100000, 0x000000008b200000, 0x000000008b200000|100%| O|  |TAMS 0x000000008b100000| PB 0x000000008b100000| Untracked 
| 178|0x000000008b200000, 0x000000008b300000, 0x000000008b300000|100%| O|  |TAMS 0x000000008b200000| PB 0x000000008b200000| Untracked 
| 179|0x000000008b300000, 0x000000008b400000, 0x000000008b400000|100%| O|  |TAMS 0x000000008b300000| PB 0x000000008b300000| Untracked 
| 180|0x000000008b400000, 0x000000008b500000, 0x000000008b500000|100%| O|  |TAMS 0x000000008b400000| PB 0x000000008b400000| Untracked 
| 181|0x000000008b500000, 0x000000008b600000, 0x000000008b600000|100%| O|  |TAMS 0x000000008b500000| PB 0x000000008b500000| Untracked 
| 182|0x000000008b600000, 0x000000008b700000, 0x000000008b700000|100%| O|  |TAMS 0x000000008b600000| PB 0x000000008b600000| Untracked 
| 183|0x000000008b700000, 0x000000008b800000, 0x000000008b800000|100%|HS|  |TAMS 0x000000008b700000| PB 0x000000008b700000| Complete 
| 184|0x000000008b800000, 0x000000008b900000, 0x000000008b900000|100%|HC|  |TAMS 0x000000008b800000| PB 0x000000008b800000| Complete 
| 185|0x000000008b900000, 0x000000008ba00000, 0x000000008ba00000|100%|HS|  |TAMS 0x000000008b900000| PB 0x000000008b900000| Complete 
| 186|0x000000008ba00000, 0x000000008bb00000, 0x000000008bb00000|100%|HS|  |TAMS 0x000000008ba00000| PB 0x000000008ba00000| Complete 
| 187|0x000000008bb00000, 0x000000008bc00000, 0x000000008bc00000|100%|HS|  |TAMS 0x000000008bb00000| PB 0x000000008bb00000| Complete 
| 188|0x000000008bc00000, 0x000000008bd00000, 0x000000008bd00000|100%| O|  |TAMS 0x000000008bc00000| PB 0x000000008bc00000| Untracked 
| 189|0x000000008bd00000, 0x000000008bd00000, 0x000000008be00000|  0%| F|  |TAMS 0x000000008bd00000| PB 0x000000008bd00000| Untracked 
| 190|0x000000008be00000, 0x000000008bf00000, 0x000000008bf00000|100%| O|  |TAMS 0x000000008be00000| PB 0x000000008be00000| Untracked 
| 191|0x000000008bf00000, 0x000000008c000000, 0x000000008c000000|100%| O|  |TAMS 0x000000008bf00000| PB 0x000000008bf00000| Untracked 
| 192|0x000000008c000000, 0x000000008c100000, 0x000000008c100000|100%| O|  |TAMS 0x000000008c000000| PB 0x000000008c000000| Untracked 
| 193|0x000000008c100000, 0x000000008c200000, 0x000000008c200000|100%| O|  |TAMS 0x000000008c100000| PB 0x000000008c100000| Untracked 
| 194|0x000000008c200000, 0x000000008c300000, 0x000000008c300000|100%| O|  |TAMS 0x000000008c200000| PB 0x000000008c200000| Untracked 
| 195|0x000000008c300000, 0x000000008c400000, 0x000000008c400000|100%| O|  |TAMS 0x000000008c300000| PB 0x000000008c300000| Untracked 
| 196|0x000000008c400000, 0x000000008c500000, 0x000000008c500000|100%| O|  |TAMS 0x000000008c400000| PB 0x000000008c400000| Untracked 
| 197|0x000000008c500000, 0x000000008c600000, 0x000000008c600000|100%|HS|  |TAMS 0x000000008c500000| PB 0x000000008c500000| Complete 
| 198|0x000000008c600000, 0x000000008c700000, 0x000000008c700000|100%|HC|  |TAMS 0x000000008c600000| PB 0x000000008c600000| Complete 
| 199|0x000000008c700000, 0x000000008c800000, 0x000000008c800000|100%|HS|  |TAMS 0x000000008c700000| PB 0x000000008c700000| Complete 
| 200|0x000000008c800000, 0x000000008c900000, 0x000000008c900000|100%|HC|  |TAMS 0x000000008c800000| PB 0x000000008c800000| Complete 
| 201|0x000000008c900000, 0x000000008ca00000, 0x000000008ca00000|100%|HC|  |TAMS 0x000000008c900000| PB 0x000000008c900000| Complete 
| 202|0x000000008ca00000, 0x000000008cb00000, 0x000000008cb00000|100%|HS|  |TAMS 0x000000008ca00000| PB 0x000000008ca00000| Complete 
| 203|0x000000008cb00000, 0x000000008cc00000, 0x000000008cc00000|100%|HS|  |TAMS 0x000000008cb00000| PB 0x000000008cb00000| Complete 
| 204|0x000000008cc00000, 0x000000008cd00000, 0x000000008cd00000|100%|HC|  |TAMS 0x000000008cc00000| PB 0x000000008cc00000| Complete 
| 205|0x000000008cd00000, 0x000000008ce00000, 0x000000008ce00000|100%|HS|  |TAMS 0x000000008cd00000| PB 0x000000008cd00000| Complete 
| 206|0x000000008ce00000, 0x000000008cf00000, 0x000000008cf00000|100%|HC|  |TAMS 0x000000008ce00000| PB 0x000000008ce00000| Complete 
| 207|0x000000008cf00000, 0x000000008d000000, 0x000000008d000000|100%|HC|  |TAMS 0x000000008cf00000| PB 0x000000008cf00000| Complete 
| 208|0x000000008d000000, 0x000000008d100000, 0x000000008d100000|100%| O|  |TAMS 0x000000008d000000| PB 0x000000008d000000| Untracked 
| 209|0x000000008d100000, 0x000000008d200000, 0x000000008d200000|100%| O|  |TAMS 0x000000008d100000| PB 0x000000008d100000| Untracked 
| 210|0x000000008d200000, 0x000000008d300000, 0x000000008d300000|100%| O|  |TAMS 0x000000008d200000| PB 0x000000008d200000| Untracked 
| 211|0x000000008d300000, 0x000000008d400000, 0x000000008d400000|100%| O|  |TAMS 0x000000008d300000| PB 0x000000008d300000| Untracked 
| 212|0x000000008d400000, 0x000000008d500000, 0x000000008d500000|100%| O|  |TAMS 0x000000008d400000| PB 0x000000008d400000| Untracked 
| 213|0x000000008d500000, 0x000000008d600000, 0x000000008d600000|100%| O|  |TAMS 0x000000008d500000| PB 0x000000008d500000| Untracked 
| 214|0x000000008d600000, 0x000000008d700000, 0x000000008d700000|100%| O|  |TAMS 0x000000008d600000| PB 0x000000008d600000| Untracked 
| 215|0x000000008d700000, 0x000000008d800000, 0x000000008d800000|100%| O|  |TAMS 0x000000008d700000| PB 0x000000008d700000| Untracked 
| 216|0x000000008d800000, 0x000000008d900000, 0x000000008d900000|100%| O|  |TAMS 0x000000008d800000| PB 0x000000008d800000| Untracked 
| 217|0x000000008d900000, 0x000000008da00000, 0x000000008da00000|100%| O|  |TAMS 0x000000008d900000| PB 0x000000008d900000| Untracked 
| 218|0x000000008da00000, 0x000000008db00000, 0x000000008db00000|100%| O|  |TAMS 0x000000008da00000| PB 0x000000008da00000| Untracked 
| 219|0x000000008db00000, 0x000000008dc00000, 0x000000008dc00000|100%| O|  |TAMS 0x000000008db00000| PB 0x000000008db00000| Untracked 
| 220|0x000000008dc00000, 0x000000008dd00000, 0x000000008dd00000|100%| O|  |TAMS 0x000000008dc00000| PB 0x000000008dc00000| Untracked 
| 221|0x000000008dd00000, 0x000000008de00000, 0x000000008de00000|100%| O|  |TAMS 0x000000008dd00000| PB 0x000000008dd00000| Untracked 
| 222|0x000000008de00000, 0x000000008df00000, 0x000000008df00000|100%| O|  |TAMS 0x000000008de00000| PB 0x000000008de00000| Untracked 
| 223|0x000000008df00000, 0x000000008e000000, 0x000000008e000000|100%| O|  |TAMS 0x000000008df00000| PB 0x000000008df00000| Untracked 
| 224|0x000000008e000000, 0x000000008e100000, 0x000000008e100000|100%| O|  |TAMS 0x000000008e000000| PB 0x000000008e000000| Untracked 
| 225|0x000000008e100000, 0x000000008e200000, 0x000000008e200000|100%| O|  |TAMS 0x000000008e100000| PB 0x000000008e100000| Untracked 
| 226|0x000000008e200000, 0x000000008e300000, 0x000000008e300000|100%| O|  |TAMS 0x000000008e200000| PB 0x000000008e200000| Untracked 
| 227|0x000000008e300000, 0x000000008e400000, 0x000000008e400000|100%| O|  |TAMS 0x000000008e300000| PB 0x000000008e300000| Untracked 
| 228|0x000000008e400000, 0x000000008e500000, 0x000000008e500000|100%| O|  |TAMS 0x000000008e400000| PB 0x000000008e400000| Untracked 
| 229|0x000000008e500000, 0x000000008e600000, 0x000000008e600000|100%| O|  |TAMS 0x000000008e500000| PB 0x000000008e500000| Untracked 
| 230|0x000000008e600000, 0x000000008e700000, 0x000000008e700000|100%| O|  |TAMS 0x000000008e600000| PB 0x000000008e600000| Untracked 
| 231|0x000000008e700000, 0x000000008e800000, 0x000000008e800000|100%| O|  |TAMS 0x000000008e700000| PB 0x000000008e700000| Untracked 
| 232|0x000000008e800000, 0x000000008e900000, 0x000000008e900000|100%| O|  |TAMS 0x000000008e800000| PB 0x000000008e800000| Untracked 
| 233|0x000000008e900000, 0x000000008e900000, 0x000000008ea00000|  0%| F|  |TAMS 0x000000008e900000| PB 0x000000008e900000| Untracked 
| 234|0x000000008ea00000, 0x000000008eb00000, 0x000000008eb00000|100%| O|  |TAMS 0x000000008ea00000| PB 0x000000008ea00000| Untracked 
| 235|0x000000008eb00000, 0x000000008eb00000, 0x000000008ec00000|  0%| F|  |TAMS 0x000000008eb00000| PB 0x000000008eb00000| Untracked 
| 236|0x000000008ec00000, 0x000000008ed00000, 0x000000008ed00000|100%| O|  |TAMS 0x000000008ec00000| PB 0x000000008ec00000| Untracked 
| 237|0x000000008ed00000, 0x000000008ee00000, 0x000000008ee00000|100%| O|  |TAMS 0x000000008ed00000| PB 0x000000008ed00000| Untracked 
| 238|0x000000008ee00000, 0x000000008ef00000, 0x000000008ef00000|100%| O|  |TAMS 0x000000008ee00000| PB 0x000000008ee00000| Untracked 
| 239|0x000000008ef00000, 0x000000008f000000, 0x000000008f000000|100%| O|  |TAMS 0x000000008ef00000| PB 0x000000008ef00000| Untracked 
| 240|0x000000008f000000, 0x000000008f100000, 0x000000008f100000|100%| O|  |TAMS 0x000000008f000000| PB 0x000000008f000000| Untracked 
| 241|0x000000008f100000, 0x000000008f100000, 0x000000008f200000|  0%| F|  |TAMS 0x000000008f100000| PB 0x000000008f100000| Untracked 
| 242|0x000000008f200000, 0x000000008f200000, 0x000000008f300000|  0%| F|  |TAMS 0x000000008f200000| PB 0x000000008f200000| Untracked 
| 243|0x000000008f300000, 0x000000008f400000, 0x000000008f400000|100%| O|  |TAMS 0x000000008f300000| PB 0x000000008f300000| Untracked 
| 244|0x000000008f400000, 0x000000008f500000, 0x000000008f500000|100%| O|  |TAMS 0x000000008f400000| PB 0x000000008f400000| Untracked 
| 245|0x000000008f500000, 0x000000008f600000, 0x000000008f600000|100%| O|  |TAMS 0x000000008f500000| PB 0x000000008f500000| Untracked 
| 246|0x000000008f600000, 0x000000008f700000, 0x000000008f700000|100%| O|  |TAMS 0x000000008f600000| PB 0x000000008f600000| Untracked 
| 247|0x000000008f700000, 0x000000008f800000, 0x000000008f800000|100%|HS|  |TAMS 0x000000008f700000| PB 0x000000008f700000| Complete 
| 248|0x000000008f800000, 0x000000008f900000, 0x000000008f900000|100%|HC|  |TAMS 0x000000008f800000| PB 0x000000008f800000| Complete 
| 249|0x000000008f900000, 0x000000008fa00000, 0x000000008fa00000|100%|HS|  |TAMS 0x000000008f900000| PB 0x000000008f900000| Complete 
| 250|0x000000008fa00000, 0x000000008fb00000, 0x000000008fb00000|100%| O|  |TAMS 0x000000008fa00000| PB 0x000000008fa00000| Untracked 
| 251|0x000000008fb00000, 0x000000008fc00000, 0x000000008fc00000|100%|HS|  |TAMS 0x000000008fb00000| PB 0x000000008fb00000| Complete 
| 252|0x000000008fc00000, 0x000000008fd00000, 0x000000008fd00000|100%|HS|  |TAMS 0x000000008fc00000| PB 0x000000008fc00000| Complete 
| 253|0x000000008fd00000, 0x000000008fe00000, 0x000000008fe00000|100%|HC|  |TAMS 0x000000008fd00000| PB 0x000000008fd00000| Complete 
| 254|0x000000008fe00000, 0x000000008ff00000, 0x000000008ff00000|100%| O|  |TAMS 0x000000008fe00000| PB 0x000000008fe00000| Untracked 
| 255|0x000000008ff00000, 0x000000008ff00000, 0x0000000090000000|  0%| F|  |TAMS 0x000000008ff00000| PB 0x000000008ff00000| Untracked 
| 256|0x0000000090000000, 0x0000000090100000, 0x0000000090100000|100%| O|  |TAMS 0x0000000090000000| PB 0x0000000090000000| Untracked 
| 257|0x0000000090100000, 0x0000000090200000, 0x0000000090200000|100%| O|  |TAMS 0x0000000090100000| PB 0x0000000090100000| Untracked 
| 258|0x0000000090200000, 0x0000000090300000, 0x0000000090300000|100%| O|  |TAMS 0x0000000090200000| PB 0x0000000090200000| Untracked 
| 259|0x0000000090300000, 0x0000000090400000, 0x0000000090400000|100%| O|  |TAMS 0x0000000090300000| PB 0x0000000090300000| Untracked 
| 260|0x0000000090400000, 0x0000000090500000, 0x0000000090500000|100%| O|  |TAMS 0x0000000090400000| PB 0x0000000090400000| Untracked 
| 261|0x0000000090500000, 0x0000000090600000, 0x0000000090600000|100%| O|  |TAMS 0x0000000090500000| PB 0x0000000090500000| Untracked 
| 262|0x0000000090600000, 0x0000000090700000, 0x0000000090700000|100%| O|  |TAMS 0x0000000090600000| PB 0x0000000090600000| Untracked 
| 263|0x0000000090700000, 0x0000000090800000, 0x0000000090800000|100%| O|  |TAMS 0x0000000090700000| PB 0x0000000090700000| Untracked 
| 264|0x0000000090800000, 0x0000000090900000, 0x0000000090900000|100%| O|  |TAMS 0x0000000090800000| PB 0x0000000090800000| Untracked 
| 265|0x0000000090900000, 0x0000000090900000, 0x0000000090a00000|  0%| F|  |TAMS 0x0000000090900000| PB 0x0000000090900000| Untracked 
| 266|0x0000000090a00000, 0x0000000090b00000, 0x0000000090b00000|100%| O|  |TAMS 0x0000000090a00000| PB 0x0000000090a00000| Untracked 
| 267|0x0000000090b00000, 0x0000000090c00000, 0x0000000090c00000|100%| O|  |TAMS 0x0000000090b00000| PB 0x0000000090b00000| Untracked 
| 268|0x0000000090c00000, 0x0000000090d00000, 0x0000000090d00000|100%| O|  |TAMS 0x0000000090c00000| PB 0x0000000090c00000| Untracked 
| 269|0x0000000090d00000, 0x0000000090e00000, 0x0000000090e00000|100%| O|  |TAMS 0x0000000090d00000| PB 0x0000000090d00000| Untracked 
| 270|0x0000000090e00000, 0x0000000090f00000, 0x0000000090f00000|100%| O|  |TAMS 0x0000000090e00000| PB 0x0000000090e00000| Untracked 
| 271|0x0000000090f00000, 0x0000000091000000, 0x0000000091000000|100%| O|  |TAMS 0x0000000090f00000| PB 0x0000000090f00000| Untracked 
| 272|0x0000000091000000, 0x0000000091100000, 0x0000000091100000|100%| O|  |TAMS 0x0000000091000000| PB 0x0000000091000000| Untracked 
| 273|0x0000000091100000, 0x0000000091200000, 0x0000000091200000|100%| O|  |TAMS 0x0000000091100000| PB 0x0000000091100000| Untracked 
| 274|0x0000000091200000, 0x0000000091300000, 0x0000000091300000|100%| O|  |TAMS 0x0000000091200000| PB 0x0000000091200000| Untracked 
| 275|0x0000000091300000, 0x0000000091400000, 0x0000000091400000|100%| O|  |TAMS 0x0000000091300000| PB 0x0000000091300000| Untracked 
| 276|0x0000000091400000, 0x0000000091500000, 0x0000000091500000|100%| O|  |TAMS 0x0000000091400000| PB 0x0000000091400000| Untracked 
| 277|0x0000000091500000, 0x0000000091600000, 0x0000000091600000|100%| O|  |TAMS 0x0000000091500000| PB 0x0000000091500000| Untracked 
| 278|0x0000000091600000, 0x0000000091700000, 0x0000000091700000|100%|HS|  |TAMS 0x0000000091600000| PB 0x0000000091600000| Complete 
| 279|0x0000000091700000, 0x0000000091800000, 0x0000000091800000|100%|HC|  |TAMS 0x0000000091700000| PB 0x0000000091700000| Complete 
| 280|0x0000000091800000, 0x0000000091900000, 0x0000000091900000|100%|HS|  |TAMS 0x0000000091800000| PB 0x0000000091800000| Complete 
| 281|0x0000000091900000, 0x0000000091a00000, 0x0000000091a00000|100%|HS|  |TAMS 0x0000000091900000| PB 0x0000000091900000| Complete 
| 282|0x0000000091a00000, 0x0000000091b00000, 0x0000000091b00000|100%|HC|  |TAMS 0x0000000091a00000| PB 0x0000000091a00000| Complete 
| 283|0x0000000091b00000, 0x0000000091c00000, 0x0000000091c00000|100%|HS|  |TAMS 0x0000000091b00000| PB 0x0000000091b00000| Complete 
| 284|0x0000000091c00000, 0x0000000091d00000, 0x0000000091d00000|100%|HS|  |TAMS 0x0000000091c00000| PB 0x0000000091c00000| Complete 
| 285|0x0000000091d00000, 0x0000000091e00000, 0x0000000091e00000|100%|HS|  |TAMS 0x0000000091d00000| PB 0x0000000091d00000| Complete 
| 286|0x0000000091e00000, 0x0000000091f00000, 0x0000000091f00000|100%|HS|  |TAMS 0x0000000091e00000| PB 0x0000000091e00000| Complete 
| 287|0x0000000091f00000, 0x0000000092000000, 0x0000000092000000|100%|HC|  |TAMS 0x0000000091f00000| PB 0x0000000091f00000| Complete 
| 288|0x0000000092000000, 0x0000000092100000, 0x0000000092100000|100%|HS|  |TAMS 0x0000000092000000| PB 0x0000000092000000| Complete 
| 289|0x0000000092100000, 0x0000000092200000, 0x0000000092200000|100%|HS|  |TAMS 0x0000000092100000| PB 0x0000000092100000| Complete 
| 290|0x0000000092200000, 0x0000000092300000, 0x0000000092300000|100%|HS|  |TAMS 0x0000000092200000| PB 0x0000000092200000| Complete 
| 291|0x0000000092300000, 0x0000000092400000, 0x0000000092400000|100%|HS|  |TAMS 0x0000000092300000| PB 0x0000000092300000| Complete 
| 292|0x0000000092400000, 0x0000000092500000, 0x0000000092500000|100%|HC|  |TAMS 0x0000000092400000| PB 0x0000000092400000| Complete 
| 293|0x0000000092500000, 0x0000000092500000, 0x0000000092600000|  0%| F|  |TAMS 0x0000000092500000| PB 0x0000000092500000| Untracked 
| 294|0x0000000092600000, 0x0000000092600000, 0x0000000092700000|  0%| F|  |TAMS 0x0000000092600000| PB 0x0000000092600000| Untracked 
| 295|0x0000000092700000, 0x0000000092800000, 0x0000000092800000|100%|HS|  |TAMS 0x0000000092700000| PB 0x0000000092700000| Complete 
| 296|0x0000000092800000, 0x0000000092900000, 0x0000000092900000|100%| O|  |TAMS 0x0000000092800000| PB 0x0000000092800000| Untracked 
| 297|0x0000000092900000, 0x0000000092a00000, 0x0000000092a00000|100%| O|  |TAMS 0x0000000092900000| PB 0x0000000092900000| Untracked 
| 298|0x0000000092a00000, 0x0000000092b00000, 0x0000000092b00000|100%| O|  |TAMS 0x0000000092a00000| PB 0x0000000092a00000| Untracked 
| 299|0x0000000092b00000, 0x0000000092c00000, 0x0000000092c00000|100%| O|  |TAMS 0x0000000092b00000| PB 0x0000000092b00000| Untracked 
| 300|0x0000000092c00000, 0x0000000092d00000, 0x0000000092d00000|100%| O|  |TAMS 0x0000000092c00000| PB 0x0000000092c00000| Untracked 
| 301|0x0000000092d00000, 0x0000000092d00000, 0x0000000092e00000|  0%| F|  |TAMS 0x0000000092d00000| PB 0x0000000092d00000| Untracked 
| 302|0x0000000092e00000, 0x0000000092f00000, 0x0000000092f00000|100%|HS|  |TAMS 0x0000000092e00000| PB 0x0000000092e00000| Complete 
| 303|0x0000000092f00000, 0x0000000093000000, 0x0000000093000000|100%|HS|  |TAMS 0x0000000092f00000| PB 0x0000000092f00000| Complete 
| 304|0x0000000093000000, 0x0000000093100000, 0x0000000093100000|100%|HS|  |TAMS 0x0000000093000000| PB 0x0000000093000000| Complete 
| 305|0x0000000093100000, 0x0000000093200000, 0x0000000093200000|100%|HS|  |TAMS 0x0000000093100000| PB 0x0000000093100000| Complete 
| 306|0x0000000093200000, 0x0000000093300000, 0x0000000093300000|100%| O|  |TAMS 0x0000000093200000| PB 0x0000000093200000| Untracked 
| 307|0x0000000093300000, 0x0000000093400000, 0x0000000093400000|100%|HS|  |TAMS 0x0000000093300000| PB 0x0000000093300000| Complete 
| 308|0x0000000093400000, 0x0000000093500000, 0x0000000093500000|100%|HS|  |TAMS 0x0000000093400000| PB 0x0000000093400000| Complete 
| 309|0x0000000093500000, 0x0000000093600000, 0x0000000093600000|100%|HS|  |TAMS 0x0000000093500000| PB 0x0000000093500000| Complete 
| 310|0x0000000093600000, 0x0000000093700000, 0x0000000093700000|100%|HC|  |TAMS 0x0000000093600000| PB 0x0000000093600000| Complete 
| 311|0x0000000093700000, 0x0000000093700000, 0x0000000093800000|  0%| F|  |TAMS 0x0000000093700000| PB 0x0000000093700000| Untracked 
| 312|0x0000000093800000, 0x0000000093800000, 0x0000000093900000|  0%| F|  |TAMS 0x0000000093800000| PB 0x0000000093800000| Untracked 
| 313|0x0000000093900000, 0x0000000093900000, 0x0000000093a00000|  0%| F|  |TAMS 0x0000000093900000| PB 0x0000000093900000| Untracked 
| 314|0x0000000093a00000, 0x0000000093a00000, 0x0000000093b00000|  0%| F|  |TAMS 0x0000000093a00000| PB 0x0000000093a00000| Untracked 
| 315|0x0000000093b00000, 0x0000000093b00000, 0x0000000093c00000|  0%| F|  |TAMS 0x0000000093b00000| PB 0x0000000093b00000| Untracked 
| 316|0x0000000093c00000, 0x0000000093c00000, 0x0000000093d00000|  0%| F|  |TAMS 0x0000000093c00000| PB 0x0000000093c00000| Untracked 
| 317|0x0000000093d00000, 0x0000000093d00000, 0x0000000093e00000|  0%| F|  |TAMS 0x0000000093d00000| PB 0x0000000093d00000| Untracked 
| 318|0x0000000093e00000, 0x0000000093e00000, 0x0000000093f00000|  0%| F|  |TAMS 0x0000000093e00000| PB 0x0000000093e00000| Untracked 
| 319|0x0000000093f00000, 0x0000000093f00000, 0x0000000094000000|  0%| F|  |TAMS 0x0000000093f00000| PB 0x0000000093f00000| Untracked 
| 320|0x0000000094000000, 0x0000000094000000, 0x0000000094100000|  0%| F|  |TAMS 0x0000000094000000| PB 0x0000000094000000| Untracked 
| 321|0x0000000094100000, 0x0000000094100000, 0x0000000094200000|  0%| F|  |TAMS 0x0000000094100000| PB 0x0000000094100000| Untracked 
| 322|0x0000000094200000, 0x0000000094200000, 0x0000000094300000|  0%| F|  |TAMS 0x0000000094200000| PB 0x0000000094200000| Untracked 
| 323|0x0000000094300000, 0x0000000094300000, 0x0000000094400000|  0%| F|  |TAMS 0x0000000094300000| PB 0x0000000094300000| Untracked 
| 324|0x0000000094400000, 0x0000000094400000, 0x0000000094500000|  0%| F|  |TAMS 0x0000000094400000| PB 0x0000000094400000| Untracked 
| 325|0x0000000094500000, 0x0000000094500000, 0x0000000094600000|  0%| F|  |TAMS 0x0000000094500000| PB 0x0000000094500000| Untracked 
| 326|0x0000000094600000, 0x0000000094600000, 0x0000000094700000|  0%| F|  |TAMS 0x0000000094600000| PB 0x0000000094600000| Untracked 
| 327|0x0000000094700000, 0x0000000094700000, 0x0000000094800000|  0%| F|  |TAMS 0x0000000094700000| PB 0x0000000094700000| Untracked 
| 328|0x0000000094800000, 0x0000000094900000, 0x0000000094900000|100%| O|  |TAMS 0x0000000094800000| PB 0x0000000094800000| Untracked 
| 329|0x0000000094900000, 0x0000000094a00000, 0x0000000094a00000|100%| O|  |TAMS 0x0000000094900000| PB 0x0000000094900000| Untracked 
| 330|0x0000000094a00000, 0x0000000094b00000, 0x0000000094b00000|100%| O|  |TAMS 0x0000000094a00000| PB 0x0000000094a00000| Untracked 
| 331|0x0000000094b00000, 0x0000000094c00000, 0x0000000094c00000|100%| O|  |TAMS 0x0000000094b00000| PB 0x0000000094b00000| Untracked 
| 332|0x0000000094c00000, 0x0000000094d00000, 0x0000000094d00000|100%| O|  |TAMS 0x0000000094c00000| PB 0x0000000094c00000| Untracked 
| 333|0x0000000094d00000, 0x0000000094d00000, 0x0000000094e00000|  0%| F|  |TAMS 0x0000000094d00000| PB 0x0000000094d00000| Untracked 
| 334|0x0000000094e00000, 0x0000000094e00000, 0x0000000094f00000|  0%| F|  |TAMS 0x0000000094e00000| PB 0x0000000094e00000| Untracked 
| 335|0x0000000094f00000, 0x0000000094f00000, 0x0000000095000000|  0%| F|  |TAMS 0x0000000094f00000| PB 0x0000000094f00000| Untracked 
| 336|0x0000000095000000, 0x0000000095000000, 0x0000000095100000|  0%| F|  |TAMS 0x0000000095000000| PB 0x0000000095000000| Untracked 
| 337|0x0000000095100000, 0x0000000095100000, 0x0000000095200000|  0%| F|  |TAMS 0x0000000095100000| PB 0x0000000095100000| Untracked 
| 338|0x0000000095200000, 0x0000000095200000, 0x0000000095300000|  0%| F|  |TAMS 0x0000000095200000| PB 0x0000000095200000| Untracked 
| 339|0x0000000095300000, 0x0000000095300000, 0x0000000095400000|  0%| F|  |TAMS 0x0000000095300000| PB 0x0000000095300000| Untracked 
| 340|0x0000000095400000, 0x0000000095400000, 0x0000000095500000|  0%| F|  |TAMS 0x0000000095400000| PB 0x0000000095400000| Untracked 
| 341|0x0000000095500000, 0x0000000095500000, 0x0000000095600000|  0%| F|  |TAMS 0x0000000095500000| PB 0x0000000095500000| Untracked 
| 342|0x0000000095600000, 0x0000000095600000, 0x0000000095700000|  0%| F|  |TAMS 0x0000000095600000| PB 0x0000000095600000| Untracked 
| 343|0x0000000095700000, 0x0000000095700000, 0x0000000095800000|  0%| F|  |TAMS 0x0000000095700000| PB 0x0000000095700000| Untracked 
| 344|0x0000000095800000, 0x0000000095800000, 0x0000000095900000|  0%| F|  |TAMS 0x0000000095800000| PB 0x0000000095800000| Untracked 
| 345|0x0000000095900000, 0x0000000095900000, 0x0000000095a00000|  0%| F|  |TAMS 0x0000000095900000| PB 0x0000000095900000| Untracked 
| 346|0x0000000095a00000, 0x0000000095b00000, 0x0000000095b00000|100%| O|  |TAMS 0x0000000095a00000| PB 0x0000000095a00000| Untracked 
| 347|0x0000000095b00000, 0x0000000095c00000, 0x0000000095c00000|100%| O|  |TAMS 0x0000000095b00000| PB 0x0000000095b00000| Untracked 
| 348|0x0000000095c00000, 0x0000000095d00000, 0x0000000095d00000|100%| O|  |TAMS 0x0000000095c00000| PB 0x0000000095c00000| Untracked 
| 349|0x0000000095d00000, 0x0000000095d80140, 0x0000000095e00000| 50%| O|  |TAMS 0x0000000095d00000| PB 0x0000000095d00000| Untracked 
| 350|0x0000000095e00000, 0x0000000095e00000, 0x0000000095f00000|  0%| F|  |TAMS 0x0000000095e00000| PB 0x0000000095e00000| Untracked 
| 351|0x0000000095f00000, 0x0000000095f00000, 0x0000000096000000|  0%| F|  |TAMS 0x0000000095f00000| PB 0x0000000095f00000| Untracked 
| 352|0x0000000096000000, 0x0000000096000000, 0x0000000096100000|  0%| F|  |TAMS 0x0000000096000000| PB 0x0000000096000000| Untracked 
| 353|0x0000000096100000, 0x0000000096100000, 0x0000000096200000|  0%| F|  |TAMS 0x0000000096100000| PB 0x0000000096100000| Untracked 
| 354|0x0000000096200000, 0x0000000096200000, 0x0000000096300000|  0%| F|  |TAMS 0x0000000096200000| PB 0x0000000096200000| Untracked 
| 355|0x0000000096300000, 0x0000000096300000, 0x0000000096400000|  0%| F|  |TAMS 0x0000000096300000| PB 0x0000000096300000| Untracked 
| 356|0x0000000096400000, 0x0000000096400000, 0x0000000096500000|  0%| F|  |TAMS 0x0000000096400000| PB 0x0000000096400000| Untracked 
| 357|0x0000000096500000, 0x0000000096500000, 0x0000000096600000|  0%| F|  |TAMS 0x0000000096500000| PB 0x0000000096500000| Untracked 
| 358|0x0000000096600000, 0x0000000096600000, 0x0000000096700000|  0%| F|  |TAMS 0x0000000096600000| PB 0x0000000096600000| Untracked 
| 359|0x0000000096700000, 0x0000000096700000, 0x0000000096800000|  0%| F|  |TAMS 0x0000000096700000| PB 0x0000000096700000| Untracked 
| 360|0x0000000096800000, 0x0000000096800000, 0x0000000096900000|  0%| F|  |TAMS 0x0000000096800000| PB 0x0000000096800000| Untracked 
| 361|0x0000000096900000, 0x0000000096900000, 0x0000000096a00000|  0%| F|  |TAMS 0x0000000096900000| PB 0x0000000096900000| Untracked 
| 362|0x0000000096a00000, 0x0000000096a00000, 0x0000000096b00000|  0%| F|  |TAMS 0x0000000096a00000| PB 0x0000000096a00000| Untracked 
| 363|0x0000000096b00000, 0x0000000096b00000, 0x0000000096c00000|  0%| F|  |TAMS 0x0000000096b00000| PB 0x0000000096b00000| Untracked 
| 364|0x0000000096c00000, 0x0000000096c00000, 0x0000000096d00000|  0%| F|  |TAMS 0x0000000096c00000| PB 0x0000000096c00000| Untracked 
| 365|0x0000000096d00000, 0x0000000096d00000, 0x0000000096e00000|  0%| F|  |TAMS 0x0000000096d00000| PB 0x0000000096d00000| Untracked 
| 366|0x0000000096e00000, 0x0000000096e00000, 0x0000000096f00000|  0%| F|  |TAMS 0x0000000096e00000| PB 0x0000000096e00000| Untracked 
| 367|0x0000000096f00000, 0x0000000096f00000, 0x0000000097000000|  0%| F|  |TAMS 0x0000000096f00000| PB 0x0000000096f00000| Untracked 
| 368|0x0000000097000000, 0x0000000097000000, 0x0000000097100000|  0%| F|  |TAMS 0x0000000097000000| PB 0x0000000097000000| Untracked 
| 369|0x0000000097100000, 0x0000000097100000, 0x0000000097200000|  0%| F|  |TAMS 0x0000000097100000| PB 0x0000000097100000| Untracked 
| 370|0x0000000097200000, 0x0000000097200000, 0x0000000097300000|  0%| F|  |TAMS 0x0000000097200000| PB 0x0000000097200000| Untracked 
| 371|0x0000000097300000, 0x0000000097300000, 0x0000000097400000|  0%| F|  |TAMS 0x0000000097300000| PB 0x0000000097300000| Untracked 
| 372|0x0000000097400000, 0x0000000097400000, 0x0000000097500000|  0%| F|  |TAMS 0x0000000097400000| PB 0x0000000097400000| Untracked 
| 373|0x0000000097500000, 0x0000000097500000, 0x0000000097600000|  0%| F|  |TAMS 0x0000000097500000| PB 0x0000000097500000| Untracked 
| 374|0x0000000097600000, 0x0000000097600000, 0x0000000097700000|  0%| F|  |TAMS 0x0000000097600000| PB 0x0000000097600000| Untracked 
| 375|0x0000000097700000, 0x0000000097700000, 0x0000000097800000|  0%| F|  |TAMS 0x0000000097700000| PB 0x0000000097700000| Untracked 
| 376|0x0000000097800000, 0x0000000097800000, 0x0000000097900000|  0%| F|  |TAMS 0x0000000097800000| PB 0x0000000097800000| Untracked 
| 377|0x0000000097900000, 0x0000000097900000, 0x0000000097a00000|  0%| F|  |TAMS 0x0000000097900000| PB 0x0000000097900000| Untracked 
| 378|0x0000000097a00000, 0x0000000097a00000, 0x0000000097b00000|  0%| F|  |TAMS 0x0000000097a00000| PB 0x0000000097a00000| Untracked 
| 379|0x0000000097b00000, 0x0000000097b00000, 0x0000000097c00000|  0%| F|  |TAMS 0x0000000097b00000| PB 0x0000000097b00000| Untracked 
| 380|0x0000000097c00000, 0x0000000097c00000, 0x0000000097d00000|  0%| F|  |TAMS 0x0000000097c00000| PB 0x0000000097c00000| Untracked 
| 381|0x0000000097d00000, 0x0000000097d00000, 0x0000000097e00000|  0%| F|  |TAMS 0x0000000097d00000| PB 0x0000000097d00000| Untracked 
| 382|0x0000000097e00000, 0x0000000097e00000, 0x0000000097f00000|  0%| F|  |TAMS 0x0000000097e00000| PB 0x0000000097e00000| Untracked 
| 383|0x0000000097f00000, 0x0000000097f00000, 0x0000000098000000|  0%| F|  |TAMS 0x0000000097f00000| PB 0x0000000097f00000| Untracked 
| 384|0x0000000098000000, 0x0000000098000000, 0x0000000098100000|  0%| F|  |TAMS 0x0000000098000000| PB 0x0000000098000000| Untracked 
| 385|0x0000000098100000, 0x0000000098100000, 0x0000000098200000|  0%| F|  |TAMS 0x0000000098100000| PB 0x0000000098100000| Untracked 
| 386|0x0000000098200000, 0x0000000098200000, 0x0000000098300000|  0%| F|  |TAMS 0x0000000098200000| PB 0x0000000098200000| Untracked 
| 387|0x0000000098300000, 0x0000000098300000, 0x0000000098400000|  0%| F|  |TAMS 0x0000000098300000| PB 0x0000000098300000| Untracked 
| 388|0x0000000098400000, 0x0000000098400000, 0x0000000098500000|  0%| F|  |TAMS 0x0000000098400000| PB 0x0000000098400000| Untracked 
| 389|0x0000000098500000, 0x0000000098500000, 0x0000000098600000|  0%| F|  |TAMS 0x0000000098500000| PB 0x0000000098500000| Untracked 
| 390|0x0000000098600000, 0x0000000098600000, 0x0000000098700000|  0%| F|  |TAMS 0x0000000098600000| PB 0x0000000098600000| Untracked 
| 391|0x0000000098700000, 0x0000000098700000, 0x0000000098800000|  0%| F|  |TAMS 0x0000000098700000| PB 0x0000000098700000| Untracked 
| 392|0x0000000098800000, 0x0000000098800000, 0x0000000098900000|  0%| F|  |TAMS 0x0000000098800000| PB 0x0000000098800000| Untracked 
| 393|0x0000000098900000, 0x0000000098900000, 0x0000000098a00000|  0%| F|  |TAMS 0x0000000098900000| PB 0x0000000098900000| Untracked 
| 394|0x0000000098a00000, 0x0000000098a00000, 0x0000000098b00000|  0%| F|  |TAMS 0x0000000098a00000| PB 0x0000000098a00000| Untracked 
| 395|0x0000000098b00000, 0x0000000098b00000, 0x0000000098c00000|  0%| F|  |TAMS 0x0000000098b00000| PB 0x0000000098b00000| Untracked 
| 396|0x0000000098c00000, 0x0000000098c46840, 0x0000000098d00000| 27%| S|CS|TAMS 0x0000000098c00000| PB 0x0000000098c00000| Complete 
| 397|0x0000000098d00000, 0x0000000098e00000, 0x0000000098e00000|100%| S|CS|TAMS 0x0000000098d00000| PB 0x0000000098d00000| Complete 
| 398|0x0000000098e00000, 0x0000000098f00000, 0x0000000098f00000|100%| S|CS|TAMS 0x0000000098e00000| PB 0x0000000098e00000| Complete 
| 399|0x0000000098f00000, 0x0000000099000000, 0x0000000099000000|100%| S|CS|TAMS 0x0000000098f00000| PB 0x0000000098f00000| Complete 
| 400|0x0000000099000000, 0x0000000099100000, 0x0000000099100000|100%| S|CS|TAMS 0x0000000099000000| PB 0x0000000099000000| Complete 
| 401|0x0000000099100000, 0x0000000099200000, 0x0000000099200000|100%| S|CS|TAMS 0x0000000099100000| PB 0x0000000099100000| Complete 
| 402|0x0000000099200000, 0x0000000099300000, 0x0000000099300000|100%| S|CS|TAMS 0x0000000099200000| PB 0x0000000099200000| Complete 
| 403|0x0000000099300000, 0x0000000099400000, 0x0000000099400000|100%| S|CS|TAMS 0x0000000099300000| PB 0x0000000099300000| Complete 
| 404|0x0000000099400000, 0x0000000099500000, 0x0000000099500000|100%| S|CS|TAMS 0x0000000099400000| PB 0x0000000099400000| Complete 
| 405|0x0000000099500000, 0x0000000099600000, 0x0000000099600000|100%| S|CS|TAMS 0x0000000099500000| PB 0x0000000099500000| Complete 
| 406|0x0000000099600000, 0x0000000099700000, 0x0000000099700000|100%| S|CS|TAMS 0x0000000099600000| PB 0x0000000099600000| Complete 
| 407|0x0000000099700000, 0x0000000099800000, 0x0000000099800000|100%| S|CS|TAMS 0x0000000099700000| PB 0x0000000099700000| Complete 
| 408|0x0000000099800000, 0x0000000099900000, 0x0000000099900000|100%| S|CS|TAMS 0x0000000099800000| PB 0x0000000099800000| Complete 
| 409|0x0000000099900000, 0x0000000099a00000, 0x0000000099a00000|100%| S|CS|TAMS 0x0000000099900000| PB 0x0000000099900000| Complete 
| 410|0x0000000099a00000, 0x0000000099b00000, 0x0000000099b00000|100%| S|CS|TAMS 0x0000000099a00000| PB 0x0000000099a00000| Complete 
| 411|0x0000000099b00000, 0x0000000099c00000, 0x0000000099c00000|100%| S|CS|TAMS 0x0000000099b00000| PB 0x0000000099b00000| Complete 
| 412|0x0000000099c00000, 0x0000000099d00000, 0x0000000099d00000|100%| S|CS|TAMS 0x0000000099c00000| PB 0x0000000099c00000| Complete 
| 413|0x0000000099d00000, 0x0000000099e00000, 0x0000000099e00000|100%| S|CS|TAMS 0x0000000099d00000| PB 0x0000000099d00000| Complete 
| 414|0x0000000099e00000, 0x0000000099e00000, 0x0000000099f00000|  0%| F|  |TAMS 0x0000000099e00000| PB 0x0000000099e00000| Untracked 
| 415|0x0000000099f00000, 0x0000000099f00000, 0x000000009a000000|  0%| F|  |TAMS 0x0000000099f00000| PB 0x0000000099f00000| Untracked 
| 416|0x000000009a000000, 0x000000009a000000, 0x000000009a100000|  0%| F|  |TAMS 0x000000009a000000| PB 0x000000009a000000| Untracked 
| 417|0x000000009a100000, 0x000000009a100000, 0x000000009a200000|  0%| F|  |TAMS 0x000000009a100000| PB 0x000000009a100000| Untracked 
| 418|0x000000009a200000, 0x000000009a200000, 0x000000009a300000|  0%| F|  |TAMS 0x000000009a200000| PB 0x000000009a200000| Untracked 
| 419|0x000000009a300000, 0x000000009a300000, 0x000000009a400000|  0%| F|  |TAMS 0x000000009a300000| PB 0x000000009a300000| Untracked 
| 420|0x000000009a400000, 0x000000009a400000, 0x000000009a500000|  0%| F|  |TAMS 0x000000009a400000| PB 0x000000009a400000| Untracked 
| 421|0x000000009a500000, 0x000000009a500000, 0x000000009a600000|  0%| F|  |TAMS 0x000000009a500000| PB 0x000000009a500000| Untracked 
| 422|0x000000009a600000, 0x000000009a600000, 0x000000009a700000|  0%| F|  |TAMS 0x000000009a600000| PB 0x000000009a600000| Untracked 
| 423|0x000000009a700000, 0x000000009a700000, 0x000000009a800000|  0%| F|  |TAMS 0x000000009a700000| PB 0x000000009a700000| Untracked 
| 424|0x000000009a800000, 0x000000009a800000, 0x000000009a900000|  0%| F|  |TAMS 0x000000009a800000| PB 0x000000009a800000| Untracked 
| 425|0x000000009a900000, 0x000000009a900000, 0x000000009aa00000|  0%| F|  |TAMS 0x000000009a900000| PB 0x000000009a900000| Untracked 
| 426|0x000000009aa00000, 0x000000009aa00000, 0x000000009ab00000|  0%| F|  |TAMS 0x000000009aa00000| PB 0x000000009aa00000| Untracked 
| 427|0x000000009ab00000, 0x000000009ab00000, 0x000000009ac00000|  0%| F|  |TAMS 0x000000009ab00000| PB 0x000000009ab00000| Untracked 
| 428|0x000000009ac00000, 0x000000009ac00000, 0x000000009ad00000|  0%| F|  |TAMS 0x000000009ac00000| PB 0x000000009ac00000| Untracked 
| 429|0x000000009ad00000, 0x000000009ad00000, 0x000000009ae00000|  0%| F|  |TAMS 0x000000009ad00000| PB 0x000000009ad00000| Untracked 
| 430|0x000000009ae00000, 0x000000009ae00000, 0x000000009af00000|  0%| F|  |TAMS 0x000000009ae00000| PB 0x000000009ae00000| Untracked 
| 431|0x000000009af00000, 0x000000009af00000, 0x000000009b000000|  0%| F|  |TAMS 0x000000009af00000| PB 0x000000009af00000| Untracked 
| 432|0x000000009b000000, 0x000000009b000000, 0x000000009b100000|  0%| F|  |TAMS 0x000000009b000000| PB 0x000000009b000000| Untracked 
| 433|0x000000009b100000, 0x000000009b100000, 0x000000009b200000|  0%| F|  |TAMS 0x000000009b100000| PB 0x000000009b100000| Untracked 
| 434|0x000000009b200000, 0x000000009b200000, 0x000000009b300000|  0%| F|  |TAMS 0x000000009b200000| PB 0x000000009b200000| Untracked 
| 435|0x000000009b300000, 0x000000009b300000, 0x000000009b400000|  0%| F|  |TAMS 0x000000009b300000| PB 0x000000009b300000| Untracked 
| 436|0x000000009b400000, 0x000000009b400000, 0x000000009b500000|  0%| F|  |TAMS 0x000000009b400000| PB 0x000000009b400000| Untracked 
| 437|0x000000009b500000, 0x000000009b500000, 0x000000009b600000|  0%| F|  |TAMS 0x000000009b500000| PB 0x000000009b500000| Untracked 
| 438|0x000000009b600000, 0x000000009b600000, 0x000000009b700000|  0%| F|  |TAMS 0x000000009b600000| PB 0x000000009b600000| Untracked 
| 439|0x000000009b700000, 0x000000009b700000, 0x000000009b800000|  0%| F|  |TAMS 0x000000009b700000| PB 0x000000009b700000| Untracked 
| 440|0x000000009b800000, 0x000000009b800000, 0x000000009b900000|  0%| F|  |TAMS 0x000000009b800000| PB 0x000000009b800000| Untracked 
| 441|0x000000009b900000, 0x000000009b900000, 0x000000009ba00000|  0%| F|  |TAMS 0x000000009b900000| PB 0x000000009b900000| Untracked 
| 442|0x000000009ba00000, 0x000000009ba00000, 0x000000009bb00000|  0%| F|  |TAMS 0x000000009ba00000| PB 0x000000009ba00000| Untracked 
| 443|0x000000009bb00000, 0x000000009bb00000, 0x000000009bc00000|  0%| F|  |TAMS 0x000000009bb00000| PB 0x000000009bb00000| Untracked 
| 444|0x000000009bc00000, 0x000000009bc00000, 0x000000009bd00000|  0%| F|  |TAMS 0x000000009bc00000| PB 0x000000009bc00000| Untracked 
| 445|0x000000009bd00000, 0x000000009bd00000, 0x000000009be00000|  0%| F|  |TAMS 0x000000009bd00000| PB 0x000000009bd00000| Untracked 
| 446|0x000000009be00000, 0x000000009be00000, 0x000000009bf00000|  0%| F|  |TAMS 0x000000009be00000| PB 0x000000009be00000| Untracked 
| 447|0x000000009bf00000, 0x000000009bf00000, 0x000000009c000000|  0%| F|  |TAMS 0x000000009bf00000| PB 0x000000009bf00000| Untracked 
| 448|0x000000009c000000, 0x000000009c000000, 0x000000009c100000|  0%| F|  |TAMS 0x000000009c000000| PB 0x000000009c000000| Untracked 
| 449|0x000000009c100000, 0x000000009c100000, 0x000000009c200000|  0%| F|  |TAMS 0x000000009c100000| PB 0x000000009c100000| Untracked 
| 450|0x000000009c200000, 0x000000009c200000, 0x000000009c300000|  0%| F|  |TAMS 0x000000009c200000| PB 0x000000009c200000| Untracked 
| 451|0x000000009c300000, 0x000000009c300000, 0x000000009c400000|  0%| F|  |TAMS 0x000000009c300000| PB 0x000000009c300000| Untracked 
| 452|0x000000009c400000, 0x000000009c400000, 0x000000009c500000|  0%| F|  |TAMS 0x000000009c400000| PB 0x000000009c400000| Untracked 
| 453|0x000000009c500000, 0x000000009c500000, 0x000000009c600000|  0%| F|  |TAMS 0x000000009c500000| PB 0x000000009c500000| Untracked 
| 454|0x000000009c600000, 0x000000009c600000, 0x000000009c700000|  0%| F|  |TAMS 0x000000009c600000| PB 0x000000009c600000| Untracked 
| 455|0x000000009c700000, 0x000000009c700000, 0x000000009c800000|  0%| F|  |TAMS 0x000000009c700000| PB 0x000000009c700000| Untracked 
| 456|0x000000009c800000, 0x000000009c800000, 0x000000009c900000|  0%| F|  |TAMS 0x000000009c800000| PB 0x000000009c800000| Untracked 
| 457|0x000000009c900000, 0x000000009c900000, 0x000000009ca00000|  0%| F|  |TAMS 0x000000009c900000| PB 0x000000009c900000| Untracked 
| 458|0x000000009ca00000, 0x000000009ca00000, 0x000000009cb00000|  0%| F|  |TAMS 0x000000009ca00000| PB 0x000000009ca00000| Untracked 
| 459|0x000000009cb00000, 0x000000009cb00000, 0x000000009cc00000|  0%| F|  |TAMS 0x000000009cb00000| PB 0x000000009cb00000| Untracked 
| 460|0x000000009cc00000, 0x000000009cc00000, 0x000000009cd00000|  0%| F|  |TAMS 0x000000009cc00000| PB 0x000000009cc00000| Untracked 
| 461|0x000000009cd00000, 0x000000009cd00000, 0x000000009ce00000|  0%| F|  |TAMS 0x000000009cd00000| PB 0x000000009cd00000| Untracked 
| 462|0x000000009ce00000, 0x000000009ce00000, 0x000000009cf00000|  0%| F|  |TAMS 0x000000009ce00000| PB 0x000000009ce00000| Untracked 
| 463|0x000000009cf00000, 0x000000009cf00000, 0x000000009d000000|  0%| F|  |TAMS 0x000000009cf00000| PB 0x000000009cf00000| Untracked 
| 464|0x000000009d000000, 0x000000009d000000, 0x000000009d100000|  0%| F|  |TAMS 0x000000009d000000| PB 0x000000009d000000| Untracked 
| 465|0x000000009d100000, 0x000000009d100000, 0x000000009d200000|  0%| F|  |TAMS 0x000000009d100000| PB 0x000000009d100000| Untracked 
| 466|0x000000009d200000, 0x000000009d200000, 0x000000009d300000|  0%| F|  |TAMS 0x000000009d200000| PB 0x000000009d200000| Untracked 
| 467|0x000000009d300000, 0x000000009d300000, 0x000000009d400000|  0%| F|  |TAMS 0x000000009d300000| PB 0x000000009d300000| Untracked 
| 468|0x000000009d400000, 0x000000009d400000, 0x000000009d500000|  0%| F|  |TAMS 0x000000009d400000| PB 0x000000009d400000| Untracked 
| 469|0x000000009d500000, 0x000000009d500000, 0x000000009d600000|  0%| F|  |TAMS 0x000000009d500000| PB 0x000000009d500000| Untracked 
| 470|0x000000009d600000, 0x000000009d600000, 0x000000009d700000|  0%| F|  |TAMS 0x000000009d600000| PB 0x000000009d600000| Untracked 
| 471|0x000000009d700000, 0x000000009d700000, 0x000000009d800000|  0%| F|  |TAMS 0x000000009d700000| PB 0x000000009d700000| Untracked 
| 472|0x000000009d800000, 0x000000009d800000, 0x000000009d900000|  0%| F|  |TAMS 0x000000009d800000| PB 0x000000009d800000| Untracked 
| 473|0x000000009d900000, 0x000000009d900000, 0x000000009da00000|  0%| F|  |TAMS 0x000000009d900000| PB 0x000000009d900000| Untracked 
| 474|0x000000009da00000, 0x000000009da00000, 0x000000009db00000|  0%| F|  |TAMS 0x000000009da00000| PB 0x000000009da00000| Untracked 
| 475|0x000000009db00000, 0x000000009db00000, 0x000000009dc00000|  0%| F|  |TAMS 0x000000009db00000| PB 0x000000009db00000| Untracked 
| 476|0x000000009dc00000, 0x000000009dc00000, 0x000000009dd00000|  0%| F|  |TAMS 0x000000009dc00000| PB 0x000000009dc00000| Untracked 
| 477|0x000000009dd00000, 0x000000009dd00000, 0x000000009de00000|  0%| F|  |TAMS 0x000000009dd00000| PB 0x000000009dd00000| Untracked 
| 478|0x000000009de00000, 0x000000009de00000, 0x000000009df00000|  0%| F|  |TAMS 0x000000009de00000| PB 0x000000009de00000| Untracked 
| 479|0x000000009df00000, 0x000000009df00000, 0x000000009e000000|  0%| F|  |TAMS 0x000000009df00000| PB 0x000000009df00000| Untracked 
| 480|0x000000009e000000, 0x000000009e000000, 0x000000009e100000|  0%| F|  |TAMS 0x000000009e000000| PB 0x000000009e000000| Untracked 
| 481|0x000000009e100000, 0x000000009e100000, 0x000000009e200000|  0%| F|  |TAMS 0x000000009e100000| PB 0x000000009e100000| Untracked 
| 482|0x000000009e200000, 0x000000009e200000, 0x000000009e300000|  0%| F|  |TAMS 0x000000009e200000| PB 0x000000009e200000| Untracked 
| 483|0x000000009e300000, 0x000000009e300000, 0x000000009e400000|  0%| F|  |TAMS 0x000000009e300000| PB 0x000000009e300000| Untracked 
| 484|0x000000009e400000, 0x000000009e400000, 0x000000009e500000|  0%| F|  |TAMS 0x000000009e400000| PB 0x000000009e400000| Untracked 
| 485|0x000000009e500000, 0x000000009e500000, 0x000000009e600000|  0%| F|  |TAMS 0x000000009e500000| PB 0x000000009e500000| Untracked 
| 486|0x000000009e600000, 0x000000009e600000, 0x000000009e700000|  0%| F|  |TAMS 0x000000009e600000| PB 0x000000009e600000| Untracked 
| 487|0x000000009e700000, 0x000000009e700000, 0x000000009e800000|  0%| F|  |TAMS 0x000000009e700000| PB 0x000000009e700000| Untracked 
| 488|0x000000009e800000, 0x000000009e800000, 0x000000009e900000|  0%| F|  |TAMS 0x000000009e800000| PB 0x000000009e800000| Untracked 
| 489|0x000000009e900000, 0x000000009e900000, 0x000000009ea00000|  0%| F|  |TAMS 0x000000009e900000| PB 0x000000009e900000| Untracked 
| 490|0x000000009ea00000, 0x000000009ea00000, 0x000000009eb00000|  0%| F|  |TAMS 0x000000009ea00000| PB 0x000000009ea00000| Untracked 
| 491|0x000000009eb00000, 0x000000009eb00000, 0x000000009ec00000|  0%| F|  |TAMS 0x000000009eb00000| PB 0x000000009eb00000| Untracked 
| 492|0x000000009ec00000, 0x000000009ec00000, 0x000000009ed00000|  0%| F|  |TAMS 0x000000009ec00000| PB 0x000000009ec00000| Untracked 
| 493|0x000000009ed00000, 0x000000009ed00000, 0x000000009ee00000|  0%| F|  |TAMS 0x000000009ed00000| PB 0x000000009ed00000| Untracked 
| 494|0x000000009ee00000, 0x000000009ee00000, 0x000000009ef00000|  0%| F|  |TAMS 0x000000009ee00000| PB 0x000000009ee00000| Untracked 
| 495|0x000000009ef00000, 0x000000009ef00000, 0x000000009f000000|  0%| F|  |TAMS 0x000000009ef00000| PB 0x000000009ef00000| Untracked 
| 496|0x000000009f000000, 0x000000009f000000, 0x000000009f100000|  0%| F|  |TAMS 0x000000009f000000| PB 0x000000009f000000| Untracked 
| 497|0x000000009f100000, 0x000000009f100000, 0x000000009f200000|  0%| F|  |TAMS 0x000000009f100000| PB 0x000000009f100000| Untracked 
| 498|0x000000009f200000, 0x000000009f200000, 0x000000009f300000|  0%| F|  |TAMS 0x000000009f200000| PB 0x000000009f200000| Untracked 
| 499|0x000000009f300000, 0x000000009f300000, 0x000000009f400000|  0%| F|  |TAMS 0x000000009f300000| PB 0x000000009f300000| Untracked 
| 500|0x000000009f400000, 0x000000009f400000, 0x000000009f500000|  0%| F|  |TAMS 0x000000009f400000| PB 0x000000009f400000| Untracked 
| 501|0x000000009f500000, 0x000000009f500000, 0x000000009f600000|  0%| F|  |TAMS 0x000000009f500000| PB 0x000000009f500000| Untracked 
| 502|0x000000009f600000, 0x000000009f600000, 0x000000009f700000|  0%| F|  |TAMS 0x000000009f600000| PB 0x000000009f600000| Untracked 
| 503|0x000000009f700000, 0x000000009f700000, 0x000000009f800000|  0%| F|  |TAMS 0x000000009f700000| PB 0x000000009f700000| Untracked 
| 504|0x000000009f800000, 0x000000009f800000, 0x000000009f900000|  0%| F|  |TAMS 0x000000009f800000| PB 0x000000009f800000| Untracked 
| 505|0x000000009f900000, 0x000000009f900000, 0x000000009fa00000|  0%| F|  |TAMS 0x000000009f900000| PB 0x000000009f900000| Untracked 
| 506|0x000000009fa00000, 0x000000009fa00000, 0x000000009fb00000|  0%| F|  |TAMS 0x000000009fa00000| PB 0x000000009fa00000| Untracked 
| 507|0x000000009fb00000, 0x000000009fb00000, 0x000000009fc00000|  0%| F|  |TAMS 0x000000009fb00000| PB 0x000000009fb00000| Untracked 
| 508|0x000000009fc00000, 0x000000009fc00000, 0x000000009fd00000|  0%| F|  |TAMS 0x000000009fc00000| PB 0x000000009fc00000| Untracked 
| 509|0x000000009fd00000, 0x000000009fd00000, 0x000000009fe00000|  0%| F|  |TAMS 0x000000009fd00000| PB 0x000000009fd00000| Untracked 
| 510|0x000000009fe00000, 0x000000009fe00000, 0x000000009ff00000|  0%| F|  |TAMS 0x000000009fe00000| PB 0x000000009fe00000| Untracked 
| 511|0x000000009ff00000, 0x000000009ff00000, 0x00000000a0000000|  0%| F|  |TAMS 0x000000009ff00000| PB 0x000000009ff00000| Untracked 
| 512|0x00000000a0000000, 0x00000000a0000000, 0x00000000a0100000|  0%| F|  |TAMS 0x00000000a0000000| PB 0x00000000a0000000| Untracked 
| 513|0x00000000a0100000, 0x00000000a0100000, 0x00000000a0200000|  0%| F|  |TAMS 0x00000000a0100000| PB 0x00000000a0100000| Untracked 
| 514|0x00000000a0200000, 0x00000000a0200000, 0x00000000a0300000|  0%| F|  |TAMS 0x00000000a0200000| PB 0x00000000a0200000| Untracked 
| 515|0x00000000a0300000, 0x00000000a0300000, 0x00000000a0400000|  0%| F|  |TAMS 0x00000000a0300000| PB 0x00000000a0300000| Untracked 
| 516|0x00000000a0400000, 0x00000000a0400000, 0x00000000a0500000|  0%| F|  |TAMS 0x00000000a0400000| PB 0x00000000a0400000| Untracked 
| 517|0x00000000a0500000, 0x00000000a0500000, 0x00000000a0600000|  0%| F|  |TAMS 0x00000000a0500000| PB 0x00000000a0500000| Untracked 
| 518|0x00000000a0600000, 0x00000000a0600000, 0x00000000a0700000|  0%| F|  |TAMS 0x00000000a0600000| PB 0x00000000a0600000| Untracked 
| 519|0x00000000a0700000, 0x00000000a0700000, 0x00000000a0800000|  0%| F|  |TAMS 0x00000000a0700000| PB 0x00000000a0700000| Untracked 
| 520|0x00000000a0800000, 0x00000000a0800000, 0x00000000a0900000|  0%| F|  |TAMS 0x00000000a0800000| PB 0x00000000a0800000| Untracked 
| 521|0x00000000a0900000, 0x00000000a0900000, 0x00000000a0a00000|  0%| F|  |TAMS 0x00000000a0900000| PB 0x00000000a0900000| Untracked 
| 522|0x00000000a0a00000, 0x00000000a0a00000, 0x00000000a0b00000|  0%| F|  |TAMS 0x00000000a0a00000| PB 0x00000000a0a00000| Untracked 
| 523|0x00000000a0b00000, 0x00000000a0b00000, 0x00000000a0c00000|  0%| F|  |TAMS 0x00000000a0b00000| PB 0x00000000a0b00000| Untracked 
| 524|0x00000000a0c00000, 0x00000000a0c00000, 0x00000000a0d00000|  0%| F|  |TAMS 0x00000000a0c00000| PB 0x00000000a0c00000| Untracked 
| 525|0x00000000a0d00000, 0x00000000a0d00000, 0x00000000a0e00000|  0%| F|  |TAMS 0x00000000a0d00000| PB 0x00000000a0d00000| Untracked 
| 526|0x00000000a0e00000, 0x00000000a0e00000, 0x00000000a0f00000|  0%| F|  |TAMS 0x00000000a0e00000| PB 0x00000000a0e00000| Untracked 
| 527|0x00000000a0f00000, 0x00000000a0f00000, 0x00000000a1000000|  0%| F|  |TAMS 0x00000000a0f00000| PB 0x00000000a0f00000| Untracked 
| 528|0x00000000a1000000, 0x00000000a1000000, 0x00000000a1100000|  0%| F|  |TAMS 0x00000000a1000000| PB 0x00000000a1000000| Untracked 
| 529|0x00000000a1100000, 0x00000000a1100000, 0x00000000a1200000|  0%| F|  |TAMS 0x00000000a1100000| PB 0x00000000a1100000| Untracked 
| 530|0x00000000a1200000, 0x00000000a1200000, 0x00000000a1300000|  0%| F|  |TAMS 0x00000000a1200000| PB 0x00000000a1200000| Untracked 
| 531|0x00000000a1300000, 0x00000000a1300000, 0x00000000a1400000|  0%| F|  |TAMS 0x00000000a1300000| PB 0x00000000a1300000| Untracked 
| 532|0x00000000a1400000, 0x00000000a1400000, 0x00000000a1500000|  0%| F|  |TAMS 0x00000000a1400000| PB 0x00000000a1400000| Untracked 
| 533|0x00000000a1500000, 0x00000000a1500000, 0x00000000a1600000|  0%| F|  |TAMS 0x00000000a1500000| PB 0x00000000a1500000| Untracked 
| 534|0x00000000a1600000, 0x00000000a1600000, 0x00000000a1700000|  0%| F|  |TAMS 0x00000000a1600000| PB 0x00000000a1600000| Untracked 
| 535|0x00000000a1700000, 0x00000000a1700000, 0x00000000a1800000|  0%| F|  |TAMS 0x00000000a1700000| PB 0x00000000a1700000| Untracked 
| 536|0x00000000a1800000, 0x00000000a1800000, 0x00000000a1900000|  0%| F|  |TAMS 0x00000000a1800000| PB 0x00000000a1800000| Untracked 
| 537|0x00000000a1900000, 0x00000000a1900000, 0x00000000a1a00000|  0%| F|  |TAMS 0x00000000a1900000| PB 0x00000000a1900000| Untracked 
| 538|0x00000000a1a00000, 0x00000000a1a00000, 0x00000000a1b00000|  0%| F|  |TAMS 0x00000000a1a00000| PB 0x00000000a1a00000| Untracked 
| 539|0x00000000a1b00000, 0x00000000a1b00000, 0x00000000a1c00000|  0%| F|  |TAMS 0x00000000a1b00000| PB 0x00000000a1b00000| Untracked 
| 540|0x00000000a1c00000, 0x00000000a1c00000, 0x00000000a1d00000|  0%| F|  |TAMS 0x00000000a1c00000| PB 0x00000000a1c00000| Untracked 
| 541|0x00000000a1d00000, 0x00000000a1d00000, 0x00000000a1e00000|  0%| F|  |TAMS 0x00000000a1d00000| PB 0x00000000a1d00000| Untracked 
| 542|0x00000000a1e00000, 0x00000000a1e00000, 0x00000000a1f00000|  0%| F|  |TAMS 0x00000000a1e00000| PB 0x00000000a1e00000| Untracked 
| 543|0x00000000a1f00000, 0x00000000a1f00000, 0x00000000a2000000|  0%| F|  |TAMS 0x00000000a1f00000| PB 0x00000000a1f00000| Untracked 
| 544|0x00000000a2000000, 0x00000000a2000000, 0x00000000a2100000|  0%| F|  |TAMS 0x00000000a2000000| PB 0x00000000a2000000| Untracked 
| 545|0x00000000a2100000, 0x00000000a2100000, 0x00000000a2200000|  0%| F|  |TAMS 0x00000000a2100000| PB 0x00000000a2100000| Untracked 
| 546|0x00000000a2200000, 0x00000000a2200000, 0x00000000a2300000|  0%| F|  |TAMS 0x00000000a2200000| PB 0x00000000a2200000| Untracked 
| 547|0x00000000a2300000, 0x00000000a2300000, 0x00000000a2400000|  0%| F|  |TAMS 0x00000000a2300000| PB 0x00000000a2300000| Untracked 
| 548|0x00000000a2400000, 0x00000000a2400000, 0x00000000a2500000|  0%| F|  |TAMS 0x00000000a2400000| PB 0x00000000a2400000| Untracked 
| 549|0x00000000a2500000, 0x00000000a2500000, 0x00000000a2600000|  0%| F|  |TAMS 0x00000000a2500000| PB 0x00000000a2500000| Untracked 
| 550|0x00000000a2600000, 0x00000000a2600000, 0x00000000a2700000|  0%| F|  |TAMS 0x00000000a2600000| PB 0x00000000a2600000| Untracked 
| 551|0x00000000a2700000, 0x00000000a2700000, 0x00000000a2800000|  0%| F|  |TAMS 0x00000000a2700000| PB 0x00000000a2700000| Untracked 
| 552|0x00000000a2800000, 0x00000000a2800000, 0x00000000a2900000|  0%| F|  |TAMS 0x00000000a2800000| PB 0x00000000a2800000| Untracked 
| 553|0x00000000a2900000, 0x00000000a2900000, 0x00000000a2a00000|  0%| F|  |TAMS 0x00000000a2900000| PB 0x00000000a2900000| Untracked 
| 554|0x00000000a2a00000, 0x00000000a2a00000, 0x00000000a2b00000|  0%| F|  |TAMS 0x00000000a2a00000| PB 0x00000000a2a00000| Untracked 
| 555|0x00000000a2b00000, 0x00000000a2b00000, 0x00000000a2c00000|  0%| F|  |TAMS 0x00000000a2b00000| PB 0x00000000a2b00000| Untracked 
| 556|0x00000000a2c00000, 0x00000000a2c00000, 0x00000000a2d00000|  0%| F|  |TAMS 0x00000000a2c00000| PB 0x00000000a2c00000| Untracked 
|1973|0x00000000fb500000, 0x00000000fb500000, 0x00000000fb600000|  0%| F|  |TAMS 0x00000000fb500000| PB 0x00000000fb500000| Untracked 
|1974|0x00000000fb600000, 0x00000000fb600000, 0x00000000fb700000|  0%| F|  |TAMS 0x00000000fb600000| PB 0x00000000fb600000| Untracked 
|1975|0x00000000fb700000, 0x00000000fb700000, 0x00000000fb800000|  0%| F|  |TAMS 0x00000000fb700000| PB 0x00000000fb700000| Untracked 
|1976|0x00000000fb800000, 0x00000000fb900000, 0x00000000fb900000|100%| O|  |TAMS 0x00000000fb800000| PB 0x00000000fb800000| Untracked 
|1977|0x00000000fb900000, 0x00000000fba00000, 0x00000000fba00000|100%| O|  |TAMS 0x00000000fb900000| PB 0x00000000fb900000| Untracked 
|1978|0x00000000fba00000, 0x00000000fbb00000, 0x00000000fbb00000|100%| O|  |TAMS 0x00000000fba00000| PB 0x00000000fba00000| Untracked 
|1979|0x00000000fbb00000, 0x00000000fbc00000, 0x00000000fbc00000|100%| O|  |TAMS 0x00000000fbb00000| PB 0x00000000fbb00000| Untracked 
|1980|0x00000000fbc00000, 0x00000000fbd00000, 0x00000000fbd00000|100%| O|  |TAMS 0x00000000fbc00000| PB 0x00000000fbc00000| Untracked 
|1981|0x00000000fbd00000, 0x00000000fbe00000, 0x00000000fbe00000|100%| O|  |TAMS 0x00000000fbd00000| PB 0x00000000fbd00000| Untracked 
|1982|0x00000000fbe00000, 0x00000000fbf00000, 0x00000000fbf00000|100%| O|  |TAMS 0x00000000fbe00000| PB 0x00000000fbe00000| Untracked 
|1983|0x00000000fbf00000, 0x00000000fc000000, 0x00000000fc000000|100%| O|  |TAMS 0x00000000fbf00000| PB 0x00000000fbf00000| Untracked 
|1984|0x00000000fc000000, 0x00000000fc000000, 0x00000000fc100000|  0%| F|  |TAMS 0x00000000fc000000| PB 0x00000000fc000000| Untracked 
|1985|0x00000000fc100000, 0x00000000fc100000, 0x00000000fc200000|  0%| F|  |TAMS 0x00000000fc100000| PB 0x00000000fc100000| Untracked 
|1986|0x00000000fc200000, 0x00000000fc300000, 0x00000000fc300000|100%| O|  |TAMS 0x00000000fc200000| PB 0x00000000fc200000| Untracked 
|1987|0x00000000fc300000, 0x00000000fc400000, 0x00000000fc400000|100%| O|  |TAMS 0x00000000fc300000| PB 0x00000000fc300000| Untracked 
|1988|0x00000000fc400000, 0x00000000fc400000, 0x00000000fc500000|  0%| F|  |TAMS 0x00000000fc400000| PB 0x00000000fc400000| Untracked 
|1989|0x00000000fc500000, 0x00000000fc600000, 0x00000000fc600000|100%| O|  |TAMS 0x00000000fc500000| PB 0x00000000fc500000| Untracked 
|1990|0x00000000fc600000, 0x00000000fc700000, 0x00000000fc700000|100%| O|  |TAMS 0x00000000fc600000| PB 0x00000000fc600000| Untracked 
|1991|0x00000000fc700000, 0x00000000fc800000, 0x00000000fc800000|100%| O|  |TAMS 0x00000000fc700000| PB 0x00000000fc700000| Untracked 
|1992|0x00000000fc800000, 0x00000000fc900000, 0x00000000fc900000|100%| O|  |TAMS 0x00000000fc800000| PB 0x00000000fc800000| Untracked 
|1993|0x00000000fc900000, 0x00000000fca00000, 0x00000000fca00000|100%| O|  |TAMS 0x00000000fc900000| PB 0x00000000fc900000| Untracked 
|1994|0x00000000fca00000, 0x00000000fcb00000, 0x00000000fcb00000|100%| O|  |TAMS 0x00000000fca00000| PB 0x00000000fca00000| Untracked 
|1995|0x00000000fcb00000, 0x00000000fcc00000, 0x00000000fcc00000|100%| O|  |TAMS 0x00000000fcb00000| PB 0x00000000fcb00000| Untracked 
|1996|0x00000000fcc00000, 0x00000000fcc00000, 0x00000000fcd00000|  0%| F|  |TAMS 0x00000000fcc00000| PB 0x00000000fcc00000| Untracked 
|1997|0x00000000fcd00000, 0x00000000fcd00000, 0x00000000fce00000|  0%| F|  |TAMS 0x00000000fcd00000| PB 0x00000000fcd00000| Untracked 
|1998|0x00000000fce00000, 0x00000000fce00000, 0x00000000fcf00000|  0%| F|  |TAMS 0x00000000fce00000| PB 0x00000000fce00000| Untracked 
|1999|0x00000000fcf00000, 0x00000000fd000000, 0x00000000fd000000|100%| O|  |TAMS 0x00000000fcf00000| PB 0x00000000fcf00000| Untracked 
|2000|0x00000000fd000000, 0x00000000fd000000, 0x00000000fd100000|  0%| F|  |TAMS 0x00000000fd000000| PB 0x00000000fd000000| Untracked 
|2001|0x00000000fd100000, 0x00000000fd200000, 0x00000000fd200000|100%| O|  |TAMS 0x00000000fd100000| PB 0x00000000fd100000| Untracked 
|2002|0x00000000fd200000, 0x00000000fd300000, 0x00000000fd300000|100%| O|  |TAMS 0x00000000fd200000| PB 0x00000000fd200000| Untracked 
|2003|0x00000000fd300000, 0x00000000fd400000, 0x00000000fd400000|100%| O|  |TAMS 0x00000000fd300000| PB 0x00000000fd300000| Untracked 
|2004|0x00000000fd400000, 0x00000000fd500000, 0x00000000fd500000|100%| O|  |TAMS 0x00000000fd400000| PB 0x00000000fd400000| Untracked 
|2005|0x00000000fd500000, 0x00000000fd600000, 0x00000000fd600000|100%| O|  |TAMS 0x00000000fd500000| PB 0x00000000fd500000| Untracked 
|2006|0x00000000fd600000, 0x00000000fd700000, 0x00000000fd700000|100%| O|  |TAMS 0x00000000fd600000| PB 0x00000000fd600000| Untracked 
|2007|0x00000000fd700000, 0x00000000fd800000, 0x00000000fd800000|100%| O|  |TAMS 0x00000000fd700000| PB 0x00000000fd700000| Untracked 
|2008|0x00000000fd800000, 0x00000000fd900000, 0x00000000fd900000|100%| O|  |TAMS 0x00000000fd800000| PB 0x00000000fd800000| Untracked 
|2009|0x00000000fd900000, 0x00000000fda00000, 0x00000000fda00000|100%| O|  |TAMS 0x00000000fd900000| PB 0x00000000fd900000| Untracked 
|2010|0x00000000fda00000, 0x00000000fdb00000, 0x00000000fdb00000|100%| O|  |TAMS 0x00000000fda00000| PB 0x00000000fda00000| Untracked 
|2011|0x00000000fdb00000, 0x00000000fdc00000, 0x00000000fdc00000|100%| O|  |TAMS 0x00000000fdb00000| PB 0x00000000fdb00000| Untracked 
|2012|0x00000000fdc00000, 0x00000000fdd00000, 0x00000000fdd00000|100%| O|  |TAMS 0x00000000fdc00000| PB 0x00000000fdc00000| Untracked 
|2013|0x00000000fdd00000, 0x00000000fde00000, 0x00000000fde00000|100%| O|  |TAMS 0x00000000fdd00000| PB 0x00000000fdd00000| Untracked 
|2014|0x00000000fde00000, 0x00000000fdf00000, 0x00000000fdf00000|100%| O|  |TAMS 0x00000000fde00000| PB 0x00000000fde00000| Untracked 
|2015|0x00000000fdf00000, 0x00000000fe000000, 0x00000000fe000000|100%| O|  |TAMS 0x00000000fdf00000| PB 0x00000000fdf00000| Untracked 
|2016|0x00000000fe000000, 0x00000000fe100000, 0x00000000fe100000|100%| O|  |TAMS 0x00000000fe000000| PB 0x00000000fe000000| Untracked 
|2017|0x00000000fe100000, 0x00000000fe200000, 0x00000000fe200000|100%| O|  |TAMS 0x00000000fe100000| PB 0x00000000fe100000| Untracked 
|2018|0x00000000fe200000, 0x00000000fe300000, 0x00000000fe300000|100%| O|  |TAMS 0x00000000fe200000| PB 0x00000000fe200000| Untracked 
|2019|0x00000000fe300000, 0x00000000fe400000, 0x00000000fe400000|100%| O|  |TAMS 0x00000000fe300000| PB 0x00000000fe300000| Untracked 
|2020|0x00000000fe400000, 0x00000000fe500000, 0x00000000fe500000|100%| O|  |TAMS 0x00000000fe400000| PB 0x00000000fe400000| Untracked 
|2021|0x00000000fe500000, 0x00000000fe600000, 0x00000000fe600000|100%| O|  |TAMS 0x00000000fe500000| PB 0x00000000fe500000| Untracked 
|2022|0x00000000fe600000, 0x00000000fe700000, 0x00000000fe700000|100%| O|  |TAMS 0x00000000fe600000| PB 0x00000000fe600000| Untracked 
|2023|0x00000000fe700000, 0x00000000fe800000, 0x00000000fe800000|100%| O|  |TAMS 0x00000000fe700000| PB 0x00000000fe700000| Untracked 
|2024|0x00000000fe800000, 0x00000000fe800000, 0x00000000fe900000|  0%| F|  |TAMS 0x00000000fe800000| PB 0x00000000fe800000| Untracked 
|2025|0x00000000fe900000, 0x00000000fe900000, 0x00000000fea00000|  0%| F|  |TAMS 0x00000000fe900000| PB 0x00000000fe900000| Untracked 
|2026|0x00000000fea00000, 0x00000000fea00000, 0x00000000feb00000|  0%| F|  |TAMS 0x00000000fea00000| PB 0x00000000fea00000| Untracked 
|2027|0x00000000feb00000, 0x00000000feb00000, 0x00000000fec00000|  0%| F|  |TAMS 0x00000000feb00000| PB 0x00000000feb00000| Untracked 
|2028|0x00000000fec00000, 0x00000000fec00000, 0x00000000fed00000|  0%| F|  |TAMS 0x00000000fec00000| PB 0x00000000fec00000| Untracked 
|2029|0x00000000fed00000, 0x00000000fed00000, 0x00000000fee00000|  0%| F|  |TAMS 0x00000000fed00000| PB 0x00000000fed00000| Untracked 
|2030|0x00000000fee00000, 0x00000000fee00000, 0x00000000fef00000|  0%| F|  |TAMS 0x00000000fee00000| PB 0x00000000fee00000| Untracked 
|2031|0x00000000fef00000, 0x00000000fef00000, 0x00000000ff000000|  0%| F|  |TAMS 0x00000000fef00000| PB 0x00000000fef00000| Untracked 
|2032|0x00000000ff000000, 0x00000000ff000000, 0x00000000ff100000|  0%| F|  |TAMS 0x00000000ff000000| PB 0x00000000ff000000| Untracked 
|2033|0x00000000ff100000, 0x00000000ff100000, 0x00000000ff200000|  0%| F|  |TAMS 0x00000000ff100000| PB 0x00000000ff100000| Untracked 
|2034|0x00000000ff200000, 0x00000000ff200000, 0x00000000ff300000|  0%| F|  |TAMS 0x00000000ff200000| PB 0x00000000ff200000| Untracked 
|2035|0x00000000ff300000, 0x00000000ff300000, 0x00000000ff400000|  0%| F|  |TAMS 0x00000000ff300000| PB 0x00000000ff300000| Untracked 
|2036|0x00000000ff400000, 0x00000000ff400000, 0x00000000ff500000|  0%| F|  |TAMS 0x00000000ff400000| PB 0x00000000ff400000| Untracked 
|2037|0x00000000ff500000, 0x00000000ff500000, 0x00000000ff600000|  0%| F|  |TAMS 0x00000000ff500000| PB 0x00000000ff500000| Untracked 
|2038|0x00000000ff600000, 0x00000000ff600000, 0x00000000ff700000|  0%| F|  |TAMS 0x00000000ff600000| PB 0x00000000ff600000| Untracked 
|2039|0x00000000ff700000, 0x00000000ff800000, 0x00000000ff800000|100%| O|  |TAMS 0x00000000ff700000| PB 0x00000000ff700000| Untracked 
|2040|0x00000000ff800000, 0x00000000ff900000, 0x00000000ff900000|100%| O|  |TAMS 0x00000000ff800000| PB 0x00000000ff800000| Untracked 
|2041|0x00000000ff900000, 0x00000000ffa00000, 0x00000000ffa00000|100%| O|  |TAMS 0x00000000ff900000| PB 0x00000000ff900000| Untracked 
|2042|0x00000000ffa00000, 0x00000000ffb00000, 0x00000000ffb00000|100%| O|  |TAMS 0x00000000ffa00000| PB 0x00000000ffa00000| Untracked 
|2043|0x00000000ffb00000, 0x00000000ffc00000, 0x00000000ffc00000|100%| O|  |TAMS 0x00000000ffb00000| PB 0x00000000ffb00000| Untracked 
|2044|0x00000000ffc00000, 0x00000000ffc00000, 0x00000000ffd00000|  0%| F|  |TAMS 0x00000000ffc00000| PB 0x00000000ffc00000| Untracked 
|2045|0x00000000ffd00000, 0x00000000ffe00000, 0x00000000ffe00000|100%| O|  |TAMS 0x00000000ffd00000| PB 0x00000000ffd00000| Untracked 
|2046|0x00000000ffe00000, 0x00000000fff00000, 0x00000000fff00000|100%| O|  |TAMS 0x00000000ffe00000| PB 0x00000000ffe00000| Untracked 
|2047|0x00000000fff00000, 0x0000000100000000, 0x0000000100000000|100%| O|  |TAMS 0x00000000fff00000| PB 0x00000000fff00000| Untracked 

Card table byte_map: [0x00000279d8370000,0x00000279d8770000] _byte_map_base: 0x00000279d7f70000

Marking Bits: (CMBitMap*) 0x00000279da7960f0
 Bits: [0x00000279d8770000, 0x00000279da770000)

Polling page: 0x00000279c3710000

Metaspace:

Usage:
  Non-class:    147.65 MB used.
      Class:     22.24 MB used.
       Both:    169.89 MB used.

Virtual space:
  Non-class space:      192.00 MB reserved,     148.88 MB ( 78%) committed,  3 nodes.
      Class space:        1.00 GB reserved,      23.50 MB (  2%) committed,  1 nodes.
             Both:        1.19 GB reserved,     172.38 MB ( 14%) committed. 

Chunk freelists:
   Non-Class:  11.14 MB
       Class:  8.44 MB
        Both:  19.58 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 284.50 MB
CDS: off
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 6.
num_arena_births: 4930.
num_arena_deaths: 10.
num_vsnodes_births: 4.
num_vsnodes_deaths: 0.
num_space_committed: 2756.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 25.
num_chunks_taken_from_freelist: 12396.
num_chunk_merges: 8.
num_chunk_splits: 8156.
num_chunks_enlarged: 5179.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=14016Kb max_used=14016Kb free=105983Kb
 bounds [0x00000279d0710000, 0x00000279d14d0000, 0x00000279d7c40000]
CodeHeap 'profiled nmethods': size=120000Kb used=34490Kb max_used=37001Kb free=85509Kb
 bounds [0x00000279c8c40000, 0x00000279cb130000, 0x00000279d0170000]
CodeHeap 'non-nmethods': size=5760Kb used=3106Kb max_used=3169Kb free=2654Kb
 bounds [0x00000279d0170000, 0x00000279d04a0000, 0x00000279d0710000]
 total_blobs=18914 nmethods=17768 adapters=1049
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 76.145 Thread 0x00000279dd93d950 25453       3       org.gradle.internal.serialize.SetSerializer::createCollection (6 bytes)
Event: 76.145 Thread 0x00000279dd93d950 nmethod 25453 0x00000279c9469590 code [0x00000279c9469800, 0x00000279c946a028]
Event: 76.145 Thread 0x00000279dd93d950 25454       3       org.gradle.internal.serialize.SetSerializer::createCollection (27 bytes)
Event: 76.146 Thread 0x00000279dd93d950 nmethod 25454 0x00000279c9509590 code [0x00000279c9509800, 0x00000279c9509fc0]
Event: 76.146 Thread 0x00000279dd93d950 25455       1       org.apache.commons.compress.archivers.zip.ZipArchiveEntry::getMethod (5 bytes)
Event: 76.146 Thread 0x00000279dd93d950 nmethod 25455 0x00000279d14bf410 code [0x00000279d14bf5a0, 0x00000279d14bf668]
Event: 76.146 Thread 0x00000279dd93d950 25456       1       org.gradle.api.internal.tasks.compile.incremental.deps.ClassAnalysis::getClassName (5 bytes)
Event: 76.146 Thread 0x00000279dd93d950 nmethod 25456 0x00000279d14bf710 code [0x00000279d14bf8a0, 0x00000279d14bf968]
Event: 76.146 Thread 0x00000279dd93d950 25457       1       org.gradle.api.internal.tasks.compile.incremental.deps.ClassAnalysis::getDependencyToAllReason (5 bytes)
Event: 76.147 Thread 0x00000279dd93d950 nmethod 25457 0x00000279d14bfa10 code [0x00000279d14bfba0, 0x00000279d14bfc68]
Event: 76.147 Thread 0x00000279dd93d950 25458       1       org.gradle.api.internal.tasks.compile.incremental.deps.ClassAnalysis::getAccessibleClassDependencies (5 bytes)
Event: 76.147 Thread 0x00000279dd93d950 nmethod 25458 0x00000279d14bfd10 code [0x00000279d14bfea0, 0x00000279d14bff68]
Event: 76.147 Thread 0x00000279dd93d950 25459       1       org.gradle.api.internal.tasks.compile.incremental.deps.ClassAnalysis::getConstants (5 bytes)
Event: 76.147 Thread 0x00000279dd93d950 nmethod 25459 0x00000279d14c0010 code [0x00000279d14c01a0, 0x00000279d14c0268]
Event: 76.147 Thread 0x00000279dd93d950 25460       3       org.gradle.internal.hash.DefaultStreamHasher::hash (9 bytes)
Event: 76.147 Thread 0x00000279dd93d950 nmethod 25460 0x00000279c92e7710 code [0x00000279c92e78c0, 0x00000279c92e7a60]
Event: 76.147 Thread 0x00000279dd93d950 25461       3       com.google.common.io.ByteStreams::nullOutputStream (4 bytes)
Event: 76.147 Thread 0x00000279dd93d950 nmethod 25461 0x00000279c9456e90 code [0x00000279c9457020, 0x00000279c9457118]
Event: 76.147 Thread 0x00000279dd93d950 25462   !   3       java.util.concurrent.ArrayBlockingQueue::poll (38 bytes)
Event: 76.148 Thread 0x00000279dd93d950 nmethod 25462 0x00000279c9534990 code [0x00000279c9534be0, 0x00000279c9535520]

GC Heap History (20 events):
Event: 68.189 GC heap after
{Heap after GC invocations=43 (full 0):
 garbage-first heap   total 409600K, used 229939K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 5 young (5120K), 5 survivors (5120K)
 Metaspace       used 170141K, committed 172736K, reserved 1245184K
  class space    used 22299K, committed 23616K, reserved 1048576K
}
Event: 68.207 GC heap before
{Heap before GC invocations=43 (full 0):
 garbage-first heap   total 409600K, used 234035K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 8 young (8192K), 5 survivors (5120K)
 Metaspace       used 170209K, committed 172800K, reserved 1245184K
  class space    used 22305K, committed 23616K, reserved 1048576K
}
Event: 68.213 GC heap after
{Heap after GC invocations=44 (full 0):
 garbage-first heap   total 409600K, used 231284K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 5 young (5120K), 5 survivors (5120K)
 Metaspace       used 170209K, committed 172800K, reserved 1245184K
  class space    used 22305K, committed 23616K, reserved 1048576K
}
Event: 68.222 GC heap before
{Heap before GC invocations=44 (full 0):
 garbage-first heap   total 409600K, used 232308K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 7 young (7168K), 5 survivors (5120K)
 Metaspace       used 170211K, committed 172800K, reserved 1245184K
  class space    used 22305K, committed 23616K, reserved 1048576K
}
Event: 68.227 GC heap after
{Heap after GC invocations=45 (full 0):
 garbage-first heap   total 409600K, used 232561K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 5 young (5120K), 5 survivors (5120K)
 Metaspace       used 170211K, committed 172800K, reserved 1245184K
  class space    used 22305K, committed 23616K, reserved 1048576K
}
Event: 69.002 GC heap before
{Heap before GC invocations=46 (full 0):
 garbage-first heap   total 409600K, used 403569K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 131 young (134144K), 5 survivors (5120K)
 Metaspace       used 171373K, committed 174016K, reserved 1245184K
  class space    used 22515K, committed 23872K, reserved 1048576K
}
Event: 69.046 GC heap after
{Heap after GC invocations=47 (full 0):
 garbage-first heap   total 464896K, used 304581K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 17 young (17408K), 17 survivors (17408K)
 Metaspace       used 171373K, committed 174016K, reserved 1245184K
  class space    used 22515K, committed 23872K, reserved 1048576K
}
Event: 69.268 GC heap before
{Heap before GC invocations=47 (full 0):
 garbage-first heap   total 464896K, used 401861K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 113 young (115712K), 17 survivors (17408K)
 Metaspace       used 171410K, committed 174016K, reserved 1245184K
  class space    used 22516K, committed 23872K, reserved 1048576K
}
Event: 69.321 GC heap after
{Heap after GC invocations=48 (full 0):
 garbage-first heap   total 475136K, used 337920K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 15 young (15360K), 15 survivors (15360K)
 Metaspace       used 171410K, committed 174016K, reserved 1245184K
  class space    used 22516K, committed 23872K, reserved 1048576K
}
Event: 69.793 GC heap before
{Heap before GC invocations=48 (full 0):
 garbage-first heap   total 475136K, used 409600K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 85 young (87040K), 15 survivors (15360K)
 Metaspace       used 171880K, committed 174464K, reserved 1245184K
  class space    used 22558K, committed 23872K, reserved 1048576K
}
Event: 69.824 GC heap after
{Heap after GC invocations=49 (full 0):
 garbage-first heap   total 475136K, used 357888K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 11 young (11264K), 11 survivors (11264K)
 Metaspace       used 171880K, committed 174464K, reserved 1245184K
  class space    used 22558K, committed 23872K, reserved 1048576K
}
Event: 70.383 GC heap before
{Heap before GC invocations=50 (full 0):
 garbage-first heap   total 568320K, used 401920K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 72 young (73728K), 11 survivors (11264K)
 Metaspace       used 171965K, committed 174528K, reserved 1245184K
  class space    used 22558K, committed 23872K, reserved 1048576K
}
Event: 70.396 GC heap after
{Heap after GC invocations=51 (full 0):
 garbage-first heap   total 568320K, used 348160K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 7 young (7168K), 7 survivors (7168K)
 Metaspace       used 171965K, committed 174528K, reserved 1245184K
  class space    used 22558K, committed 23872K, reserved 1048576K
}
Event: 71.573 GC heap before
{Heap before GC invocations=51 (full 0):
 garbage-first heap   total 568320K, used 495616K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 130 young (133120K), 7 survivors (7168K)
 Metaspace       used 172114K, committed 174720K, reserved 1245184K
  class space    used 22562K, committed 23872K, reserved 1048576K
}
Event: 71.590 GC heap after
{Heap after GC invocations=52 (full 0):
 garbage-first heap   total 568320K, used 369842K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 14 young (14336K), 14 survivors (14336K)
 Metaspace       used 172114K, committed 174720K, reserved 1245184K
  class space    used 22562K, committed 23872K, reserved 1048576K
}
Event: 71.796 GC heap before
{Heap before GC invocations=52 (full 0):
 garbage-first heap   total 568320K, used 396466K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 42 young (43008K), 14 survivors (14336K)
 Metaspace       used 172121K, committed 174720K, reserved 1245184K
  class space    used 22562K, committed 23872K, reserved 1048576K
}
Event: 71.808 GC heap after
{Heap after GC invocations=53 (full 0):
 garbage-first heap   total 568320K, used 374451K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 13 young (13312K), 13 survivors (13312K)
 Metaspace       used 172121K, committed 174720K, reserved 1245184K
  class space    used 22562K, committed 23872K, reserved 1048576K
}
Event: 72.354 GC heap before
{Heap before GC invocations=54 (full 0):
 garbage-first heap   total 647168K, used 520883K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 134 young (137216K), 13 survivors (13312K)
 Metaspace       used 172239K, committed 174848K, reserved 1245184K
  class space    used 22563K, committed 23872K, reserved 1048576K
}
Event: 72.367 GC heap after
{Heap after GC invocations=55 (full 0):
 garbage-first heap   total 647168K, used 400560K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 9 young (9216K), 9 survivors (9216K)
 Metaspace       used 172239K, committed 174848K, reserved 1245184K
  class space    used 22563K, committed 23872K, reserved 1048576K
}
Event: 76.148 GC heap before
{Heap before GC invocations=55 (full 0):
 garbage-first heap   total 647168K, used 574640K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 167 young (171008K), 9 survivors (9216K)
 Metaspace       used 173965K, committed 176512K, reserved 1245184K
  class space    used 22769K, committed 24064K, reserved 1048576K
}

Dll operation events (3 events):
Event: 0.011 Loaded shared library C:\Program Files\Android\Android Studio\jbr\bin\java.dll
Event: 0.018 Loaded shared library C:\Program Files\Android\Android Studio\jbr\bin\zip.dll
Event: 0.629 Loaded shared library C:\Program Files\Android\Android Studio\jbr\bin\verify.dll

Deoptimization events (20 events):
Event: 75.951 Thread 0x00000279e4d424c0 DEOPT PACKING pc=0x00000279d07cfd78 sp=0x000000878d8fb9a0
Event: 75.951 Thread 0x00000279e4d424c0 DEOPT UNPACKING pc=0x00000279d01c46a2 sp=0x000000878d8fb958 mode 2
Event: 75.967 Thread 0x00000279e4d424c0 Uncommon trap: trap_request=0xffffffc6 fr.pc=0x00000279d09e4c0c relative=0x00000000000038ec
Event: 75.967 Thread 0x00000279e4d424c0 Uncommon trap: reason=bimorphic_or_optimized_type_check action=maybe_recompile pc=0x00000279d09e4c0c method=jdk.internal.loader.BuiltinClassLoader.loadClassOrNull(Ljava/lang/String;)Ljava/lang/Class; @ 3 c2
Event: 75.967 Thread 0x00000279e4d424c0 DEOPT PACKING pc=0x00000279d09e4c0c sp=0x000000878d8fafc0
Event: 75.967 Thread 0x00000279e4d424c0 DEOPT UNPACKING pc=0x00000279d01c46a2 sp=0x000000878d8faf58 mode 2
Event: 76.031 Thread 0x00000279e4d424c0 DEOPT PACKING pc=0x00000279c94abe9b sp=0x000000878d8faa50
Event: 76.031 Thread 0x00000279e4d424c0 DEOPT UNPACKING pc=0x00000279d01c4e42 sp=0x000000878d8f9ef8 mode 0
Event: 76.072 Thread 0x00000279f21b5990 Uncommon trap: trap_request=0xffffff45 fr.pc=0x00000279d0b69830 relative=0x00000000000001f0
Event: 76.072 Thread 0x00000279f21b5990 Uncommon trap: reason=unstable_if action=reinterpret pc=0x00000279d0b69830 method=com.esotericsoftware.kryo.io.Input.require(I)I @ 65 c2
Event: 76.072 Thread 0x00000279f21b5990 DEOPT PACKING pc=0x00000279d0b69830 sp=0x000000878c0fe5d0
Event: 76.072 Thread 0x00000279f21b5990 DEOPT UNPACKING pc=0x00000279d01c46a2 sp=0x000000878c0fe580 mode 2
Event: 76.072 Thread 0x00000279f21b5990 Uncommon trap: trap_request=0xffffff45 fr.pc=0x00000279d0e09670 relative=0x0000000000000ab0
Event: 76.072 Thread 0x00000279f21b5990 Uncommon trap: reason=unstable_if action=reinterpret pc=0x00000279d0e09670 method=org.gradle.internal.io.StreamByteBuffer$StreamByteBufferInputStream.readImpl([BII)I @ 49 c2
Event: 76.072 Thread 0x00000279f21b5990 DEOPT PACKING pc=0x00000279d0e09670 sp=0x000000878c0fe490
Event: 76.072 Thread 0x00000279f21b5990 DEOPT UNPACKING pc=0x00000279d01c46a2 sp=0x000000878c0fe3e0 mode 2
Event: 76.075 Thread 0x00000279f21b5990 Uncommon trap: trap_request=0xffffff45 fr.pc=0x00000279d0b8a34c relative=0x000000000000072c
Event: 76.075 Thread 0x00000279f21b5990 Uncommon trap: reason=unstable_if action=reinterpret pc=0x00000279d0b8a34c method=org.gradle.internal.io.StreamByteBuffer$StreamByteBufferInputStream.readImpl([BII)I @ 49 c2
Event: 76.075 Thread 0x00000279f21b5990 DEOPT PACKING pc=0x00000279d0b8a34c sp=0x000000878c0fe3d0
Event: 76.075 Thread 0x00000279f21b5990 DEOPT UNPACKING pc=0x00000279d01c46a2 sp=0x000000878c0fe398 mode 2

Classes loaded (20 events):
Event: 68.844 Loading class java/util/concurrent/ForkJoinTask$AdaptedInterruptibleCallable
Event: 68.844 Loading class java/util/concurrent/ForkJoinTask$InterruptibleTask
Event: 68.844 Loading class java/util/concurrent/ForkJoinTask$InterruptibleTask done
Event: 68.844 Loading class java/util/concurrent/ForkJoinTask$AdaptedInterruptibleCallable done
Event: 68.844 Loading class java/util/concurrent/ForkJoinPool$DefaultForkJoinWorkerThreadFactory$1
Event: 68.844 Loading class java/util/concurrent/ForkJoinPool$DefaultForkJoinWorkerThreadFactory$1 done
Event: 68.845 Loading class java/util/concurrent/CountedCompleter
Event: 68.845 Loading class java/util/concurrent/CountedCompleter done
Event: 68.850 Loading class java/util/AbstractList$RandomAccessSubList
Event: 68.850 Loading class java/util/AbstractList$SubList
Event: 68.850 Loading class java/util/AbstractList$SubList done
Event: 68.850 Loading class java/util/AbstractList$RandomAccessSubList done
Event: 68.850 Loading class java/util/AbstractList$SubList$1
Event: 68.850 Loading class java/util/AbstractList$SubList$1 done
Event: 68.873 Loading class java/nio/HeapShortBuffer
Event: 68.874 Loading class java/nio/HeapShortBuffer done
Event: 75.946 Loading class java/util/AbstractMap$2
Event: 75.946 Loading class java/util/AbstractMap$2 done
Event: 76.065 Loading class java/util/Collections$UnmodifiableSortedMap
Event: 76.065 Loading class java/util/Collections$UnmodifiableSortedMap done

Classes unloaded (14 events):
Event: 10.984 Thread 0x00000279dd5dfcc0 Unloading class 0x0000000100b28550 '_BuildScript_$_run_closure1'
Event: 10.984 Thread 0x00000279dd5dfcc0 Unloading class 0x0000000100b28000 '_BuildScript_'
Event: 13.663 Thread 0x00000279dd5dfcc0 Unloading class 0x0000000100d0e400 '_BuildScript_$_run_closure2'
Event: 13.663 Thread 0x00000279dd5dfcc0 Unloading class 0x0000000100d0e000 '_BuildScript_$_run_closure1$_closure6'
Event: 13.663 Thread 0x00000279dd5dfcc0 Unloading class 0x0000000100d0dc00 '_BuildScript_$_run_closure1$_closure5'
Event: 13.663 Thread 0x00000279dd5dfcc0 Unloading class 0x0000000100d0d800 '_BuildScript_$_run_closure1$_closure4$_closure8'
Event: 13.663 Thread 0x00000279dd5dfcc0 Unloading class 0x0000000100d0d400 '_BuildScript_$_run_closure1$_closure4$_closure7'
Event: 13.663 Thread 0x00000279dd5dfcc0 Unloading class 0x0000000100d0d000 '_BuildScript_$_run_closure1$_closure4'
Event: 13.663 Thread 0x00000279dd5dfcc0 Unloading class 0x0000000100d0c950 '_BuildScript_$_run_closure1$_closure3'
Event: 13.663 Thread 0x00000279dd5dfcc0 Unloading class 0x0000000100d0c550 '_BuildScript_$_run_closure1'
Event: 13.663 Thread 0x00000279dd5dfcc0 Unloading class 0x0000000100d0c000 '_BuildScript_'
Event: 68.356 Thread 0x00000279dd5dfcc0 Unloading class 0x00000001015c4c00 'java/lang/invoke/LambdaForm$DMH+0x00000001015c4c00'
Event: 68.356 Thread 0x00000279dd5dfcc0 Unloading class 0x00000001015c4000 'java/lang/invoke/LambdaForm$DMH+0x00000001015c4000'
Event: 70.123 Thread 0x00000279dd5dfcc0 Unloading class 0x0000000101668400 'java/lang/invoke/LambdaForm$DMH+0x0000000101668400'

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 67.956 Thread 0x00000279eb8a82c0 Implicit null exception at 0x00000279d120bbfd to 0x00000279d120bd2c
Event: 68.052 Thread 0x00000279eb8a82c0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000909ae150}> (0x00000000909ae150) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 68.106 Thread 0x00000279e4d43260 Exception <a 'sun/nio/fs/WindowsException'{0x0000000090560218}> (0x0000000090560218) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 68.106 Thread 0x00000279e4d43260 Exception <a 'sun/nio/fs/WindowsException'{0x000000009056cc60}> (0x000000009056cc60) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 68.166 Thread 0x00000279eb8a1c90 Exception <a 'sun/nio/fs/WindowsException'{0x000000008fac0988}> (0x000000008fac0988) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 68.167 Thread 0x00000279eb8a1c90 Exception <a 'sun/nio/fs/WindowsException'{0x000000008fac14b8}> (0x000000008fac14b8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 68.205 Thread 0x00000279e4d43260 Exception <a 'sun/nio/fs/WindowsException'{0x00000000fe8a9540}> (0x00000000fe8a9540) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 68.850 Thread 0x00000279f1f84ba0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000921236c0}: 'long java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000921236c0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 69.550 Thread 0x00000279e59cf0c0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000096317338}: 'long java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object)'> (0x0000000096317338) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 69.557 Thread 0x00000279e59cf0c0 Exception <a 'java/lang/NoSuchMethodError'{0x000000009634d010}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, int, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000009634d010) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 69.558 Thread 0x00000279e59cf0c0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000096351270}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, int, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000096351270) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 70.462 Thread 0x00000279e59cf0c0 Implicit null exception at 0x00000279d1038e1a to 0x00000279d103a558
Event: 71.593 Thread 0x00000279f1f8a430 Implicit null exception at 0x00000279d12653be to 0x00000279d1267025
Event: 71.593 Thread 0x00000279f1f8a430 Implicit null exception at 0x00000279d0a77ad6 to 0x00000279d0a79410
Event: 72.716 Thread 0x00000279f1f84ba0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000a1801ec8}> (0x00000000a1801ec8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 72.928 Thread 0x00000279f1f8a430 Exception <a 'sun/nio/fs/WindowsException'{0x00000000a0de2828}> (0x00000000a0de2828) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 75.633 Thread 0x00000279e4d40980 Exception <a 'sun/nio/fs/WindowsException'{0x000000009e6faa70}> (0x000000009e6faa70) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 75.795 Thread 0x00000279e4d424c0 Exception <a 'java/lang/NoSuchMethodError'{0x000000009d30c6c0}: static Ljava/lang/Object;.<clinit>()V> (0x000000009d30c6c0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1139]
Event: 75.852 Thread 0x00000279e4d424c0 Implicit null exception at 0x00000279d0c56fb3 to 0x00000279d0c5803c
Event: 75.852 Thread 0x00000279e4d424c0 Implicit null exception at 0x00000279d0977083 to 0x00000279d0977bc4

ZGC Phase Switch (0 events):
No events

VM Operations (20 events):
Event: 71.590 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause) done
Event: 71.796 Executing VM operation: G1TryInitiateConcMark (G1 Humongous Allocation)
Event: 71.808 Executing VM operation: G1TryInitiateConcMark (G1 Humongous Allocation) done
Event: 72.140 Executing VM operation: G1PauseRemark
Event: 72.152 Executing VM operation: G1PauseRemark done
Event: 72.332 Executing VM operation: G1PauseCleanup
Event: 72.334 Executing VM operation: G1PauseCleanup done
Event: 72.354 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause)
Event: 72.367 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause) done
Event: 73.379 Executing VM operation: Cleanup
Event: 73.380 Executing VM operation: Cleanup done
Event: 75.849 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 75.849 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 75.849 Executing VM operation: Cleanup
Event: 75.850 Executing VM operation: Cleanup done
Event: 76.062 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 76.062 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 76.069 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 76.070 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 76.148 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause)

Memory protections (0 events):
No events

Nmethod flushes (20 events):
Event: 72.148 Thread 0x00000279dd5dfcc0 flushing  nmethod 0x00000279c9bbd110
Event: 72.148 Thread 0x00000279dd5dfcc0 flushing  nmethod 0x00000279c9be4790
Event: 72.148 Thread 0x00000279dd5dfcc0 flushing  nmethod 0x00000279c9be5490
Event: 72.148 Thread 0x00000279dd5dfcc0 flushing  nmethod 0x00000279c9cccb10
Event: 72.148 Thread 0x00000279dd5dfcc0 flushing  nmethod 0x00000279c9cd3b90
Event: 72.148 Thread 0x00000279dd5dfcc0 flushing  nmethod 0x00000279c9d58a10
Event: 72.148 Thread 0x00000279dd5dfcc0 flushing  nmethod 0x00000279c9d5d190
Event: 72.148 Thread 0x00000279dd5dfcc0 flushing  nmethod 0x00000279ca4eb590
Event: 72.148 Thread 0x00000279dd5dfcc0 flushing  nmethod 0x00000279ca539e10
Event: 72.148 Thread 0x00000279dd5dfcc0 flushing  nmethod 0x00000279ca620510
Event: 72.148 Thread 0x00000279dd5dfcc0 flushing  nmethod 0x00000279ca620890
Event: 72.148 Thread 0x00000279dd5dfcc0 flushing  nmethod 0x00000279ca622610
Event: 72.148 Thread 0x00000279dd5dfcc0 flushing  nmethod 0x00000279ca794610
Event: 72.148 Thread 0x00000279dd5dfcc0 flushing  nmethod 0x00000279ca9e4c90
Event: 72.148 Thread 0x00000279dd5dfcc0 flushing  nmethod 0x00000279ca9e7410
Event: 72.148 Thread 0x00000279dd5dfcc0 flushing  nmethod 0x00000279caa39b90
Event: 72.148 Thread 0x00000279dd5dfcc0 flushing  nmethod 0x00000279caa3b690
Event: 72.148 Thread 0x00000279dd5dfcc0 flushing  nmethod 0x00000279cad26190
Event: 72.148 Thread 0x00000279dd5dfcc0 flushing  nmethod 0x00000279cb022890
Event: 72.148 Thread 0x00000279dd5dfcc0 flushing  nmethod 0x00000279cb04bb10

Events (20 events):
Event: 72.938 Thread 0x00000279f1f87b50 Thread exited: 0x00000279f1f87b50
Event: 72.938 Thread 0x00000279f1f89690 Thread exited: 0x00000279f1f89690
Event: 72.938 Thread 0x00000279f1f88220 Thread exited: 0x00000279f1f88220
Event: 72.938 Thread 0x00000279f1f83060 Thread exited: 0x00000279f1f83060
Event: 72.939 Thread 0x00000279f1f844d0 Thread exited: 0x00000279f1f844d0
Event: 72.940 Thread 0x00000279f1f84ba0 Thread exited: 0x00000279f1f84ba0
Event: 72.940 Thread 0x00000279f1f8a430 Thread exited: 0x00000279f1f8a430
Event: 74.188 Thread 0x00000279ea0f66d0 Thread exited: 0x00000279ea0f66d0
Event: 75.440 Thread 0x00000279dd870ce0 Thread exited: 0x00000279dd870ce0
Event: 75.440 Thread 0x00000279dd873030 Thread exited: 0x00000279dd873030
Event: 75.459 Thread 0x00000279dd93d950 Thread added: 0x00000279e732efa0
Event: 75.459 Thread 0x00000279dd93d950 Thread added: 0x00000279e732fdc0
Event: 75.983 Thread 0x00000279e4d424c0 Thread added: 0x00000279f21b5990
Event: 76.031 Thread 0x00000279e4d424c0 Thread added: 0x00000279f21b52c0
Event: 76.031 Thread 0x00000279e4d424c0 Thread added: 0x00000279f21b9db0
Event: 76.032 Thread 0x00000279e4d424c0 Thread added: 0x00000279f21b6e00
Event: 76.032 Thread 0x00000279e4d424c0 Thread added: 0x00000279f21bd430
Event: 76.033 Thread 0x00000279e4d424c0 Thread added: 0x00000279f21bc690
Event: 76.033 Thread 0x00000279e4d424c0 Thread added: 0x00000279f21bcd60
Event: 76.033 Thread 0x00000279e4d424c0 Thread added: 0x00000279f21ba480


Dynamic libraries:
0x00007ff7a3220000 - 0x00007ff7a322a000 	C:\Program Files\Android\Android Studio\jbr\bin\java.exe
0x00007ffe0c390000 - 0x00007ffe0c588000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ffe0aa90000 - 0x00007ffe0ab52000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ffe09a70000 - 0x00007ffe09d66000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ffe05c20000 - 0x00007ffe05cb4000 	C:\WINDOWS\SYSTEM32\apphelp.dll
0x00007ffd84cb0000 - 0x00007ffd85146000 	C:\WINDOWS\SYSTEM32\AcLayers.DLL
0x00007ffe0b840000 - 0x00007ffe0b8de000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ffe0b0e0000 - 0x00007ffe0b27d000 	C:\WINDOWS\System32\USER32.dll
0x00007ffe09ec0000 - 0x00007ffe09ee2000 	C:\WINDOWS\System32\win32u.dll
0x00007ffe0af90000 - 0x00007ffe0afbb000 	C:\WINDOWS\System32\GDI32.dll
0x00007ffe09ef0000 - 0x00007ffe0a009000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ffe09d70000 - 0x00007ffe09e0d000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ffe0a280000 - 0x00007ffe0a380000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ffe0bad0000 - 0x00007ffe0c23e000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ffe0c240000 - 0x00007ffe0c29b000 	C:\WINDOWS\System32\SHLWAPI.dll
0x00007ffe0ba10000 - 0x00007ffe0bac1000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ffe0ab60000 - 0x00007ffe0abff000 	C:\WINDOWS\System32\sechost.dll
0x00007ffe0a5f0000 - 0x00007ffe0a713000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ffe0a380000 - 0x00007ffe0a3a7000 	C:\WINDOWS\System32\bcrypt.dll
0x00000279c3690000 - 0x00000279c3693000 	C:\WINDOWS\SYSTEM32\sfc.dll
0x00007ffddeed0000 - 0x00007ffddef74000 	C:\WINDOWS\SYSTEM32\WINSPOOL.DRV
0x00007ffe0a730000 - 0x00007ffe0aa83000 	C:\WINDOWS\System32\combase.dll
0x00007ffe0ae00000 - 0x00007ffe0aead000 	C:\WINDOWS\System32\shcore.dll
0x00007ffddc230000 - 0x00007ffddc242000 	C:\WINDOWS\SYSTEM32\sfc_os.DLL
0x00007ffe02cc0000 - 0x00007ffe02cd1000 	C:\WINDOWS\SYSTEM32\SortWindows61.dll
0x00007ffe0a410000 - 0x00007ffe0a43f000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ffdecd90000 - 0x00007ffdecdab000 	C:\Program Files\Android\Android Studio\jbr\bin\VCRUNTIME140.dll
0x00007ffdf0790000 - 0x00007ffdf07a8000 	C:\Program Files\Android\Android Studio\jbr\bin\jli.dll
0x00007ffdf9360000 - 0x00007ffdf95fa000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5915_none_60b4b9d171f9c7c7\COMCTL32.dll
0x00007ffdf87b0000 - 0x00007ffdf87bc000 	C:\Program Files\Android\Android Studio\jbr\bin\vcruntime140_1.dll
0x00007ffddc100000 - 0x00007ffddc18d000 	C:\Program Files\Android\Android Studio\jbr\bin\msvcp140.dll
0x00007ffd81e90000 - 0x00007ffd82b1b000 	C:\Program Files\Android\Android Studio\jbr\bin\server\jvm.dll
0x00007ffe0ac90000 - 0x00007ffe0acfb000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ffe09870000 - 0x00007ffe098bb000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007ffe012c0000 - 0x00007ffe012ca000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ffdfe730000 - 0x00007ffdfe757000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ffe09850000 - 0x00007ffe09862000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007ffe08290000 - 0x00007ffe082a2000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ffdf5c70000 - 0x00007ffdf5c7a000 	C:\Program Files\Android\Android Studio\jbr\bin\jimage.dll
0x00007ffe07870000 - 0x00007ffe07a71000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ffdf88c0000 - 0x00007ffdf88f4000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ffe0a1f0000 - 0x00007ffe0a272000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ffdf0780000 - 0x00007ffdf078e000 	C:\Program Files\Android\Android Studio\jbr\bin\instrument.dll
0x00007ffdecd70000 - 0x00007ffdecd90000 	C:\Program Files\Android\Android Studio\jbr\bin\java.dll
0x00007ffde8180000 - 0x00007ffde8198000 	C:\Program Files\Android\Android Studio\jbr\bin\zip.dll
0x00007ffe07a80000 - 0x00007ffe08224000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ffe09380000 - 0x00007ffe093ab000 	C:\WINDOWS\SYSTEM32\Wldp.dll
0x00007ffe0b700000 - 0x00007ffe0b7cd000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ffe09950000 - 0x00007ffe09975000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ffdee2e0000 - 0x00007ffdee2f0000 	C:\Program Files\Android\Android Studio\jbr\bin\net.dll
0x00007ffe05cc0000 - 0x00007ffe05dca000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007ffe090e0000 - 0x00007ffe0914a000 	C:\WINDOWS\system32\mswsock.dll
0x00007ffddfe40000 - 0x00007ffddfe56000 	C:\Program Files\Android\Android Studio\jbr\bin\nio.dll
0x00007ffdecef0000 - 0x00007ffdecf00000 	C:\Program Files\Android\Android Studio\jbr\bin\verify.dll
0x00007ffde7b10000 - 0x00007ffde7b37000 	C:\Users\<USER>\.gradle\native\1def1411415f61bf3af743bc5b6707747c0891f09f0c88961ee8f79bc544acac\windows-amd64\native-platform.dll
0x00007ffdc7810000 - 0x00007ffdc7888000 	C:\Users\<USER>\.gradle\native\0.2.7\x86_64-windows-gnu\gradle-fileevents.dll
0x00007ffdecd60000 - 0x00007ffdecd69000 	C:\Program Files\Android\Android Studio\jbr\bin\management.dll
0x00007ffdecd40000 - 0x00007ffdecd4b000 	C:\Program Files\Android\Android Studio\jbr\bin\management_ext.dll
0x00007ffe0b830000 - 0x00007ffe0b838000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007ffe08dc0000 - 0x00007ffe08dfb000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007ffe0a720000 - 0x00007ffe0a728000 	C:\WINDOWS\System32\NSI.dll
0x00007ffde8320000 - 0x00007ffde8329000 	C:\Program Files\Android\Android Studio\jbr\bin\extnet.dll
0x00007ffe092d0000 - 0x00007ffe092e8000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007ffe08a00000 - 0x00007ffe08a38000 	C:\WINDOWS\system32\rsaenh.dll
0x00007ffe098d0000 - 0x00007ffe098fe000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007ffe092f0000 - 0x00007ffe092fc000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007ffdbabf0000 - 0x00007ffdbabf7000 	C:\WINDOWS\system32\wshunix.dll
0x00007ffe08e00000 - 0x00007ffe08eca000 	C:\WINDOWS\SYSTEM32\DNSAPI.dll
0x00007ffe00e80000 - 0x00007ffe00e8a000 	C:\Windows\System32\rasadhlp.dll
0x00007ffe00810000 - 0x00007ffe00890000 	C:\WINDOWS\System32\fwpuclnt.dll
0x00007ffe08c50000 - 0x00007ffe08c83000 	C:\WINDOWS\SYSTEM32\ntmarta.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Program Files\Android\Android Studio\jbr\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5915_none_60b4b9d171f9c7c7;C:\Program Files\Android\Android Studio\jbr\bin\server;C:\Users\<USER>\.gradle\native\1def1411415f61bf3af743bc5b6707747c0891f09f0c88961ee8f79bc544acac\windows-amd64;C:\Users\<USER>\.gradle\native\0.2.7\x86_64-windows-gnu

VM Arguments:
jvm_args: --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.xml/javax.xml.namespace=ALL-UNNAMED -Xmx2048m -Dfile.encoding=UTF-8 -Duser.country=US -Duser.language=en -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.14.2-bin\2pb3mgt1p815evrl3weanttgr\gradle-8.14.2\lib\agents\gradle-instrumentation-agent-8.14.2.jar 
java_command: org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.14.2
java_class_path (initial): C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.14.2-bin\2pb3mgt1p815evrl3weanttgr\gradle-8.14.2\lib\gradle-daemon-main-8.14.2.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
     uint ConcGCThreads                            = 2                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 8                                         {product} {ergonomic}
   size_t G1HeapRegionSize                         = 1048576                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 369098752                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 2147483648                                {product} {command line}
   size_t MaxNewSize                               = 1287651328                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 1048576                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 2147483648                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
PATH=C:\Program Files\Python313\Scripts\;C:\Program Files\Python313\;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\xampp\php;C:\flutter sdk\flutter\bin;C:\Program Files\Java\jdk-21\bin;C:\OpenSSH-Win64\OpenSSH-Win64;C:\Windows\System32;C:\Program Files\Git\cmd;C:\Program Files\Git\bin;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;C:\xampp\php\php.exe;C:\scrcpy;C:\ProgramData\ComposerSetup\bin;C:\Program Files\nodejs\;C:\Program Files\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\xampp\php;C:\Program Files\Java\jdk1.8.0_111\bin;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\JetBrains\PyCharm Community Edition 2022.1.3\bin;C:\Program Files\Java\jdk1.8.0_291\bin;C:;C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Python 3.13;C:\Program Files\Void\bin;C:\Users\<USER>\platform-tools;C:\Program Files\Python313\Scripts\;C:\Program Files\Python313\;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\xampp\php;C:\flutter sdk\flutter\bin;C:\Program Files\Java\jdk-21\bin;C:\OpenSSH-Win64\OpenSSH-Win64;C:\Windows\System32;C:\Program Files\Git\cmd;C:\Program Files\Git\bin;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;C:\xampp\php\php.exe;C:\scrcpy;C:\ProgramData\ComposerSetup\bin;C:\Program Files\nodejs\;C:\Program Files\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\xampp\php;C:\Program Files\Java\jdk1.8.0_111\bin;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\JetBrains\PyCharm Community Edition 2022.1.3\bin;C:\Program Files\
USERNAME=ntc
OS=Windows_NT
PROCESSOR_IDENTIFIER=AMD64 Family 23 Model 24 Stepping 1, AuthenticAMD
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

JNI global refs:
JNI global refs: 31, weak refs: 7

JNI global refs memory usage: 835, weak refs: 833

Process memory usage:
Resident Set Size: 1160168K (5% of 23015712K total physical memory with 4054940K free physical memory)

OOME stack traces (most recent first):
Classloader memory used:
Loader org.gradle.internal.classloader.VisitableURLClassLoader$InstrumentingVisitableURLClassLoader: 61746K
Loader org.gradle.internal.classloader.VisitableURLClassLoader                         : 53186K
Loader bootstrap                                                                       : 33864K
Loader org.gradle.initialization.MixInLegacyTypesClassLoader                           : 14359K
Loader org.gradle.internal.classloader.VisitableURLClassLoader                         : 8511K
Loader jdk.internal.reflect.DelegatingClassLoader                                      : 892K
Loader jdk.internal.loader.ClassLoaders$PlatformClassLoader                            : 789K
Loader org.gradle.groovy.scripts.internal.DefaultScriptCompilationHandler$ScriptClassLoader: 300K
Loader jdk.internal.loader.ClassLoaders$AppClassLoader                                 : 242K
Loader org.codehaus.groovy.runtime.callsite.CallSiteClassLoader                        : 73760B

Classes loaded by more than one classloader:
Class com.google.common.collect.Iterables                                             : loaded 3 times (x 67B)
Class com.google.common.collect.Iterators$MergingIterator                             : loaded 3 times (x 77B)
Class com.google.common.collect.ImmutableList$SubList                                 : loaded 3 times (x 205B)
Class com.google.common.collect.RegularImmutableMap$KeySet                            : loaded 3 times (x 148B)
Class com.google.common.collect.ForwardingObject                                      : loaded 3 times (x 68B)
Class com.google.common.collect.ImmutableSet$SetBuilderImpl                           : loaded 3 times (x 72B)
Class com.google.common.collect.ImmutableList                                         : loaded 3 times (x 202B)
Class com.google.common.base.CharMatcher$InRange                                      : loaded 3 times (x 107B)
Class com.google.common.collect.ImmutableSet$Builder                                  : loaded 3 times (x 81B)
Class com.google.common.collect.SetMultimap                                           : loaded 3 times (x 66B)
Class com.google.common.collect.ImmutableMapEntrySet$RegularEntrySet                  : loaded 3 times (x 148B)
Class com.google.common.collect.RangeSet                                              : loaded 3 times (x 66B)
Class com.google.common.base.CharMatcher$NamedFastMatcher                             : loaded 3 times (x 108B)
Class com.google.common.base.CharMatcher$1                                            : loaded 3 times (x 109B)
Class com.google.common.base.Joiner$2                                                 : loaded 3 times (x 75B)
Class com.google.common.collect.RangeGwtSerializationDependencies                     : loaded 3 times (x 67B)
Class com.google.common.base.CharMatcher$IsEither                                     : loaded 3 times (x 107B)
Class com.google.common.base.CharMatcher                                              : loaded 3 times (x 107B)
Class com.google.common.collect.PeekingIterator                                       : loaded 3 times (x 66B)
Class com.google.common.base.Joiner$1                                                 : loaded 3 times (x 76B)
Class com.google.common.collect.ImmutableSetMultimap                                  : loaded 3 times (x 176B)
Class com.google.common.base.Preconditions                                            : loaded 3 times (x 67B)
Class com.google.common.base.CharMatcher$Is                                           : loaded 3 times (x 107B)
Class com.google.common.base.CharMatcher$Negated                                      : loaded 3 times (x 109B)
Class com.google.common.collect.RegularImmutableMap$BucketOverflowException           : loaded 3 times (x 78B)
Class com.google.common.collect.ImmutableSet$RegularSetBuilderImpl                    : loaded 3 times (x 73B)
Class com.google.common.collect.ImmutableRangeSet$ComplementRanges                    : loaded 3 times (x 203B)
Class org.gradle.internal.IoActions                                                   : loaded 3 times (x 67B)
Class org.gradle.api.GradleException                                                  : loaded 3 times (x 78B)
Class com.google.common.collect.ImmutableEntry                                        : loaded 3 times (x 78B)
Class com.google.common.collect.ImmutableList$1                                       : loaded 3 times (x 93B)
Class [Lcom.google.common.collect.AbstractMapEntry;                                   : loaded 3 times (x 65B)
Class com.google.common.base.CharMatcher$NegatedFastMatcher                           : loaded 3 times (x 109B)
Class com.google.common.collect.AbstractRangeSet                                      : loaded 3 times (x 110B)
Class com.google.common.base.Predicate                                                : loaded 3 times (x 66B)
Class com.google.common.collect.ImmutableMap                                          : loaded 3 times (x 116B)
Class com.google.common.base.CharMatcher$FastMatcher                                  : loaded 3 times (x 107B)
Class org.gradle.internal.Cast                                                        : loaded 3 times (x 67B)
Class org.gradle.api.Action                                                           : loaded 3 times (x 66B)
Class com.google.common.collect.RegularImmutableMap                                   : loaded 3 times (x 117B)
Class com.google.common.collect.ImmutableMap$MapViewOfValuesAsSingletonSets           : loaded 3 times (x 122B)
Class com.google.common.collect.ImmutableList$SerializedForm                          : loaded 3 times (x 69B)
Class com.google.common.base.CharMatcher$Or                                           : loaded 3 times (x 108B)
Class com.google.common.base.CharMatcher$BitSetMatcher                                : loaded 3 times (x 108B)
Class com.google.common.collect.RegularImmutableMap$Values                            : loaded 3 times (x 203B)
Class com.google.common.collect.ImmutableSet                                          : loaded 3 times (x 141B)
Class com.google.common.collect.ImmutableRangeSet$Builder                             : loaded 3 times (x 73B)
Class [Lcom.google.common.collect.ImmutableEntry;                                     : loaded 3 times (x 65B)
Class com.google.common.collect.RegularImmutableList                                  : loaded 3 times (x 209B)
Class com.google.common.collect.ImmutableMapEntry$NonTerminalImmutableMapEntry        : loaded 3 times (x 81B)
Class com.google.common.base.Function                                                 : loaded 3 times (x 66B)
Class com.google.common.collect.ImmutableMultimap                                     : loaded 3 times (x 143B)
Class com.google.common.collect.ImmutableMapEntry                                     : loaded 3 times (x 81B)
Class com.google.common.collect.RegularImmutableSortedSet                             : loaded 3 times (x 296B)
Class com.google.common.collect.UnmodifiableListIterator                              : loaded 3 times (x 91B)
Class com.google.common.base.CharMatcher$IsNot                                        : loaded 3 times (x 107B)
Class com.google.common.collect.RegularImmutableSet                                   : loaded 3 times (x 145B)
Class com.google.common.collect.ImmutableSortedSet                                    : loaded 3 times (x 296B)
Class com.google.common.collect.ImmutableAsList                                       : loaded 3 times (x 205B)
Class com.google.common.collect.Iterators$9                                           : loaded 3 times (x 77B)
Class com.google.common.collect.Lists                                                 : loaded 3 times (x 67B)
Class com.google.common.collect.ImmutableSet$CachingAsList                            : loaded 3 times (x 144B)
Class com.google.common.base.CharMatcher$AnyOf                                        : loaded 3 times (x 108B)
Class com.google.common.collect.ImmutableCollection                                   : loaded 3 times (x 121B)
Class com.google.common.base.CharMatcher$ForPredicate                                 : loaded 3 times (x 108B)
Class com.google.common.collect.Iterators$5                                           : loaded 3 times (x 78B)
Class com.google.common.collect.ImmutableBiMap                                        : loaded 3 times (x 139B)
Class com.google.common.collect.Iterators$4                                           : loaded 3 times (x 78B)
Class com.google.common.collect.SortedIterable                                        : loaded 3 times (x 66B)
Class com.google.common.collect.ImmutableMap$IteratorBasedImmutableMap                : loaded 3 times (x 122B)
Class com.google.common.collect.SingletonImmutableSet                                 : loaded 3 times (x 142B)
Class com.google.common.collect.Iterators$1                                           : loaded 3 times (x 77B)
Class com.google.common.collect.ImmutableList$ReverseImmutableList                    : loaded 3 times (x 205B)
Class org.gradle.api.UncheckedIOException                                             : loaded 3 times (x 78B)
Class com.google.common.collect.SingletonImmutableList                                : loaded 3 times (x 203B)
Class com.google.common.collect.NullnessCasts                                         : loaded 3 times (x 67B)
Class com.google.common.collect.Multimap                                              : loaded 3 times (x 66B)
Class com.google.common.collect.ImmutableMap$Builder                                  : loaded 3 times (x 78B)
Class com.google.common.collect.ImmutableMapEntrySet                                  : loaded 3 times (x 148B)
Class com.google.common.collect.AbstractIndexedListIterator                           : loaded 3 times (x 92B)
Class com.google.common.collect.ObjectArrays                                          : loaded 3 times (x 67B)
Class com.google.common.collect.AbstractIterator                                      : loaded 3 times (x 78B)
Class com.google.common.collect.BiMap                                                 : loaded 3 times (x 66B)
Class [Lcom.google.common.collect.ImmutableMapEntry;                                  : loaded 3 times (x 65B)
Class com.google.common.collect.ImmutableMap$1                                        : loaded 3 times (x 77B)
Class com.google.common.collect.IndexedImmutableSet                                   : loaded 3 times (x 148B)
Class com.google.common.base.CharMatcher$And                                          : loaded 3 times (x 108B)
Class com.google.common.collect.Lists$StringAsImmutableList                           : loaded 3 times (x 203B)
Class com.google.common.collect.ImmutableRangeSet                                     : loaded 3 times (x 111B)
Class com.google.common.collect.Iterators                                             : loaded 3 times (x 67B)
Class com.google.common.collect.Range                                                 : loaded 3 times (x 83B)
Class com.google.common.collect.RegularImmutableAsList                                : loaded 3 times (x 213B)
Class com.google.common.collect.ImmutableRangeSet$1                                   : loaded 3 times (x 206B)
Class com.google.common.collect.AbstractMultimap                                      : loaded 3 times (x 119B)
Class com.google.common.collect.ImmutableList$Builder                                 : loaded 3 times (x 73B)
Class com.google.common.collect.AbstractMapEntry                                      : loaded 3 times (x 77B)
Class com.google.common.collect.Iterators$SingletonIterator                           : loaded 3 times (x 77B)
Class com.google.common.collect.UnmodifiableIterator                                  : loaded 3 times (x 76B)
Class com.google.common.collect.Platform                                              : loaded 3 times (x 67B)
Class com.google.common.base.Joiner                                                   : loaded 3 times (x 75B)
Class com.google.common.collect.BaseImmutableMultimap                                 : loaded 3 times (x 119B)
Class com.google.common.collect.ImmutableCollection$Builder                           : loaded 3 times (x 72B)
Class com.google.common.collect.Iterators$ArrayItr                                    : loaded 3 times (x 93B)
Class com.google.common.collect.ImmutableRangeSet$AsSet                               : loaded 3 times (x 296B)
Class com.amazon.ion.impl._Private_IonContainer                                       : loaded 2 times (x 66B)
Class com.intellij.gradle.toolingExtension.impl.model.buildScriptClasspathModel.GradleBuildScriptClasspathSerializationService$ReadContext: loaded 2 times (x 68B)
Class org.gradle.internal.impldep.gnu.trove.TObjectIntHashMap                         : loaded 2 times (x 105B)
Class org.jetbrains.kotlin.idea.gradleTooling.model.allopen.AllOpenModel              : loaded 2 times (x 66B)
Class kotlin.jvm.internal.ArrayIteratorKt                                             : loaded 2 times (x 67B)
Class com.google.common.collect.ComparatorOrdering                                    : loaded 2 times (x 111B)
Class org.gradle.api.internal.DefaultClassPathProvider                                : loaded 2 times (x 72B)
Class com.google.common.collect.MapMakerInternalMap$Strength$1                        : loaded 2 times (x 76B)
Class com.google.common.collect.Maps$ViewCachingAbstractMap                           : loaded 2 times (x 121B)
Class com.android.builder.model.v2.models.BasicAndroidProject                         : loaded 2 times (x 66B)
Class com.google.common.collect.Sets$2                                                : loaded 2 times (x 135B)
Class com.amazon.ion.impl._Private_RecyclingStack$$Iterator                           : loaded 2 times (x 95B)
Class com.amazon.ion.impl.SharedSymbolTable                                           : loaded 2 times (x 89B)
Class com.amazon.ion.impl.bin.IonRawBinaryWriter                                      : loaded 2 times (x 165B)
Class com.amazon.ion.SymbolToken                                                      : loaded 2 times (x 66B)
Class kotlin.text.StringsKt__StringNumberConversionsJVMKt                             : loaded 2 times (x 67B)
Class com.google.common.collect.NaturalOrdering                                       : loaded 2 times (x 111B)
Class kotlin.text.MatchResult                                                         : loaded 2 times (x 66B)
Class kotlin.sequences.SequenceScope                                                  : loaded 2 times (x 69B)
Class kotlin.sequences.Sequence                                                       : loaded 2 times (x 66B)
Class org.gradle.tooling.ToolingModelContract                                         : loaded 2 times (x 66B)
Class [Lorg.gradle.api.internal.ClassPathProvider;                                    : loaded 2 times (x 65B)
Class org.gradle.api.internal.ClassPathProvider                                       : loaded 2 times (x 66B)
Class [Lorg.objectweb.asm.AnnotationVisitor;                                          : loaded 2 times (x 65B)
Class com.google.common.collect.MapMakerInternalMap$Strength$2                        : loaded 2 times (x 76B)
Class com.google.common.collect.Sets$3                                                : loaded 2 times (x 135B)
Class com.google.common.cache.LocalCache$LocalLoadingCache                            : loaded 2 times (x 130B)
Class com.google.common.base.AbstractIterator$State                                   : loaded 2 times (x 75B)
Class com.amazon.ion.impl.lite.IonBoolLite                                            : loaded 2 times (x 174B)
Class org.gradle.internal.impldep.gnu.trove.TObjectHash$NULL                          : loaded 2 times (x 67B)
Class kotlin.collections.CollectionsKt___CollectionsKt$asSequence$$inlined$Sequence$1 : loaded 2 times (x 71B)
Class org.gradle.internal.service.UnknownServiceException                             : loaded 2 times (x 79B)
Class com.google.common.io.ByteArrayDataInput                                         : loaded 2 times (x 66B)
Class com.google.common.collect.MapMakerInternalMap$StrongValueEntry                  : loaded 2 times (x 66B)
Class com.google.common.collect.Sets$4                                                : loaded 2 times (x 135B)
Class org.objectweb.asm.ModuleVisitor                                                 : loaded 2 times (x 77B)
Class com.google.common.cache.LocalCache$Strength$1                                   : loaded 2 times (x 77B)
Class org.gradle.internal.impldep.gnu.trove.PrimeFinder                               : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.idea.gradleTooling.model.kapt.KaptGradleModel              : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.idea.gradleTooling.KotlinDslScriptAdditionalTask           : loaded 2 times (x 66B)
Class org.jetbrains.plugins.gradle.model.ClasspathEntryModel                          : loaded 2 times (x 66B)
Class com.google.common.util.concurrent.SettableFuture                                : loaded 2 times (x 110B)
Class com.google.common.cache.LocalCache$Strength$2                                   : loaded 2 times (x 77B)
Class com.amazon.ion.IonTimestamp                                                     : loaded 2 times (x 66B)
Class com.amazon.ion.impl.bin.PooledBlockAllocatorProvider$PooledBlockAllocator       : loaded 2 times (x 77B)
Class org.jetbrains.plugins.gradle.tooling.serialization.GradleExtensionsSerializationService: loaded 2 times (x 73B)
Class com.android.ide.gradle.model.AdditionalClassifierArtifactsModelParameter        : loaded 2 times (x 66B)
Class org.gradle.api.JavaVersion                                                      : loaded 2 times (x 75B)
Class org.gradle.tooling.model.Dependency                                             : loaded 2 times (x 66B)
Class com.google.common.io.ByteStreams                                                : loaded 2 times (x 67B)
Class org.objectweb.asm.Handle                                                        : loaded 2 times (x 68B)
Class com.google.common.collect.Interners$InternerImpl                                : loaded 2 times (x 71B)
Class com.google.common.collect.Interner                                              : loaded 2 times (x 66B)
Class com.google.common.collect.TransformedIterator                                   : loaded 2 times (x 76B)
Class com.android.builder.model.v2.ide.AbstractArtifact                               : loaded 2 times (x 66B)
Class com.google.common.cache.LocalCache$Strength$3                                   : loaded 2 times (x 77B)
Class com.google.common.collect.MapDifference                                         : loaded 2 times (x 66B)
Class com.google.common.base.Splitter$1                                               : loaded 2 times (x 73B)
Class com.amazon.ion.IonDatagram                                                      : loaded 2 times (x 66B)
Class com.amazon.ion.impl._Private_RecyclingStack$ElementFactory                      : loaded 2 times (x 66B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$ImportedSymbolResolverMode$1$1   : loaded 2 times (x 74B)
Class com.amazon.ion.impl._Private_ByteTransferSink                                   : loaded 2 times (x 66B)
Class com.intellij.openapi.externalSystem.model.project.dependencies.ResolutionState  : loaded 2 times (x 75B)
Class kotlin.text.StringsKt__RegexExtensionsJVMKt                                     : loaded 2 times (x 67B)
Class com.google.common.collect.ComparisonChain$1                                     : loaded 2 times (x 77B)
Class com.google.common.collect.AllEqualOrdering                                      : loaded 2 times (x 110B)
Class kotlin.collections.ArraysKt___ArraysJvmKt                                       : loaded 2 times (x 67B)
Class kotlin.collections.CollectionsKt__CollectionsKt                                 : loaded 2 times (x 67B)
Class com.google.common.collect.MapMaker$Dummy                                        : loaded 2 times (x 75B)
Class org.objectweb.asm.FieldWriter                                                   : loaded 2 times (x 74B)
Class com.google.common.cache.CacheBuilder$NullListener                               : loaded 2 times (x 78B)
Class org.jetbrains.plugins.gradle.model.ExternalLibraryDependency                    : loaded 2 times (x 66B)
Class kotlin.text.Regex$Companion                                                     : loaded 2 times (x 67B)
Class org.gradle.internal.classpath.TransformedClassPath                              : loaded 2 times (x 92B)
Class org.gradle.internal.classloader.InstrumentingClassLoader                        : loaded 2 times (x 66B)
Class com.amazon.ion.impl._Private_RecyclingQueue$ElementFactory                      : loaded 2 times (x 66B)
Class com.google.common.collect.ImmutableListMultimap                                 : loaded 2 times (x 172B)
Class com.google.common.cache.CacheLoader$FunctionToCacheLoader                       : loaded 2 times (x 71B)
Class com.google.common.base.Splitter                                                 : loaded 2 times (x 68B)
Class org.objectweb.asm.AnnotationVisitor                                             : loaded 2 times (x 74B)
Class com.amazon.ion.IonValue                                                         : loaded 2 times (x 66B)
Class kotlin.jvm.internal.FunctionReference                                           : loaded 2 times (x 118B)
Class [Lkotlin.coroutines.intrinsics.CoroutineSingletons;                             : loaded 2 times (x 65B)
Class kotlin.jvm.internal.FunctionBase                                                : loaded 2 times (x 66B)
Class kotlin.enums.EnumEntriesList                                                    : loaded 2 times (x 219B)
Class com.google.common.collect.EmptyImmutableListMultimap                            : loaded 2 times (x 172B)
Class com.google.common.collect.Sets$1                                                : loaded 2 times (x 135B)
Class com.android.ide.common.gradle.Numeric                                           : loaded 2 times (x 73B)
Class com.google.common.base.MoreObjects                                              : loaded 2 times (x 67B)
Class com.amazon.ion.impl.bin.IonRawBinaryWriter$ContainerType                        : loaded 2 times (x 75B)
Class com.amazon.ion.impl._Private_IonTextWriterBuilder$Mutable                       : loaded 2 times (x 98B)
Class kotlin.comparisons.ComparisonsKt___ComparisonsJvmKt                             : loaded 2 times (x 67B)
Class kotlin.collections.CollectionsKt__IteratorsJVMKt                                : loaded 2 times (x 67B)
Class [Lorg.objectweb.asm.Attribute;                                                  : loaded 2 times (x 65B)
Class com.android.builder.model.v2.ide.TestInfo$Execution                             : loaded 2 times (x 75B)
Class com.google.common.collect.Iterators$EmptyModifiableIterator                     : loaded 2 times (x 82B)
Class [Lorg.objectweb.asm.Type;                                                       : loaded 2 times (x 65B)
Class org.gradle.tooling.internal.gradle.DefaultProjectIdentifier                     : loaded 2 times (x 82B)
Class org.jetbrains.plugins.gradle.model.IntelliJSettings                             : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.idea.gradleTooling.KotlinSourceSetContainer                : loaded 2 times (x 66B)
Class kotlin.Result                                                                   : loaded 2 times (x 68B)
Class org.gradle.internal.service.ServiceLocator                                      : loaded 2 times (x 66B)
Class com.google.common.collect.HashBiMap$BiEntry                                     : loaded 2 times (x 79B)
Class com.google.common.base.Ascii                                                    : loaded 2 times (x 67B)
Class com.google.common.collect.SingletonImmutableBiMap                               : loaded 2 times (x 139B)
Class com.google.common.cache.LocalCache$1                                            : loaded 2 times (x 85B)
Class kotlin.text.StringsKt__AppendableKt                                             : loaded 2 times (x 67B)
Class com.amazon.ion.IonWriter                                                        : loaded 2 times (x 66B)
Class settings_dwbk24pvkw3orvdwxwwezca2l$_run_closure1                                : loaded 2 times (x 135B)
Class com.amazon.ion.impl.IonWriterSystem                                             : loaded 2 times (x 167B)
Class com.amazon.ion.impl.bin.Symbols$2                                               : loaded 2 times (x 103B)
Class com.amazon.ion.impl.bin._Private_IonManagedWriter                               : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.android.synthetic.idea.AndroidExtensionsGradleModel        : loaded 2 times (x 66B)
Class com.intellij.gradle.toolingExtension.util.GradleVersionUtil                     : loaded 2 times (x 67B)
Class com.android.ide.gradle.model.GradlePropertiesModel                              : loaded 2 times (x 66B)
Class kotlin.sequences.TransformingSequence$iterator$1                                : loaded 2 times (x 75B)
Class kotlin.Pair                                                                     : loaded 2 times (x 68B)
Class org.gradle.tooling.internal.adapter.ProtocolToModelAdapter$SupportedPropertyInvoker: loaded 2 times (x 72B)
Class org.gradle.tooling.model.UnsupportedMethodException                             : loaded 2 times (x 78B)
Class com.google.common.collect.MapMakerInternalMap$AbstractStrongKeyEntry            : loaded 2 times (x 76B)
Class com.android.builder.model.v2.ide.BytecodeTransformation                         : loaded 2 times (x 75B)
Class com.google.common.collect.Sets$ImprovedAbstractSet                              : loaded 2 times (x 131B)
Class com.amazon.ion.impl.bin._Private_IonRawWriter                                   : loaded 2 times (x 66B)
Class com.amazon.ion.impl.bin.IonRawBinaryWriter$PreallocationMode                    : loaded 2 times (x 78B)
Class com.google.common.cache.LocalCache$2                                            : loaded 2 times (x 138B)
Class com.amazon.ion.impl.lite.IonContainerLite                                       : loaded 2 times (x 244B)
Class com.amazon.ion.IonBufferConfiguration$OversizedSymbolTableHandler               : loaded 2 times (x 66B)
Class com.amazon.ion.system.IonWriterBuilder                                          : loaded 2 times (x 70B)
Class org.jetbrains.plugins.gradle.tooling.serialization.ToolingStreamApiUtils        : loaded 2 times (x 67B)
Class com.amazon.ion.facet.Faceted                                                    : loaded 2 times (x 66B)
Class kotlin.collections.CollectionsKt__MutableCollectionsKt                          : loaded 2 times (x 67B)
Class com.amazon.ion.impl.lite.IonSystemLite                                          : loaded 2 times (x 271B)
Class com.android.builder.model.v2.ide.CodeShrinker                                   : loaded 2 times (x 75B)
Class com.google.common.collect.ImmutableEnumMap                                      : loaded 2 times (x 122B)
Class com.amazon.ion.impl.bin.utf8.Poolable                                           : loaded 2 times (x 75B)
Class com.amazon.ion.IonSymbol                                                        : loaded 2 times (x 66B)
Class org.jetbrains.plugins.gradle.tooling.serialization.AnnotationProcessingModelSerializationService$WriteContext: loaded 2 times (x 68B)
Class org.gradle.internal.impldep.gnu.trove.THash                                     : loaded 2 times (x 77B)
Class kotlin.io.TerminateException                                                    : loaded 2 times (x 78B)
Class kotlin.io.FilesKt__FilePathComponentsKt                                         : loaded 2 times (x 67B)
Class kotlin.reflect.KCallable                                                        : loaded 2 times (x 66B)
Class com.google.common.base.Optional                                                 : loaded 2 times (x 76B)
Class com.google.common.collect.ListMultimap                                          : loaded 2 times (x 66B)
Class com.google.common.util.concurrent.AbstractFuture$Trusted                        : loaded 2 times (x 66B)
Class com.android.builder.model.v2.models.AndroidDsl                                  : loaded 2 times (x 66B)
Class com.amazon.ion.impl.lite.IonStructLite                                          : loaded 2 times (x 294B)
Class com.amazon.ion.impl._Private_ReaderWriter                                       : loaded 2 times (x 66B)
Class com.amazon.ion.impl.bin.AbstractSymbolTable                                     : loaded 2 times (x 103B)
Class com.amazon.ion.Decimal                                                          : loaded 2 times (x 129B)
Class com.amazon.ion.impl.bin._Private_IonManagedBinaryWriterBuilder$AllocatorMode$1  : loaded 2 times (x 76B)
Class org.jetbrains.plugins.gradle.tooling.serialization.ProjectDependenciesSerializationService$WriteContext: loaded 2 times (x 68B)
Class org.jetbrains.plugins.gradle.tooling.serialization.ProjectDependenciesSerializationService: loaded 2 times (x 73B)
Class kotlin.collections.AbstractIterator                                             : loaded 2 times (x 79B)
Class kotlin.text.CharsKt                                                             : loaded 2 times (x 67B)
Class org.gradle.tooling.internal.adapter.CollectionMapper                            : loaded 2 times (x 69B)
Class com.google.common.io.ByteSink$AsCharSink                                        : loaded 2 times (x 76B)
Class [Lcom.android.builder.model.v2.ide.LibraryType;                                 : loaded 2 times (x 65B)
Class com.android.builder.model.v2.ide.LibraryType                                    : loaded 2 times (x 75B)
Class com.google.common.util.concurrent.UncheckedExecutionException                   : loaded 2 times (x 78B)
Class org.gradle.api.internal.classpath.EffectiveClassPath                            : loaded 2 times (x 86B)
Class com.amazon.ion.impl.lite.IonFloatLite                                           : loaded 2 times (x 182B)
Class com.amazon.ion.impl._Private_IonTextWriterBuilder                               : loaded 2 times (x 98B)
Class com.amazon.ion.impl.bin._Private_IonManagedBinaryWriterBuilder$AllocatorMode$2  : loaded 2 times (x 76B)
Class org.gradle.internal.impldep.gnu.trove.Equality                                  : loaded 2 times (x 66B)
Class com.android.ide.gradle.model.ArtifactIdentifierImpl                             : loaded 2 times (x 73B)
Class com.google.common.collect.ExplicitOrdering                                      : loaded 2 times (x 111B)
Class org.jetbrains.plugins.gradle.ExternalDependencyId                               : loaded 2 times (x 66B)
Class com.amazon.ion.impl._Private_RecyclingStack                                     : loaded 2 times (x 75B)
Class com.amazon.ion.impl._Private_IonReaderBuilder                                   : loaded 2 times (x 91B)
Class com.amazon.ion.IonMutableCatalog                                                : loaded 2 times (x 66B)
Class com.intellij.gradle.toolingExtension.impl.model.buildScriptClasspathModel.GradleBuildScriptClasspathSerializationService$WriteContext: loaded 2 times (x 68B)
Class org.gradle.tooling.internal.adapter.ProtocolToModelAdapter                      : loaded 2 times (x 76B)
Class org.gradle.internal.InternalTransformer                                         : loaded 2 times (x 66B)
Class com.android.builder.model.v2.ide.AndroidGradlePluginProjectFlags$BooleanFlag    : loaded 2 times (x 75B)
Class com.google.common.collect.AbstractMapBasedMultimap$KeySet$1                     : loaded 2 times (x 78B)
Class com.android.ide.common.gradle.Separator                                         : loaded 2 times (x 75B)
Class org.objectweb.asm.ClassWriter                                                   : loaded 2 times (x 102B)
Class com.google.common.cache.CacheLoader$UnsupportedLoadingOperationException        : loaded 2 times (x 78B)
Class kotlin.sequences.SequencesKt___SequencesKt$flatMap$2                            : loaded 2 times (x 121B)
Class org.gradle.internal.impldep.gnu.trove.TObjectCanonicalHashingStrategy           : loaded 2 times (x 76B)
Class kotlin.collections.ArraysKt___ArraysKt$asSequence$$inlined$Sequence$1           : loaded 2 times (x 71B)
Class kotlin.collections.MapsKt__MapsJVMKt                                            : loaded 2 times (x 67B)
Class kotlin.collections.EmptyList                                                    : loaded 2 times (x 170B)
Class org.gradle.internal.time.DefaultTimer                                           : loaded 2 times (x 76B)
Class org.gradle.internal.classloader.ClasspathUtil$1                                 : loaded 2 times (x 72B)
Class com.google.common.collect.FluentIterable$1                                      : loaded 2 times (x 77B)
Class com.google.common.base.Objects                                                  : loaded 2 times (x 67B)
Class org.objectweb.asm.FieldVisitor                                                  : loaded 2 times (x 73B)
Class com.google.common.collect.Ordering                                              : loaded 2 times (x 110B)
Class org.jetbrains.plugins.gradle.tooling.serialization.internal.IdeaProjectSerializationService: loaded 2 times (x 73B)
Class [Lcom.android.ide.common.repository.AgpVersion$PreviewKind;                     : loaded 2 times (x 65B)
Class com.amazon.ion.impl.bin.PooledBlockAllocatorProvider$PooledBlockAllocator$1     : loaded 2 times (x 75B)
Class com.amazon.ion.impl.lite.IonStringLite                                          : loaded 2 times (x 208B)
Class org.gradle.tooling.internal.adapter.ProtocolToModelAdapter$ViewKey              : loaded 2 times (x 68B)
Class org.gradle.api.internal.classpath.DefaultModuleRegistry$DefaultModule           : loaded 2 times (x 82B)
Class com.google.common.base.Absent                                                   : loaded 2 times (x 76B)
Class com.google.common.collect.FluentIterable$2                                      : loaded 2 times (x 77B)
Class com.android.ide.common.repository.AgpVersion                                    : loaded 2 times (x 71B)
Class com.android.builder.model.v2.CustomSourceDirectory                              : loaded 2 times (x 66B)
Class com.google.common.primitives.Ints                                               : loaded 2 times (x 67B)
Class org.gradle.internal.classloader.ClassLoaderVisitor                              : loaded 2 times (x 72B)
Class build_7nexczbjvru4fomegnac7tv1y$_run_closure1                                   : loaded 2 times (x 135B)
Class com.amazon.ion.impl.bin.Block                                                   : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.idea.gradleTooling.model.lombok.LombokModel                : loaded 2 times (x 66B)
Class com.google.common.collect.NullsFirstOrdering                                    : loaded 2 times (x 111B)
Class kotlin.ResultKt                                                                 : loaded 2 times (x 67B)
Class org.gradle.tooling.internal.adapter.ProtocolToModelAdapter$PropertyCachingMethodInvoker: loaded 2 times (x 72B)
Class com.google.common.collect.FluentIterable$3                                      : loaded 2 times (x 77B)
Class com.android.builder.model.v2.models.ProjectSyncIssues                           : loaded 2 times (x 66B)
Class com.google.common.collect.Multiset                                              : loaded 2 times (x 66B)
Class com.google.common.collect.ArrayListMultimapGwtSerializationDependencies         : loaded 2 times (x 168B)
Class com.google.common.math.MathPreconditions                                        : loaded 2 times (x 67B)
Class kotlin.collections.AbstractList$Companion                                       : loaded 2 times (x 67B)
Class kotlin.sequences.SequencesKt___SequencesKt                                      : loaded 2 times (x 67B)
Class org.jetbrains.plugins.gradle.model.ExternalTask                                 : loaded 2 times (x 66B)
Class com.amazon.ion.impl.bin.utf8.Utf8StringEncoderPool$1                            : loaded 2 times (x 72B)
Class com.amazon.ion.impl.lite.IonLoaderLite                                          : loaded 2 times (x 78B)
Class com.amazon.ion.impl.lite.IonContext                                             : loaded 2 times (x 66B)
Class com.amazon.ion.impl.IonWriterSystemText                                         : loaded 2 times (x 186B)
Class [Lcom.amazon.ion.impl.bin.IonManagedBinaryWriter$ImportedSymbolResolverMode;    : loaded 2 times (x 65B)
Class com.amazon.ion.ValueFactory                                                     : loaded 2 times (x 66B)
Class kotlin.collections.ArraysKt__ArraysKt                                           : loaded 2 times (x 67B)
Class kotlin.Result$Failure                                                           : loaded 2 times (x 68B)
Class org.gradle.internal.time.DefaultCountdownTimer                                  : loaded 2 times (x 84B)
Class kotlin.text.MatcherMatchResult$groupValues$1                                    : loaded 2 times (x 194B)
Class com.amazon.ion.impl._Private_IonReaderBuilder$TwoElementSequenceInputStream     : loaded 2 times (x 88B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$SymbolResolver                   : loaded 2 times (x 66B)
Class com.amazon.ion.impl._Private_SymbolToken                                        : loaded 2 times (x 66B)
Class com.amazon.ion.impl.bin._Private_IonManagedBinaryWriterBuilder$AllocatorMode    : loaded 2 times (x 76B)
Class org.jetbrains.plugins.gradle.tooling.util.ObjectCollector                       : loaded 2 times (x 68B)
Class org.jetbrains.plugins.gradle.model.GradleTaskModel                              : loaded 2 times (x 66B)
Class kotlin.collections.SetsKt__SetsJVMKt                                            : loaded 2 times (x 67B)
Class org.gradle.tooling.internal.adapter.ProtocolToModelAdapter$AdaptingMethodInvoker: loaded 2 times (x 72B)
Class org.gradle.tooling.internal.gradle.DefaultBuildIdentifier                       : loaded 2 times (x 75B)
Class kotlin.Function                                                                 : loaded 2 times (x 66B)
Class org.gradle.api.internal.classpath.Module                                        : loaded 2 times (x 66B)
Class com.google.common.cache.Weigher                                                 : loaded 2 times (x 66B)
Class com.amazon.ion.impl._Private_IonReaderBuilder$Mutable                           : loaded 2 times (x 91B)
Class com.intellij.openapi.externalSystem.model.project.IExternalSystemSourceType     : loaded 2 times (x 66B)
Class kotlin.io.FileAlreadyExistsException                                            : loaded 2 times (x 78B)
Class kotlin.internal.ProgressionUtilKt                                               : loaded 2 times (x 67B)
Class org.gradle.api.internal.classpath.UnknownModuleException                        : loaded 2 times (x 78B)
Class com.google.common.base.Stopwatch                                                : loaded 2 times (x 68B)
Class com.google.common.cache.LocalCache$Strength                                     : loaded 2 times (x 77B)
Class com.google.common.cache.Cache                                                   : loaded 2 times (x 66B)
Class org.jetbrains.plugins.gradle.tooling.serialization.ProjectDependenciesSerializationService$ReadContext: loaded 2 times (x 68B)
Class org.gradle.internal.impldep.gnu.trove.TObjectIdentityHashingStrategy            : loaded 2 times (x 74B)
Class com.intellij.openapi.externalSystem.model.project.dependencies.ProjectDependencies: loaded 2 times (x 66B)
Class kotlin.sequences.DistinctIterator                                               : loaded 2 times (x 79B)
Class com.google.common.util.concurrent.AbstractFuture$Cancellation                   : loaded 2 times (x 68B)
Class com.google.common.collect.ReverseNaturalOrdering                                : loaded 2 times (x 110B)
Class com.google.common.base.NullnessCasts                                            : loaded 2 times (x 67B)
Class com.google.common.base.Present                                                  : loaded 2 times (x 77B)
Class com.google.common.collect.AbstractMapBasedMultimap$WrappedCollection$WrappedIterator: loaded 2 times (x 80B)
Class com.google.common.cache.RemovalListener                                         : loaded 2 times (x 66B)
Class com.amazon.ion.IonBufferConfiguration                                           : loaded 2 times (x 69B)
Class com.amazon.ion.impl.bin.Symbols                                                 : loaded 2 times (x 67B)
Class org.jetbrains.plugins.gradle.tooling.serialization.RepositoriesModelSerializationService: loaded 2 times (x 78B)
Class com.android.ide.gradle.model.artifacts.AdditionalClassifierArtifactsModel       : loaded 2 times (x 66B)
Class org.gradle.tooling.internal.gradle.GradleProjectIdentity                        : loaded 2 times (x 66B)
Class org.gradle.api.internal.DefaultClassPathRegistry                                : loaded 2 times (x 72B)
Class com.google.common.collect.ImmutableMultimap$Keys                                : loaded 2 times (x 162B)
Class com.android.builder.model.v2.ide.JavaArtifact                                   : loaded 2 times (x 66B)
Class com.google.common.primitives.IntsMethodsForWeb                                  : loaded 2 times (x 67B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$ImportedSymbolResolverMode$2     : loaded 2 times (x 76B)
Class [Lcom.amazon.ion.impl.bin.AbstractIonWriter$WriteValueOptimization;             : loaded 2 times (x 65B)
Class org.jetbrains.plugins.gradle.tooling.serialization.RepositoriesModelSerializationService$ReadContext: loaded 2 times (x 68B)
Class kotlin.ranges.ClosedRange                                                       : loaded 2 times (x 66B)
Class org.gradle.api.internal.ClassPathRegistry                                       : loaded 2 times (x 66B)
Class org.gradle.internal.classpath.DefaultClassPath$ImmutableUniqueList              : loaded 2 times (x 195B)
Class com.google.common.base.CharMatcher$None                                         : loaded 2 times (x 108B)
Class com.google.common.collect.MapMakerInternalMap$Segment                           : loaded 2 times (x 138B)
Class com.google.common.base.ExtraObjectsMethodsForWeb                                : loaded 2 times (x 67B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$UserState                        : loaded 2 times (x 81B)
Class org.jetbrains.plugins.gradle.tooling.serialization.internal.adapter.InternalBuildIdentifier: loaded 2 times (x 71B)
Class com.amazon.ion.impl._Private_IonWriter                                          : loaded 2 times (x 66B)
Class com.amazon.ion.impl.bin._Private_IonManagedBinaryWriterBuilder                  : loaded 2 times (x 68B)
Class com.intellij.gradle.toolingExtension.impl.model.projectModel.GradleExternalProjectSerializationService$WriteContext: loaded 2 times (x 69B)
Class kotlin.text.StringsKt__StringsJVMKt                                             : loaded 2 times (x 67B)
Class com.google.common.io.ByteSink                                                   : loaded 2 times (x 72B)
Class com.android.builder.model.v2.dsl.SigningConfig                                  : loaded 2 times (x 66B)
Class com.android.builder.model.v2.models.AndroidProject                              : loaded 2 times (x 66B)
Class com.android.builder.model.v2.AndroidModel                                       : loaded 2 times (x 66B)
Class com.google.common.cache.CacheBuilder                                            : loaded 2 times (x 68B)
Class [Lcom.google.common.collect.AbstractIterator$State;                             : loaded 2 times (x 65B)
Class [Lcom.amazon.ion.SymbolToken;                                                   : loaded 2 times (x 65B)
Class kotlin.text.StringsKt__IndentKt                                                 : loaded 2 times (x 67B)
Class kotlin.collections.SetsKt                                                       : loaded 2 times (x 67B)
Class org.gradle.internal.classloader.DefaultClassLoaderFactory                       : loaded 2 times (x 78B)
Class org.gradle.internal.classpath.ClassPath                                         : loaded 2 times (x 66B)
Class com.google.common.collect.Interners$InternerBuilder                             : loaded 2 times (x 72B)
Class com.google.common.io.Files$FileByteSource                                       : loaded 2 times (x 81B)
Class org.jetbrains.kotlin.idea.gradleTooling.model.noarg.NoArgModel                  : loaded 2 times (x 66B)
Class com.google.common.base.JdkPattern                                               : loaded 2 times (x 71B)
Class org.jetbrains.plugins.gradle.model.ExternalProjectDependency                    : loaded 2 times (x 66B)
Class org.jetbrains.plugins.gradle.tooling.serialization.KotlinDslScriptsModelSerializationService: loaded 2 times (x 77B)
Class kotlin.reflect.KFunction                                                        : loaded 2 times (x 66B)
Class kotlin.collections.ArrayDeque                                                   : loaded 2 times (x 203B)
Class org.objectweb.asm.SymbolTable$Entry                                             : loaded 2 times (x 70B)
Class com.amazon.ion.system.IonSystemBuilder$Mutable                                  : loaded 2 times (x 71B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$SymbolResolverBuilder            : loaded 2 times (x 66B)
Class org.jetbrains.plugins.gradle.tooling.serialization.ExternalTestsSerializationService$WriteContext: loaded 2 times (x 68B)
Class org.jetbrains.kotlin.idea.gradleTooling.KotlinGradleModel                       : loaded 2 times (x 66B)
Class kotlin.jvm.internal.Lambda                                                      : loaded 2 times (x 71B)
Class kotlin.collections.MapsKt__MapsKt                                               : loaded 2 times (x 67B)
Class com.android.builder.model.v2.ide.Variant                                        : loaded 2 times (x 66B)
Class org.objectweb.asm.Edge                                                          : loaded 2 times (x 68B)
Class com.google.common.base.Suppliers$SupplierOfInstance                             : loaded 2 times (x 75B)
Class com.intellij.util.containers.IntObjectHashMap$ArrayProducer                     : loaded 2 times (x 66B)
Class com.amazon.ion.impl._Private_RecyclingStack$Recycler                            : loaded 2 times (x 66B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$LocalSymbolTableView             : loaded 2 times (x 103B)
Class com.amazon.ion.impl.bin.IonRawBinaryWriter$ThrowingRunnable                     : loaded 2 times (x 66B)
Class com.android.ide.gradle.model.ArtifactIdentifier                                 : loaded 2 times (x 66B)
Class org.gradle.tooling.model.Model                                                  : loaded 2 times (x 66B)
Class com.google.common.cache.LocalCache$ComputingValueReference                      : loaded 2 times (x 92B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$ImportedSymbolResolverMode$1     : loaded 2 times (x 76B)
Class com.amazon.ion.impl.lite.IonSymbolLite                                          : loaded 2 times (x 242B)
Class com.amazon.ion.UnknownSymbolException                                           : loaded 2 times (x 82B)
Class com.android.ide.gradle.model.composites.BuildMap                                : loaded 2 times (x 66B)
Class kotlin.text.MatchResult$Destructured                                            : loaded 2 times (x 68B)
Class kotlin.Metadata                                                                 : loaded 2 times (x 66B)
Class kotlin.UninitializedPropertyAccessException                                     : loaded 2 times (x 78B)
Class com.android.builder.model.v2.ide.Library                                        : loaded 2 times (x 66B)
Class com.android.builder.model.v2.ide.ArtifactDependenciesAdjacencyList              : loaded 2 times (x 66B)
Class com.android.builder.model.v2.models.Versions                                    : loaded 2 times (x 66B)
Class com.google.common.cache.LocalCache$Segment                                      : loaded 2 times (x 150B)
Class org.jetbrains.plugins.gradle.model.IntelliJProjectSettings                      : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.idea.gradleTooling.model.annotation.AnnotationBasedPluginModel: loaded 2 times (x 66B)
Class kotlin.sequences.SequencesKt___SequencesKt$filterNotNull$1                      : loaded 2 times (x 74B)
Class org.gradle.tooling.internal.adapter.ProtocolToModelAdapter$MethodInvocationCache: loaded 2 times (x 69B)
Class [Lcom.amazon.ion.impl.bin.IonRawBinaryWriter$StreamCloseMode;                   : loaded 2 times (x 65B)
Class com.android.builder.model.v2.models.ndk.NativeModule                            : loaded 2 times (x 66B)
Class com.google.common.collect.AbstractListMultimap                                  : loaded 2 times (x 168B)
Class [Lorg.objectweb.asm.SymbolTable$Entry;                                          : loaded 2 times (x 65B)
Class com.google.common.cache.CacheLoader                                             : loaded 2 times (x 70B)
Class org.jetbrains.kotlin.idea.gradleTooling.AndroidAwareGradleModelProvider$Companion: loaded 2 times (x 67B)
Class settings_dwbk24pvkw3orvdwxwwezca2l$_run_closure1$_closure2$_closure4            : loaded 2 times (x 135B)
Class org.jetbrains.plugins.gradle.model.ExternalDependency                           : loaded 2 times (x 66B)
Class com.intellij.util.containers.IntObjectHashMap                                   : loaded 2 times (x 68B)
Class org.gradle.internal.installation.GradleInstallation$1                           : loaded 2 times (x 71B)
Class org.gradle.api.internal.classpath.ModuleRegistry                                : loaded 2 times (x 66B)
Class com.android.builder.model.v2.ide.PrivacySandboxSdkInfo                          : loaded 2 times (x 66B)
Class com.android.ide.common.repository.AgpVersion$PreviewKind$Companion              : loaded 2 times (x 67B)
Class com.google.common.util.concurrent.internal.InternalFutureFailureAccess          : loaded 2 times (x 68B)
Class com.google.common.base.Suppliers                                                : loaded 2 times (x 67B)
Class com.google.common.collect.ImmutableSet$JdkBackedSetBuilderImpl                  : loaded 2 times (x 72B)
Class com.google.common.base.Splitter$SplittingIterator                               : loaded 2 times (x 80B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$SymbolState$1                    : loaded 2 times (x 76B)
Class com.amazon.ion.impl.LocalSymbolTableAsStruct$Factory                            : loaded 2 times (x 75B)
Class [Lcom.amazon.ion.IonType;                                                       : loaded 2 times (x 65B)
Class com.intellij.gradle.toolingExtension.impl.model.sourceSetDependencyModel.GradleSourceSetDependencySerialisationService$SourceSetDependencyModelReadContext: loaded 2 times (x 68B)
Class com.intellij.gradle.toolingExtension.impl.model.dependencyModel.DependencyReadContext: loaded 2 times (x 68B)
Class org.jetbrains.plugins.gradle.model.VersionCatalogsModel                         : loaded 2 times (x 66B)
Class org.gradle.tooling.internal.adapter.ObjectGraphAdapter                          : loaded 2 times (x 66B)
Class com.google.common.collect.ImmutableMultimap$Values                              : loaded 2 times (x 122B)
Class com.android.builder.model.v2.ide.Installation                                   : loaded 2 times (x 66B)
Class com.google.common.cache.LocalCache$LocalManualCache$1                           : loaded 2 times (x 71B)
Class [Lcom.google.common.cache.CacheBuilder$OneWeigher;                              : loaded 2 times (x 65B)
Class org.jetbrains.plugins.gradle.model.ProjectImportModelProvider                   : loaded 2 times (x 66B)
Class org.jetbrains.plugins.gradle.model.ExternalSourceSet                            : loaded 2 times (x 66B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$SymbolState$2                    : loaded 2 times (x 76B)
Class com.amazon.ion.IonNull                                                          : loaded 2 times (x 66B)
Class com.intellij.gradle.toolingExtension.impl.model.warmUp.GradleTaskWarmUpRequest  : loaded 2 times (x 66B)
Class com.google.common.collect.ImmutableSortedAsList                                 : loaded 2 times (x 217B)
Class kotlin.text.RegexKt                                                             : loaded 2 times (x 67B)
Class kotlin.coroutines.intrinsics.IntrinsicsKt__IntrinsicsKt                         : loaded 2 times (x 67B)
Class com.android.builder.model.v2.models.ModelBuilderParameter                       : loaded 2 times (x 66B)
Class org.objectweb.asm.MethodTooLargeException                                       : loaded 2 times (x 79B)
Class com.google.common.base.Platform$JdkPatternCompiler                              : loaded 2 times (x 71B)
Class com.amazon.ion.impl.bin.IonRawBinaryWriter$ContainerInfo                        : loaded 2 times (x 70B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$SymbolState$3                    : loaded 2 times (x 76B)
Class com.amazon.ion.impl.bin.IonRawBinaryWriter$StreamFlushMode                      : loaded 2 times (x 75B)
Class com.amazon.ion.IonBinaryWriter                                                  : loaded 2 times (x 66B)
Class com.intellij.gradle.toolingExtension.impl.model.sourceSetArtifactIndex.GradleSourceSetArtifactBuildRequest: loaded 2 times (x 66B)
Class kotlin.io.FilesKt__UtilsKt                                                      : loaded 2 times (x 67B)
Class com.google.common.collect.Lists$TransformingRandomAccessList                    : loaded 2 times (x 195B)
Class kotlin.collections.CollectionsKt__CollectionsJVMKt                              : loaded 2 times (x 67B)
Class org.gradle.api.internal.classpath.DefaultModuleRegistry                         : loaded 2 times (x 82B)
Class com.google.common.util.concurrent.ListenableFuture                              : loaded 2 times (x 66B)
Class com.google.common.cache.ReferenceEntry                                          : loaded 2 times (x 66B)
Class com.google.common.base.Converter                                                : loaded 2 times (x 86B)
Class com.google.common.collect.MapMakerInternalMap$Strength                          : loaded 2 times (x 76B)
Class com.google.common.collect.MapMakerInternalMap$StrongKeyDummyValueEntry$LinkedStrongKeyDummyValueEntry: loaded 2 times (x 78B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$SymbolState$4                    : loaded 2 times (x 76B)
Class org.jetbrains.plugins.gradle.tooling.serialization.internal.IdeaProjectSerializationService$WriteContext$1: loaded 2 times (x 79B)
Class org.jetbrains.plugins.gradle.model.ExternalProject                              : loaded 2 times (x 66B)
Class kotlin.collections.CollectionsKt__IteratorsKt                                   : loaded 2 times (x 67B)
Class kotlin.sequences.SequencesKt                                                    : loaded 2 times (x 67B)
Class [Lcom.android.builder.model.v2.ide.CodeShrinker;                                : loaded 2 times (x 65B)
Class com.google.common.collect.AbstractMapBasedMultimap                              : loaded 2 times (x 135B)
Class com.google.common.util.concurrent.AbstractFuture                                : loaded 2 times (x 102B)
Class com.google.common.base.FunctionalEquivalence                                    : loaded 2 times (x 79B)
Class org.gradle.internal.operations.MultipleBuildOperationFailures                   : loaded 2 times (x 91B)
Class org.gradle.tooling.internal.adapter.MethodInvocation                            : loaded 2 times (x 81B)
Class com.amazon.ion.impl.bin.utf8.Pool                                               : loaded 2 times (x 70B)
Class com.amazon.ion.impl._Private_SymtabExtendsCache                                 : loaded 2 times (x 68B)
Class org.jetbrains.plugins.gradle.tooling.serialization.internal.IdeaProjectSerializationService$WriteContext$2: loaded 2 times (x 79B)
Class kotlin.text.StringsKt                                                           : loaded 2 times (x 67B)
Class com.google.common.collect.ImmutableMultiset                                     : loaded 2 times (x 159B)
Class org.objectweb.asm.RecordComponentVisitor                                        : loaded 2 times (x 73B)
Class com.google.common.cache.CacheLoader$SupplierToCacheLoader                       : loaded 2 times (x 71B)
Class com.amazon.ion.impl._Private_IonSymbol                                          : loaded 2 times (x 66B)
Class com.amazon.ion.impl.lite.IonValueLite                                           : loaded 2 times (x 143B)
Class com.amazon.ion.IonContainer                                                     : loaded 2 times (x 66B)
Class com.amazon.ion.impl.bin.PooledBlockAllocatorProvider                            : loaded 2 times (x 69B)
Class org.jetbrains.plugins.gradle.tooling.serialization.AnnotationProcessingModelSerializationService$ReadContext: loaded 2 times (x 68B)
Class org.jetbrains.plugins.gradle.tooling.serialization.internal.IdeaProjectSerializationService$WriteContext$3: loaded 2 times (x 79B)
Class com.android.ide.gradle.model.artifacts.AdditionalClassifierArtifacts            : loaded 2 times (x 66B)
Class org.gradle.util.internal.DefaultGradleVersion$Stage                             : loaded 2 times (x 71B)
Class kotlin.coroutines.jvm.internal.RestrictedContinuationImpl                       : loaded 2 times (x 83B)
Class com.google.common.collect.Maps$EntryTransformer                                 : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.idea.gradleTooling.AndroidAwareGradleModelProvider         : loaded 2 times (x 79B)
Class org.jetbrains.plugins.gradle.tooling.serialization.internal.IdeaProjectSerializationService$WriteContext$4: loaded 2 times (x 79B)
Class com.intellij.gradle.toolingExtension.impl.model.sourceSetDependencyModel.GradleSourceSetDependencySerialisationService$Companion: loaded 2 times (x 67B)
Class [Lkotlin.Pair;                                                                  : loaded 2 times (x 65B)
Class kotlin.text.StringsKt__StringNumberConversionsKt                                : loaded 2 times (x 67B)
Class kotlin.io.FilesKt__FileReadWriteKt                                              : loaded 2 times (x 67B)
Class com.google.common.collect.UsingToStringOrdering                                 : loaded 2 times (x 110B)
Class kotlin.text.MatchNamedGroupCollection                                           : loaded 2 times (x 66B)
Class kotlin.jvm.internal.markers.KMutableList                                        : loaded 2 times (x 66B)
Class org.gradle.internal.time.MonotonicClock                                         : loaded 2 times (x 73B)
Class org.gradle.internal.service.ServiceLookupException                              : loaded 2 times (x 78B)
Class kotlin.sequences.TransformingSequence                                           : loaded 2 times (x 71B)
Class com.google.common.collect.Lists$ReverseList                                     : loaded 2 times (x 196B)
Class [Lcom.google.common.cache.LocalCache$EntryFactory;                              : loaded 2 times (x 65B)
Class com.amazon.ion.BufferConfiguration$Builder                                      : loaded 2 times (x 74B)
Class com.intellij.util.ThrowableConsumer                                             : loaded 2 times (x 66B)
Class com.intellij.gradle.toolingExtension.impl.model.sourceSetDependencyModel.GradleSourceSetDependencySerialisationService$SourceSetDependencyModelWriteContext: loaded 2 times (x 68B)
Class [Lkotlin.sequences.Sequence;                                                    : loaded 2 times (x 65B)
Class kotlin.sequences.SequencesKt___SequencesKt$sortedWith$1                         : loaded 2 times (x 71B)
Class org.gradle.internal.classloader.ClassLoaderFactory                              : loaded 2 times (x 66B)
Class com.google.common.collect.ImmutableMap$SerializedForm                           : loaded 2 times (x 69B)
Class com.android.builder.model.v2.ide.LibraryInfo                                    : loaded 2 times (x 66B)
Class com.google.common.cache.LocalCache$LocalManualCache                             : loaded 2 times (x 95B)
Class settings_dwbk24pvkw3orvdwxwwezca2l$_run_closure1$_closure2$_closure3            : loaded 2 times (x 135B)
Class com.google.common.base.CommonPattern                                            : loaded 2 times (x 70B)
Class com.amazon.ion.impl.bin.IntList                                                 : loaded 2 times (x 73B)
Class com.amazon.ion.impl._Private_RecyclingQueue$ElementIterator                     : loaded 2 times (x 78B)
Class com.amazon.ion.impl.lite._Private_LiteDomTrampoline                             : loaded 2 times (x 67B)
Class com.amazon.ion.IonType                                                          : loaded 2 times (x 75B)
Class org.jetbrains.plugins.gradle.tooling.serialization.internal.IdeaProjectSerializationService$WriteContext$6: loaded 2 times (x 79B)
Class org.jetbrains.plugins.gradle.tooling.util.IntObjectMap                          : loaded 2 times (x 69B)
Class org.gradle.internal.service.CachingServiceLocator                               : loaded 2 times (x 78B)
Class org.objectweb.asm.ClassVisitor                                                  : loaded 2 times (x 84B)
Class com.google.common.collect.Streams                                               : loaded 2 times (x 67B)
Class com.google.common.collect.AbstractMapBasedMultimap$KeySet                       : loaded 2 times (x 133B)
Class com.google.common.math.IntMath                                                  : loaded 2 times (x 67B)
Class com.amazon.ion.impl.lite.ValueFactoryLite                                       : loaded 2 times (x 214B)
Class org.jetbrains.plugins.gradle.tooling.serialization.internal.IdeaProjectSerializationService$WriteContext$7: loaded 2 times (x 79B)
Class org.jetbrains.plugins.gradle.model.RepositoryModels                             : loaded 2 times (x 66B)
Class kotlin.collections.AbstractList$SubList                                         : loaded 2 times (x 194B)
Class kotlin.jvm.internal.CallableReference                                           : loaded 2 times (x 102B)
Class com.google.common.collect.MapMakerInternalMap$InternalEntry                     : loaded 2 times (x 66B)
Class [Lcom.google.common.collect.HashBiMap$BiEntry;                                  : loaded 2 times (x 65B)
Class org.objectweb.asm.commons.InstructionAdapter                                    : loaded 2 times (x 184B)
Class [Lorg.objectweb.asm.Symbol;                                                     : loaded 2 times (x 65B)
Class settings_dwbk24pvkw3orvdwxwwezca2l$_run_closure1$_closure2                      : loaded 2 times (x 135B)
Class com.amazon.ion.impl.bin.WriteBuffer                                             : loaded 2 times (x 74B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter                                  : loaded 2 times (x 162B)
Class org.jetbrains.plugins.gradle.tooling.serialization.internal.IdeaProjectSerializationService$WriteContext$8: loaded 2 times (x 79B)
Class kotlin.ranges.IntRange                                                          : loaded 2 times (x 89B)
Class [Lorg.gradle.api.JavaVersion;                                                   : loaded 2 times (x 65B)
Class org.gradle.tooling.model.idea.IdeaDependencyScope                               : loaded 2 times (x 66B)
Class org.gradle.internal.time.TimeSource                                             : loaded 2 times (x 66B)
Class org.gradle.tooling.model.kotlin.dsl.KotlinDslScriptsModel                       : loaded 2 times (x 66B)
Class org.objectweb.asm.signature.SignatureVisitor                                    : loaded 2 times (x 83B)
Class com.android.utils.FileUtils                                                     : loaded 2 times (x 67B)
Class com.android.builder.model.v2.ide.TestInfo                                       : loaded 2 times (x 66B)
Class com.google.common.base.Equivalence                                              : loaded 2 times (x 78B)
Class com.android.builder.model.v2.ide.AaptOptions$Namespacing                        : loaded 2 times (x 75B)
Class com.google.common.collect.TransformedListIterator                               : loaded 2 times (x 89B)
Class com.amazon.ion.impl.lite.IonBlobLite                                            : loaded 2 times (x 215B)
Class com.amazon.ion.impl.LocalSymbolTable                                            : loaded 2 times (x 117B)
Class org.jetbrains.plugins.gradle.tooling.serialization.SerializationService         : loaded 2 times (x 66B)
Class org.gradle.tooling.internal.gradle.GradleBuildIdentity                          : loaded 2 times (x 66B)
Class kotlin.enums.EnumEntries                                                        : loaded 2 times (x 66B)
Class com.android.builder.model.v2.ide.JavaCompileOptions                             : loaded 2 times (x 66B)
Class com.google.common.collect.IndexedImmutableSet$1                                 : loaded 2 times (x 209B)
Class com.google.common.cache.CacheBuilder$OneWeigher                                 : loaded 2 times (x 78B)
Class com.google.common.cache.LocalCache$Values                                       : loaded 2 times (x 114B)
Class com.amazon.ion.IonList                                                          : loaded 2 times (x 66B)
Class org.jetbrains.plugins.gradle.tooling.serialization.internal.adapter.Supplier    : loaded 2 times (x 66B)
Class ijInit1_cxra44aza6j7soyehk88aj27j                                               : loaded 2 times (x 175B)
Class org.gradle.tooling.internal.adapter.ProtocolToModelAdapter$MixInMappingAction   : loaded 2 times (x 76B)
Class org.gradle.internal.exceptions.ResolutionProvider                               : loaded 2 times (x 66B)
Class com.amazon.ion.IonSystem                                                        : loaded 2 times (x 66B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$ImportedSymbolContext            : loaded 2 times (x 68B)
Class kotlin.text.MatchResult$DefaultImpls                                            : loaded 2 times (x 67B)
Class org.gradle.tooling.model.idea.IdeaCompilerOutput                                : loaded 2 times (x 66B)
Class org.gradle.tooling.model.ProjectIdentifier                                      : loaded 2 times (x 66B)
Class org.gradle.internal.classloader.ClasspathUtil                                   : loaded 2 times (x 67B)
Class com.android.builder.model.v2.ide.ComponentInfo                                  : loaded 2 times (x 66B)
Class com.google.common.util.concurrent.Uninterruptibles                              : loaded 2 times (x 67B)
Class com.android.builder.model.v2.ide.ProjectType                                    : loaded 2 times (x 75B)
Class com.intellij.gradle.toolingExtension.impl.model.sourceSetModel.GradleSourceSetSerialisationService: loaded 2 times (x 73B)
Class kotlin.io.FileSystemException                                                   : loaded 2 times (x 78B)
Class com.google.common.collect.ComparisonChain                                       : loaded 2 times (x 76B)
Class com.android.builder.model.v2.dsl.BaseConfig                                     : loaded 2 times (x 66B)
Class com.google.common.util.concurrent.LazyLogger                                    : loaded 2 times (x 68B)
Class com.google.common.collect.Maps                                                  : loaded 2 times (x 67B)
Class com.amazon.ion.impl.lite.IonIntLite                                             : loaded 2 times (x 185B)
Class com.amazon.ion.util._Private_FastAppendable                                     : loaded 2 times (x 66B)
Class [Lcom.amazon.ion.impl.bin.IonRawBinaryWriter$PreallocationMode;                 : loaded 2 times (x 65B)
Class sync_studio_tooling8_7zmlaftbuj9d91bccp71mgenz                                  : loaded 2 times (x 175B)
Class kotlin.sequences.DistinctSequence                                               : loaded 2 times (x 71B)
Class org.gradle.internal.time.TimeSource$1                                           : loaded 2 times (x 73B)
Class kotlin.jvm.internal.DefaultConstructorMarker                                    : loaded 2 times (x 67B)
Class org.gradle.api.specs.Spec                                                       : loaded 2 times (x 66B)
Class com.google.common.collect.MapMaker                                              : loaded 2 times (x 68B)
Class org.gradle.tooling.internal.adapter.TargetTypeProvider                          : loaded 2 times (x 66B)
Class com.amazon.ion.impl.lite.IonSexpLite                                            : loaded 2 times (x 486B)
Class com.amazon.ion.impl.lite.IonListLite                                            : loaded 2 times (x 486B)
Class com.amazon.ion.IonFloat                                                         : loaded 2 times (x 66B)
Class build_d5mdhodvhdi9ygnsd0pgs2lzl$_run_closure1                                   : loaded 2 times (x 135B)
Class com.google.common.cache.LocalCache$LoadingValueReference                        : loaded 2 times (x 92B)
Class com.amazon.ion.impl.LocalSymbolTable$Factory                                    : loaded 2 times (x 73B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$ImportedSymbolResolverMode       : loaded 2 times (x 76B)
Class com.intellij.gradle.toolingExtension.impl.model.buildScriptClasspathModel.GradleBuildScriptClasspathSerializationService: loaded 2 times (x 73B)
Class kotlin.jvm.internal.ArrayIterator                                               : loaded 2 times (x 75B)
Class com.google.common.collect.MapMakerInternalMap$StrongKeyDummyValueEntry          : loaded 2 times (x 78B)
Class com.google.common.base.Platform                                                 : loaded 2 times (x 67B)
Class com.amazon.ion.impl.LocalSymbolTableAsStruct                                    : loaded 2 times (x 122B)
Class com.amazon.ion.impl.lite.IonTimestampLite                                       : loaded 2 times (x 188B)
Class org.jetbrains.plugins.gradle.model.FileCollectionDependency                     : loaded 2 times (x 66B)
Class org.gradle.internal.impldep.gnu.trove.TObjectHashingStrategy                    : loaded 2 times (x 66B)
Class com.intellij.gradle.toolingExtension.impl.model.utilDummyModel.DummyModel       : loaded 2 times (x 66B)
Class kotlin.collections.EmptyIterator                                                : loaded 2 times (x 85B)
Class kotlin.text.StringsKt__RegexExtensionsKt                                        : loaded 2 times (x 67B)
Class org.gradle.internal.time.Clock                                                  : loaded 2 times (x 66B)
Class [Lorg.objectweb.asm.AnnotationWriter;                                           : loaded 2 times (x 65B)
Class com.google.common.collect.AbstractMapBasedMultimap$WrappedList                  : loaded 2 times (x 201B)
Class com.android.builder.model.v2.dsl.ProductFlavor                                  : loaded 2 times (x 66B)
Class [Lcom.android.builder.model.v2.ide.TestInfo$Execution;                          : loaded 2 times (x 65B)
Class com.google.common.collect.Maps$8                                                : loaded 2 times (x 78B)
Class com.amazon.ion.IonString                                                        : loaded 2 times (x 66B)
Class org.jetbrains.plugins.gradle.model.GradleSourceSetDependencyModel               : loaded 2 times (x 66B)
Class kotlin.io.FilePathComponents                                                    : loaded 2 times (x 68B)
Class com.google.common.collect.CompoundOrdering                                      : loaded 2 times (x 111B)
Class kotlin.sequences.SequencesKt__SequencesJVMKt                                    : loaded 2 times (x 67B)
Class kotlin.coroutines.jvm.internal.RestrictedSuspendLambda                          : loaded 2 times (x 87B)
Class org.gradle.tooling.internal.adapter.TypeInspector                               : loaded 2 times (x 69B)
Class org.gradle.tooling.internal.adapter.ProtocolToModelAdapter$NoOpDecoration       : loaded 2 times (x 75B)
Class org.gradle.tooling.model.Element                                                : loaded 2 times (x 66B)
Class kotlin.collections.AbstractList                                                 : loaded 2 times (x 193B)
Class kotlin.collections.AbstractCollection                                           : loaded 2 times (x 113B)
Class [Lcom.google.common.collect.Iterators$EmptyModifiableIterator;                  : loaded 2 times (x 65B)
Class kotlin.ranges.IntProgression$Companion                                          : loaded 2 times (x 67B)
Class com.google.common.collect.Lists$ReverseList$1                                   : loaded 2 times (x 95B)
Class com.google.common.collect.Sets$SetView                                          : loaded 2 times (x 134B)
Class com.google.common.cache.LocalCache$StrongValueReference                         : loaded 2 times (x 86B)
Class com.amazon.ion.BufferConfiguration                                              : loaded 2 times (x 69B)
Class com.amazon.ion.system.SimpleCatalog                                             : loaded 2 times (x 87B)
Class com.intellij.gradle.toolingExtension.impl.model.taskModel.GradleTaskSerialisationService: loaded 2 times (x 72B)
Class com.android.ide.gradle.model.LegacyV1AgpVersionModel                            : loaded 2 times (x 66B)
Class kotlin.text.StringsKt__StringBuilderJVMKt                                       : loaded 2 times (x 67B)
Class kotlin.text.Regex                                                               : loaded 2 times (x 68B)
Class kotlin.sequences.SequencesKt__SequenceBuilderKt$sequence$$inlined$Sequence$1    : loaded 2 times (x 71B)
Class com.google.common.graph.SuccessorsFunction                                      : loaded 2 times (x 66B)
Class org.objectweb.asm.Type                                                          : loaded 2 times (x 68B)
Class org.jetbrains.plugins.gradle.tooling.util.IntObjectMap$1                        : loaded 2 times (x 73B)
Class org.gradle.internal.impldep.com.google.common.base.Preconditions                : loaded 2 times (x 67B)
Class org.gradle.tooling.model.internal.ImmutableDomainObjectSet                      : loaded 2 times (x 151B)
Class org.gradle.internal.classloader.VisitableURLClassLoader$InstrumentingVisitableURLClassLoader: loaded 2 times (x 119B)
Class com.google.common.collect.MapMakerInternalMap$StrongKeyDummyValueEntry$Helper   : loaded 2 times (x 75B)
Class com.google.common.io.Files$FileByteSink                                         : loaded 2 times (x 73B)
Class com.android.builder.model.v2.ide.AndroidGradlePluginProjectFlags                : loaded 2 times (x 66B)
Class [Lcom.android.builder.model.v2.ide.AndroidGradlePluginProjectFlags$BooleanFlag; : loaded 2 times (x 65B)
Class com.android.builder.model.v2.ide.BasicVariant                                   : loaded 2 times (x 66B)
Class org.objectweb.asm.ByteVector                                                    : loaded 2 times (x 75B)
Class [Lcom.google.common.cache.CacheBuilder$NullListener;                            : loaded 2 times (x 65B)
Class com.google.common.math.IntMath$1                                                : loaded 2 times (x 67B)
Class [Lcom.amazon.ion.impl.bin.IonRawBinaryWriter$StreamFlushMode;                   : loaded 2 times (x 65B)
Class com.amazon.ion.UnsupportedIonVersionException                                   : loaded 2 times (x 81B)
Class kotlin.comparisons.ComparisonsKt__ComparisonsKt                                 : loaded 2 times (x 67B)
Class kotlin.ranges.RangesKt__RangesKt                                                : loaded 2 times (x 67B)
Class kotlin.collections.SetsKt___SetsKt                                              : loaded 2 times (x 67B)
Class kotlin.sequences.SequencesKt__SequencesKt                                       : loaded 2 times (x 67B)
Class kotlin.coroutines.Continuation                                                  : loaded 2 times (x 66B)
Class org.gradle.internal.classloader.VisitableURLClassLoader$Spec                    : loaded 2 times (x 70B)
Class org.gradle.internal.classpath.DefaultClassPath                                  : loaded 2 times (x 86B)
Class com.android.builder.model.v2.ide.BundleInfo                                     : loaded 2 times (x 66B)
Class com.android.builder.model.v2.ide.SourceProvider                                 : loaded 2 times (x 66B)
Class org.objectweb.asm.Symbol                                                        : loaded 2 times (x 69B)
Class com.google.common.base.AbstractIterator                                         : loaded 2 times (x 76B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$SymbolState                      : loaded 2 times (x 76B)
Class com.amazon.ion.IonStruct                                                        : loaded 2 times (x 66B)
Class org.jetbrains.plugins.gradle.model.GradleBuildScriptClasspathModel              : loaded 2 times (x 66B)
Class kotlin.text.CharsKt__CharJVMKt                                                  : loaded 2 times (x 67B)
Class kotlin.sequences.FilteringSequence                                              : loaded 2 times (x 71B)
Class kotlin.comparisons.ComparisonsKt                                                : loaded 2 times (x 67B)
Class kotlin.comparisons.ComparisonsKt___ComparisonsKt                                : loaded 2 times (x 67B)
Class kotlin.coroutines.jvm.internal.BaseContinuationImpl                             : loaded 2 times (x 83B)
Class kotlin.Result$Companion                                                         : loaded 2 times (x 67B)
Class org.gradle.util.internal.DefaultGradleVersion                                   : loaded 2 times (x 80B)
Class com.google.common.io.Closer                                                     : loaded 2 times (x 74B)
Class org.jetbrains.plugins.gradle.model.AnnotationProcessingModel                    : loaded 2 times (x 66B)
Class org.jetbrains.plugins.gradle.model.GradleExtensions                             : loaded 2 times (x 66B)
Class com.amazon.ion.system.IonBinaryWriterBuilder                                    : loaded 2 times (x 95B)
Class org.gradle.internal.classloader.ClassLoaderSpec                                 : loaded 2 times (x 67B)
Class org.objectweb.asm.AnnotationWriter                                              : loaded 2 times (x 75B)
Class com.google.common.cache.CacheStats                                              : loaded 2 times (x 67B)
Class com.amazon.ion.impl.IonWriterSystemTextMarkup                                   : loaded 2 times (x 188B)
Class com.amazon.ion.impl.bin.BlockAllocator                                          : loaded 2 times (x 76B)
Class kotlin.jvm.internal.FunctionReferenceImpl                                       : loaded 2 times (x 118B)
Class kotlin.collections.CollectionsKt___CollectionsJvmKt                             : loaded 2 times (x 67B)
Class com.google.common.collect.Interners                                             : loaded 2 times (x 67B)
Class com.google.common.io.Closer$Suppressor                                          : loaded 2 times (x 66B)
Class com.google.common.io.LineProcessor                                              : loaded 2 times (x 66B)
Class com.google.common.collect.ImmutableMultisetGwtSerializationDependencies         : loaded 2 times (x 121B)
Class org.objectweb.asm.RecordComponentWriter                                         : loaded 2 times (x 74B)
Class [Lcom.android.builder.model.v2.ide.ProjectType;                                 : loaded 2 times (x 65B)
Class com.google.common.util.concurrent.AbstractFuture$Listener                       : loaded 2 times (x 68B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$UserState$4                      : loaded 2 times (x 81B)
Class com.amazon.ion.impl._Private_RecyclingQueue                                     : loaded 2 times (x 76B)
Class com.amazon.ion.system.IonWriterBuilderBase                                      : loaded 2 times (x 79B)
Class kotlin.TuplesKt                                                                 : loaded 2 times (x 67B)
Class kotlin.jvm.internal.markers.KMutableIterable                                    : loaded 2 times (x 66B)
Class kotlin.sequences.SequencesKt___SequencesJvmKt                                   : loaded 2 times (x 67B)
Class org.gradle.tooling.internal.adapter.ProtocolToModelAdapter$DefaultViewBuilder   : loaded 2 times (x 76B)
Class kotlin.Unit                                                                     : loaded 2 times (x 67B)
Class [Lorg.objectweb.asm.Label;                                                      : loaded 2 times (x 65B)
Class [Lcom.android.builder.model.v2.ide.AaptOptions$Namespacing;                     : loaded 2 times (x 65B)
Class com.android.ide.common.gradle.Version$Companion$ParseState                      : loaded 2 times (x 66B)
Class org.objectweb.asm.Attribute                                                     : loaded 2 times (x 73B)
Class com.google.common.base.Supplier                                                 : loaded 2 times (x 66B)
Class kotlin.ranges.RangesKt                                                          : loaded 2 times (x 67B)
Class org.gradle.tooling.internal.adapter.ProtocolToModelAdapter$ViewGraphDetails$1   : loaded 2 times (x 73B)
Class org.gradle.internal.classloader.VisitableURLClassLoader                         : loaded 2 times (x 113B)
Class org.jetbrains.plugins.gradle.model.UnresolvedExternalDependency                 : loaded 2 times (x 66B)
Class org.objectweb.asm.signature.SignatureWriter                                     : loaded 2 times (x 84B)
Class com.android.ide.common.repository.AgpVersion$Companion                          : loaded 2 times (x 67B)
Class com.amazon.ion.impl.bin.IonRawBinaryWriter$1                                    : loaded 2 times (x 73B)
Class [Lcom.amazon.ion.SymbolTable;                                                   : loaded 2 times (x 65B)
Class com.amazon.ion.impl.bin.IonBinaryWriterAdapter$Factory                          : loaded 2 times (x 66B)
Class com.amazon.ion.impl.BlockedBuffer$BufferedOutputStream                          : loaded 2 times (x 86B)
Class org.jetbrains.plugins.gradle.tooling.serialization.ExternalTestsSerializationService$ReadContext: loaded 2 times (x 68B)
Class kotlin.collections.ArraysUtilJVM                                                : loaded 2 times (x 67B)
Class kotlin.sequences.FlatteningSequence$iterator$1                                  : loaded 2 times (x 75B)
Class kotlin.jvm.functions.Function0                                                  : loaded 2 times (x 66B)
Class com.android.builder.model.v2.ide.AndroidArtifact                                : loaded 2 times (x 66B)
Class com.google.common.io.ByteArrayDataOutput                                        : loaded 2 times (x 66B)
Class org.gradle.tooling.model.idea.IdeaDependency                                    : loaded 2 times (x 66B)
Class com.google.common.collect.MapMakerInternalMap                                   : loaded 2 times (x 157B)
Class com.android.builder.model.v2.ide.AaptOptions                                    : loaded 2 times (x 66B)
Class [Lcom.android.builder.model.v2.ide.BytecodeTransformation;                      : loaded 2 times (x 65B)
Class com.google.common.collect.HashBiMap                                             : loaded 2 times (x 139B)
Class com.google.common.base.Strings                                                  : loaded 2 times (x 67B)
Class com.android.ide.common.gradle.Version$Companion                                 : loaded 2 times (x 67B)
Class com.google.common.collect.Sets                                                  : loaded 2 times (x 67B)
Class org.objectweb.asm.Handler                                                       : loaded 2 times (x 68B)
Class com.google.common.base.Ticker                                                   : loaded 2 times (x 68B)
Class org.gradle.api.internal.classpath.ManifestUtil                                  : loaded 2 times (x 67B)
Class com.amazon.ion.impl.bin.AbstractIonWriter$WriteValueOptimization                : loaded 2 times (x 75B)
Class kotlin.collections.AbstractMutableList                                          : loaded 2 times (x 202B)
Class kotlin.jvm.functions.Function1                                                  : loaded 2 times (x 66B)
Class com.android.builder.model.v2.models.ndk.NativeModelBuilderParameter             : loaded 2 times (x 66B)
Class [Lcom.google.common.cache.LocalCache$Segment;                                   : loaded 2 times (x 65B)
Class [Lcom.google.common.cache.LocalCache$Strength;                                  : loaded 2 times (x 65B)
Class com.google.common.cache.CacheBuilder$1                                          : loaded 2 times (x 81B)
Class org.gradle.tooling.internal.adapter.ProtocolToModelAdapter$InvocationHandlerImpl: loaded 2 times (x 73B)
Class org.gradle.tooling.model.build.GradleEnvironment                                : loaded 2 times (x 66B)
Class com.amazon.ion.impl.bin.BlockAllocatorProvider                                  : loaded 2 times (x 68B)
Class org.jetbrains.plugins.gradle.tooling.serialization.internal.IdeaProjectSerializationService$WriteContext: loaded 2 times (x 68B)
Class com.intellij.gradle.toolingExtension.impl.model.sourceSetDependencyModel.GradleSourceSetDependencySerialisationService: loaded 2 times (x 73B)
Class org.jetbrains.plugins.gradle.model.GradleSourceSetModel                         : loaded 2 times (x 66B)
Class kotlin.KotlinNothingValueException                                              : loaded 2 times (x 78B)
Class kotlin.jvm.functions.Function2                                                  : loaded 2 times (x 66B)
Class org.gradle.internal.classpath.DefaultClassPath$ImmutableUniqueList$Builder      : loaded 2 times (x 71B)
Class com.google.common.collect.MapMakerInternalMap$WeakValueReference                : loaded 2 times (x 66B)
Class org.jetbrains.plugins.gradle.tooling.util.IntObjectMap$ObjectFactory            : loaded 2 times (x 66B)
Class com.google.common.cache.LocalCache                                              : loaded 2 times (x 183B)
Class com.google.common.cache.CacheBuilder$2                                          : loaded 2 times (x 75B)
Class com.amazon.ion.IonException                                                     : loaded 2 times (x 80B)
Class com.amazon.ion.impl.lite.IonSequenceLite                                        : loaded 2 times (x 428B)
Class com.amazon.ion.impl._Private_Utils$1                                            : loaded 2 times (x 85B)
Class com.amazon.ion.impl.SymbolTableAsStruct                                         : loaded 2 times (x 66B)
Class kotlin.collections.MapsKt___MapsJvmKt                                           : loaded 2 times (x 67B)
Class org.gradle.tooling.internal.adapter.WeakIdentityHashMap$AbsentValueProvider     : loaded 2 times (x 66B)
Class com.google.common.collect.ArrayListMultimap                                     : loaded 2 times (x 168B)
Class com.google.common.cache.CacheBuilder$3                                          : loaded 2 times (x 68B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$UserState$3                      : loaded 2 times (x 81B)
Class com.google.common.util.concurrent.ExecutionError                                : loaded 2 times (x 78B)
Class com.amazon.ion.impl.bin.AbstractIonWriter                                       : loaded 2 times (x 157B)
Class org.jetbrains.plugins.gradle.tooling.util.ObjectCollector$Processor             : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.idea.gradleTooling.model.samWithReceiver.SamWithReceiverModel: loaded 2 times (x 66B)
Class kotlin.jvm.functions.Function4                                                  : loaded 2 times (x 66B)
Class kotlin.collections.EmptySet                                                     : loaded 2 times (x 118B)
Class kotlin.collections.CollectionsKt___CollectionsKt                                : loaded 2 times (x 67B)
Class kotlin.annotation.Target                                                        : loaded 2 times (x 66B)
Class com.google.common.collect.ImmutableEnumSet                                      : loaded 2 times (x 142B)
Class org.gradle.internal.exceptions.NonGradleCauseExceptionsHolder                   : loaded 2 times (x 66B)
Class org.gradle.tooling.model.idea.IdeaModuleDependency                              : loaded 2 times (x 66B)
Class org.gradle.tooling.internal.adapter.ViewBuilder                                 : loaded 2 times (x 66B)
Class com.google.common.base.Suppliers$MemoizingSupplier                              : loaded 2 times (x 75B)
Class com.android.builder.model.v2.ide.AndroidLibraryData                             : loaded 2 times (x 66B)
Class com.android.builder.model.v2.ide.ApiVersion                                     : loaded 2 times (x 66B)
Class com.android.ide.common.gradle.Version                                           : loaded 2 times (x 71B)
Class com.google.common.collect.CollectPreconditions                                  : loaded 2 times (x 67B)
Class com.amazon.ion.system.IonReaderBuilder                                          : loaded 2 times (x 89B)
Class org.jetbrains.plugins.gradle.model.AnnotationProcessingConfig                   : loaded 2 times (x 66B)
Class com.amazon.ion.IonReader                                                        : loaded 2 times (x 66B)
Class kotlin.text.StringsKt___StringsJvmKt                                            : loaded 2 times (x 67B)
Class kotlin.reflect.KDeclarationContainer                                            : loaded 2 times (x 66B)
Class kotlin.annotation.Retention                                                     : loaded 2 times (x 66B)
Class [Lcom.google.common.collect.MapMakerInternalMap$Strength;                       : loaded 2 times (x 65B)
Class [Lcom.google.common.io.FileWriteMode;                                           : loaded 2 times (x 65B)
Class com.android.builder.model.v2.models.Versions$Version                            : loaded 2 times (x 66B)
Class com.google.common.collect.SortedMapDifference                                   : loaded 2 times (x 66B)
Class com.amazon.ion.IonClob                                                          : loaded 2 times (x 66B)
Class org.jetbrains.plugins.gradle.tooling.ModelBuilderService$Parameter              : loaded 2 times (x 66B)
Class kotlin.io.FilesKt                                                               : loaded 2 times (x 67B)
Class kotlin.collections.MapsKt___MapsKt                                              : loaded 2 times (x 67B)
Class kotlin.collections.ArraysKt___ArraysKt                                          : loaded 2 times (x 67B)
Class com.google.common.collect.Iterables$4                                           : loaded 2 times (x 77B)
Class com.google.common.collect.MapMakerInternalMap$StrongKeyDummyValueSegment        : loaded 2 times (x 138B)
Class com.google.common.io.Files                                                      : loaded 2 times (x 67B)
Class com.google.common.cache.LocalCache$WriteThroughEntry                            : loaded 2 times (x 75B)
Class com.google.common.cache.LocalCache$HashIterator                                 : loaded 2 times (x 82B)
Class com.google.common.cache.LocalCache$EntryFactory                                 : loaded 2 times (x 79B)
Class com.amazon.ion.impl.lite.IonLobLite                                             : loaded 2 times (x 180B)
Class com.amazon.ion.impl.lite.IonTextLite                                            : loaded 2 times (x 177B)
Class com.amazon.ion.IonNumber                                                        : loaded 2 times (x 66B)
Class org.gradle.internal.impldep.gnu.trove.TObjectIntProcedure                       : loaded 2 times (x 66B)
Class kotlin.collections.MapsKt__MapWithDefaultKt                                     : loaded 2 times (x 67B)
Class org.gradle.tooling.internal.adapter.ProtocolToModelAdapter$ReflectionMethodInvoker: loaded 2 times (x 72B)
Class [Lcom.google.common.collect.MapMaker$Dummy;                                     : loaded 2 times (x 65B)
Class com.google.common.base.CharMatcher$Whitespace                                   : loaded 2 times (x 108B)
Class [Lcom.android.ide.common.gradle.Separator;                                      : loaded 2 times (x 65B)
Class com.google.common.cache.LocalCache$AbstractReferenceEntry                       : loaded 2 times (x 103B)
Class [Lcom.amazon.ion.impl.bin.IonManagedBinaryWriter$UserState;                     : loaded 2 times (x 65B)
Class com.amazon.ion.impl.bin.utf8.Utf8StringEncoder                                  : loaded 2 times (x 76B)
Class com.amazon.ion.IonBool                                                          : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.idea.gradleTooling.model.assignment.AssignmentModel        : loaded 2 times (x 66B)
Class kotlin.text.StringsKt___StringsKt                                               : loaded 2 times (x 67B)
Class com.google.common.collect.NullsLastOrdering                                     : loaded 2 times (x 111B)
Class kotlin.collections.CollectionsKt__MutableCollectionsJVMKt                       : loaded 2 times (x 67B)
Class com.android.builder.model.v2.models.VariantDependenciesAdjacencyList            : loaded 2 times (x 66B)
Class kotlin.enums.EnumEntriesKt                                                      : loaded 2 times (x 67B)
Class com.google.common.collect.ImmutableMapKeySet                                    : loaded 2 times (x 148B)
Class com.google.common.collect.AbstractMapBasedMultimap$WrappedCollection            : loaded 2 times (x 120B)
Class com.android.builder.model.v2.models.VariantDependencies                         : loaded 2 times (x 66B)
Class org.objectweb.asm.SymbolTable                                                   : loaded 2 times (x 68B)
Class com.google.common.cache.CacheLoader$InvalidCacheLoadException                   : loaded 2 times (x 78B)
Class org.gradle.internal.classloader.ClassLoaderHierarchy                            : loaded 2 times (x 66B)
Class com.amazon.ion.IonLoader                                                        : loaded 2 times (x 66B)
Class org.jetbrains.plugins.gradle.model.GradleProperty                               : loaded 2 times (x 66B)
Class org.gradle.internal.classloader.SystemClassLoaderSpec                           : loaded 2 times (x 67B)
Class com.google.common.collect.FluentIterable                                        : loaded 2 times (x 77B)
Class com.android.builder.model.v2.ide.TestedTargetVariant                            : loaded 2 times (x 66B)
Class com.google.common.cache.LocalCache$StrongEntry                                  : loaded 2 times (x 104B)
Class com.google.common.util.concurrent.AbstractFuture$TrustedFuture                  : loaded 2 times (x 110B)
Class com.google.common.cache.LoadingCache                                            : loaded 2 times (x 66B)
Class com.amazon.ion.impl._Private_Utils                                              : loaded 2 times (x 67B)
Class kotlin.ranges.IntRange$Companion                                                : loaded 2 times (x 67B)
Class kotlin.text.MatcherMatchResult$groups$1                                         : loaded 2 times (x 147B)
Class kotlin.collections.EmptyMap                                                     : loaded 2 times (x 105B)
Class kotlin.reflect.KAnnotatedElement                                                : loaded 2 times (x 66B)
Class kotlin.jvm.internal.markers.KMutableCollection                                  : loaded 2 times (x 66B)
Class kotlin.coroutines.intrinsics.IntrinsicsKt                                       : loaded 2 times (x 67B)
Class org.gradle.internal.exceptions.MultiCauseException                              : loaded 2 times (x 66B)
Class org.gradle.internal.time.CountdownTimer                                         : loaded 2 times (x 66B)
Class kotlin.jvm.internal.Intrinsics                                                  : loaded 2 times (x 67B)
Class com.google.common.io.ByteStreams$LimitedInputStream                             : loaded 2 times (x 88B)
Class com.google.common.base.Suppliers$NonSerializableMemoizingSupplier               : loaded 2 times (x 75B)
Class org.objectweb.asm.MethodWriter                                                  : loaded 2 times (x 102B)
Class [Lcom.google.common.cache.Weigher;                                              : loaded 2 times (x 65B)
Class kotlin.sequences.SequencesKt__SequenceBuilderKt                                 : loaded 2 times (x 67B)
Class com.intellij.gradle.toolingExtension.impl.model.projectModel.GradleExternalProjectSerializationService$ReadContext: loaded 2 times (x 69B)
Class com.intellij.gradle.toolingExtension.impl.model.dependencyModel.DependencyWriteContext: loaded 2 times (x 68B)
Class org.jetbrains.kotlin.idea.gradleTooling.model.parcelize.ParcelizeGradleModel    : loaded 2 times (x 66B)
Class kotlin.sequences.SequencesKt__SequencesKt$flatten$1                             : loaded 2 times (x 74B)
Class kotlin.collections.MapsKt                                                       : loaded 2 times (x 67B)
Class kotlin.collections.CollectionsKt__IterablesKt                                   : loaded 2 times (x 67B)
Class kotlin.coroutines.intrinsics.CoroutineSingletons                                : loaded 2 times (x 75B)
Class org.gradle.tooling.model.ProjectModel                                           : loaded 2 times (x 66B)
Class com.google.common.collect.AbstractMapBasedMultimap$RandomAccessWrappedList      : loaded 2 times (x 201B)
Class com.google.common.collect.ImmutableMultimap$1                                   : loaded 2 times (x 78B)
Class com.android.ide.common.gradle.Version$Companion$ParseState$EMPTY                : loaded 2 times (x 70B)
Class org.jetbrains.plugins.gradle.model.GradleConfiguration                          : loaded 2 times (x 66B)
Class com.amazon.ion.impl.bin.utf8.Utf8StringEncoderPool                              : loaded 2 times (x 70B)
Class com.amazon.ion.impl._Private_IonSystem                                          : loaded 2 times (x 66B)
Class com.amazon.ion.BufferConfiguration$OversizedValueHandler                        : loaded 2 times (x 66B)
Class kotlin.ranges.OpenEndRange                                                      : loaded 2 times (x 66B)
Class kotlin.sequences.SequenceBuilderIterator                                        : loaded 2 times (x 81B)
Class kotlin.KotlinNullPointerException                                               : loaded 2 times (x 79B)
Class com.google.common.collect.AbstractIterator$State                                : loaded 2 times (x 75B)
Class com.google.common.collect.ImmutableMultimap$2                                   : loaded 2 times (x 77B)
Class com.android.ide.common.repository.AgpVersion$Companion$WhenMappings             : loaded 2 times (x 67B)
Class com.google.common.collect.RegularImmutableBiMap                                 : loaded 2 times (x 145B)
Class com.google.common.cache.LocalCache$ValueReference                               : loaded 2 times (x 66B)
Class kotlin.collections.CollectionsKt__ReversedViewsKt                               : loaded 2 times (x 67B)
Class kotlin.jvm.internal.CollectionToArray                                           : loaded 2 times (x 67B)
Class com.amazon.ion.IonDecimal                                                       : loaded 2 times (x 66B)
Class org.jetbrains.plugins.gradle.model.tests.ExternalTestsModel                     : loaded 2 times (x 66B)
Class kotlin.sequences.SequencesKt__SequencesKt$flatten$3                             : loaded 2 times (x 74B)
Class kotlin.ranges.RangesKt___RangesKt                                               : loaded 2 times (x 67B)
Class org.gradle.tooling.model.idea.IdeaLanguageLevel                                 : loaded 2 times (x 66B)
Class org.gradle.tooling.internal.adapter.ProtocolToModelAdapter$SafeMethodInvoker    : loaded 2 times (x 72B)
Class org.gradle.tooling.internal.adapter.WeakIdentityHashMap$WeakKey                 : loaded 2 times (x 75B)
Class org.gradle.internal.exceptions.DefaultMultiCauseException                       : loaded 2 times (x 91B)
Class com.google.common.hash.PrimitiveSink                                            : loaded 2 times (x 66B)
Class com.android.builder.model.v2.ide.ProjectInfo                                    : loaded 2 times (x 66B)
Class com.google.common.base.PatternCompiler                                          : loaded 2 times (x 66B)
Class com.google.common.util.concurrent.AbstractFuture$Failure                        : loaded 2 times (x 68B)
Class com.google.common.base.Equivalence$Equals                                       : loaded 2 times (x 78B)
Class com.google.common.collect.Hashing                                               : loaded 2 times (x 67B)
Class kotlin.io.FilesKt__FileTreeWalkKt                                               : loaded 2 times (x 67B)
Class com.amazon.ion.impl.bin.IonRawBinaryWriter$StreamCloseMode                      : loaded 2 times (x 75B)
Class com.amazon.ion.impl._Private_IonBinaryWriterBuilder$Mutable                     : loaded 2 times (x 111B)
Class com.intellij.gradle.toolingExtension.impl.model.projectModel.GradleExternalProjectSerializationService: loaded 2 times (x 73B)
Class kotlin.text.CharsKt__CharKt                                                     : loaded 2 times (x 67B)
Class kotlin.collections.ArraysKt                                                     : loaded 2 times (x 67B)
Class kotlin.collections.ArrayDeque$Companion                                         : loaded 2 times (x 67B)
Class org.gradle.tooling.model.HierarchicalElement                                    : loaded 2 times (x 66B)
Class kotlin.sequences.FilteringSequence$iterator$1                                   : loaded 2 times (x 75B)
Class com.google.common.io.CharSink                                                   : loaded 2 times (x 75B)
Class com.android.builder.model.v2.dsl.DependenciesInfo                               : loaded 2 times (x 66B)
Class com.intellij.gradle.toolingExtension.impl.model.sourceSetModel.GradleSourceSetSerialisationService$Companion: loaded 2 times (x 67B)
Class org.objectweb.asm.Label                                                         : loaded 2 times (x 69B)
Class org.objectweb.asm.CurrentFrame                                                  : loaded 2 times (x 69B)
Class org.objectweb.asm.ClassTooLargeException                                        : loaded 2 times (x 79B)
Class com.amazon.ion.impl._Private_IonValue                                           : loaded 2 times (x 66B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$UserState$2                      : loaded 2 times (x 81B)
Class build_7nexczbjvru4fomegnac7tv1y                                                 : loaded 2 times (x 176B)
Class com.amazon.ion.impl.bin.IonRawBinaryWriter$PatchPoint                           : loaded 2 times (x 69B)
Class com.amazon.ion.IonBlob                                                          : loaded 2 times (x 66B)
Class com.amazon.ion.IonLob                                                           : loaded 2 times (x 66B)
Class com.amazon.ion.IonSequence                                                      : loaded 2 times (x 66B)
Class org.jetbrains.plugins.gradle.tooling.serialization.ExternalTestsSerializationService: loaded 2 times (x 73B)
Class com.google.common.collect.ByFunctionOrdering                                    : loaded 2 times (x 111B)
Class kotlin.collections.SetsKt__SetsKt                                               : loaded 2 times (x 67B)
Class kotlin.coroutines.EmptyCoroutineContext                                         : loaded 2 times (x 73B)
Class org.gradle.internal.time.Time                                                   : loaded 2 times (x 67B)
Class com.google.common.collect.MapMakerInternalMap$InternalEntryHelper               : loaded 2 times (x 66B)
Class [Lcom.google.common.base.AbstractIterator$State;                                : loaded 2 times (x 65B)
Class com.google.common.collect.Table                                                 : loaded 2 times (x 66B)
Class com.android.ide.common.repository.AgpVersion$PreviewKind                        : loaded 2 times (x 75B)
Class com.android.builder.model.v2.models.ProjectGraph                                : loaded 2 times (x 66B)
Class com.google.common.collect.Maps$IteratorBasedAbstractMap                         : loaded 2 times (x 121B)
Class com.android.ide.common.gradle.Version$Companion$ParseState$NUMERIC              : loaded 2 times (x 70B)
Class com.google.common.io.FileWriteMode                                              : loaded 2 times (x 75B)
Class org.objectweb.asm.Frame                                                         : loaded 2 times (x 69B)
Class org.objectweb.asm.MethodVisitor                                                 : loaded 2 times (x 101B)
Class com.google.common.cache.LocalCache$EntryFactory$1                               : loaded 2 times (x 79B)
Class [Lcom.google.common.cache.RemovalListener;                                      : loaded 2 times (x 65B)
Class [Lcom.google.common.collect.MapMakerInternalMap$Segment;                        : loaded 2 times (x 65B)
Class com.google.common.cache.LocalCache$ValueIterator                                : loaded 2 times (x 82B)
Class com.amazon.ion.system.IonTextWriterBuilder                                      : loaded 2 times (x 94B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$ImportedSymbolResolverMode$1$1$1 : loaded 2 times (x 72B)
Class com.amazon.ion.impl._Private_LocalSymbolTableFactory                            : loaded 2 times (x 66B)
Class org.jetbrains.plugins.gradle.tooling.serialization.AnnotationProcessingModelSerializationService: loaded 2 times (x 73B)
Class kotlin.collections.ArraysKt__ArraysJVMKt                                        : loaded 2 times (x 67B)
Class org.gradle.internal.classloader.FilteringClassLoader                            : loaded 2 times (x 101B)
Class com.google.common.cache.LocalCache$EntryFactory$2                               : loaded 2 times (x 79B)
Class com.amazon.ion.impl.lite.IonNullLite                                            : loaded 2 times (x 171B)
Class [Lcom.intellij.openapi.externalSystem.model.project.dependencies.ResolutionState;: loaded 2 times (x 65B)
Class org.gradle.internal.impldep.gnu.trove.TObjectHash                               : loaded 2 times (x 90B)
Class org.jetbrains.plugins.gradle.model.DependencyAccessorsModel                     : loaded 2 times (x 66B)
Class kotlin.text.StringsKt__StringBuilderKt                                          : loaded 2 times (x 67B)
Class kotlin.jvm.internal.CallableReference$NoReceiver                                : loaded 2 times (x 67B)
Class org.gradle.api.internal.classpath.GlobalCacheRootsProvider                      : loaded 2 times (x 66B)
Class com.google.common.collect.CollectCollectors                                     : loaded 2 times (x 67B)
Class org.objectweb.asm.ModuleWriter                                                  : loaded 2 times (x 78B)
Class com.google.common.cache.LocalCache$EntryFactory$3                               : loaded 2 times (x 79B)
Class com.google.common.base.Ticker$1                                                 : loaded 2 times (x 68B)
Class com.google.common.collect.Maps$BiMapConverter                                   : loaded 2 times (x 86B)
Class kotlin.NotImplementedError                                                      : loaded 2 times (x 78B)
Class com.amazon.ion.impl.lite.IonClobLite                                            : loaded 2 times (x 216B)
Class com.amazon.ion.impl._Private_ValueFactory                                       : loaded 2 times (x 66B)
Class com.amazon.ion.impl.bin.IonRawBinaryWriter$PreallocationMode$1                  : loaded 2 times (x 78B)
Class [Lcom.amazon.ion.impl.bin._Private_IonManagedBinaryWriterBuilder$AllocatorMode; : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.idea.gradleTooling.KotlinMPPGradleModel                    : loaded 2 times (x 66B)
Class com.intellij.gradle.toolingExtension.impl.model.dependencyDownloadPolicyModel.GradleDependencyDownloadPolicy: loaded 2 times (x 66B)
Class kotlin.NoWhenBranchMatchedException                                             : loaded 2 times (x 78B)
Class org.gradle.tooling.model.BuildIdentifier                                        : loaded 2 times (x 66B)
Class kotlin.sequences.FlatteningSequence                                             : loaded 2 times (x 71B)
Class com.android.builder.model.v2.ide.VectorDrawablesOptions                         : loaded 2 times (x 66B)
Class com.google.common.cache.LocalCache$EntryFactory$4                               : loaded 2 times (x 79B)
Class kotlin.text.MatcherMatchResult                                                  : loaded 2 times (x 76B)
Class kotlin.text.MatchGroupCollection                                                : loaded 2 times (x 66B)
Class settings_dwbk24pvkw3orvdwxwwezca2l                                              : loaded 2 times (x 175B)
Class com.amazon.ion.IonInt                                                           : loaded 2 times (x 66B)
Class com.amazon.ion.impl.bin.IonRawBinaryWriter$PreallocationMode$2                  : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.idea.gradleTooling.PrepareKotlinIdeImportTaskModel         : loaded 2 times (x 66B)
Class com.google.common.io.ByteStreams$1                                              : loaded 2 times (x 81B)
Class com.android.builder.model.v2.ide.Edge                                           : loaded 2 times (x 66B)
Class com.android.builder.model.v2.models.VariantDependenciesFlatList                 : loaded 2 times (x 66B)
Class com.google.common.cache.LocalCache$EntryFactory$5                               : loaded 2 times (x 79B)
Class com.amazon.ion.impl.bin.IonRawBinaryWriter$PreallocationMode$3                  : loaded 2 times (x 78B)
Class com.android.ide.gradle.model.GradlePluginModel                                  : loaded 2 times (x 66B)
Class com.google.common.collect.ReverseOrdering                                       : loaded 2 times (x 111B)
Class org.gradle.tooling.model.build.JavaEnvironment                                  : loaded 2 times (x 66B)
Class com.android.builder.model.v2.ide.SourceSetContainer                             : loaded 2 times (x 66B)
Class com.google.common.cache.LocalCache$EntryFactory$6                               : loaded 2 times (x 79B)
Class org.jetbrains.plugins.gradle.model.GradleExtension                              : loaded 2 times (x 66B)
Class com.amazon.ion.impl.lite.IonDecimalLite                                         : loaded 2 times (x 184B)
Class com.amazon.ion.IonBufferConfiguration$Builder                                   : loaded 2 times (x 74B)
Class org.gradle.internal.time.Timer                                                  : loaded 2 times (x 66B)
Class kotlin.jvm.internal.markers.KMappedMarker                                       : loaded 2 times (x 66B)
Class org.gradle.internal.installation.CurrentGradleInstallationLocator               : loaded 2 times (x 67B)
Class org.objectweb.asm.ClassReader                                                   : loaded 2 times (x 90B)
Class com.google.common.collect.ImmutableMultimap$Builder                             : loaded 2 times (x 82B)
Class com.android.builder.model.v2.ide.LintOptions                                    : loaded 2 times (x 66B)
Class com.google.common.collect.Maps$KeySet                                           : loaded 2 times (x 133B)
Class com.google.common.cache.LocalCache$EntryFactory$7                               : loaded 2 times (x 79B)
Class com.google.common.collect.ImmutableSet$EmptySetBuilderImpl                      : loaded 2 times (x 72B)
Class kotlin.text.StringsKt__StringsKt                                                : loaded 2 times (x 67B)
Class [Lcom.amazon.ion.impl.bin.IonRawBinaryWriter$ContainerType;                     : loaded 2 times (x 65B)
Class org.gradle.tooling.internal.adapter.ProtocolToModelAdapter$ViewGraphDetails     : loaded 2 times (x 68B)
Class org.gradle.tooling.model.idea.IdeaProject                                       : loaded 2 times (x 66B)
Class org.gradle.tooling.model.DomainObjectSet                                        : loaded 2 times (x 66B)
Class org.gradle.internal.service.DefaultServiceLocator                               : loaded 2 times (x 79B)
Class com.google.common.cache.LocalCache$SoftValueReference                           : loaded 2 times (x 93B)
Class com.google.common.collect.Maps$EntrySet                                         : loaded 2 times (x 132B)
Class com.google.common.collect.ImmutableMultimap$EntryCollection                     : loaded 2 times (x 124B)
Class com.android.builder.model.v2.ide.BasicArtifact                                  : loaded 2 times (x 66B)
Class com.google.common.cache.LocalCache$EntryFactory$8                               : loaded 2 times (x 79B)
Class org.jetbrains.plugins.gradle.tooling.serialization.internal.IdeaProjectSerializationService$ReadContext: loaded 2 times (x 68B)
Class com.amazon.ion.impl.bin.utf8.Utf8StringEncoder$Result                           : loaded 2 times (x 70B)
Class com.amazon.ion.impl.bin.utf8.Pool$Allocator                                     : loaded 2 times (x 66B)
Class com.amazon.ion.IonText                                                          : loaded 2 times (x 66B)
Class com.amazon.ion.impl.SymbolTokenImpl                                             : loaded 2 times (x 77B)
Class com.amazon.ion.SubstituteSymbolTableException                                   : loaded 2 times (x 80B)
Class com.google.common.collect.ComparisonChain$InactiveComparisonChain               : loaded 2 times (x 76B)
Class kotlin.coroutines.jvm.internal.SuspendFunction                                  : loaded 2 times (x 66B)
Class org.gradle.util.GradleVersion                                                   : loaded 2 times (x 79B)
Class org.gradle.internal.installation.CurrentGradleInstallation                      : loaded 2 times (x 69B)
Class org.objectweb.asm.Opcodes                                                       : loaded 2 times (x 66B)
Class com.google.common.cache.CacheLoader$1                                           : loaded 2 times (x 71B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$UserState$1                      : loaded 2 times (x 81B)
Class com.google.common.base.Equivalence$Identity                                     : loaded 2 times (x 78B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$ImportDescriptor                 : loaded 2 times (x 72B)
Class com.amazon.ion.SymbolTable                                                      : loaded 2 times (x 66B)
Class org.jetbrains.plugins.gradle.tooling.serialization.GradleExtensionsSerializationService$WriteContext: loaded 2 times (x 68B)
Class com.intellij.gradle.toolingExtension.impl.model.taskModel.GradleTaskSerialisationService$Companion: loaded 2 times (x 67B)
Class org.jetbrains.plugins.gradle.javaModel.JavaGradleManifestModel                  : loaded 2 times (x 66B)
Class kotlin.jvm.internal.StringCompanionObject                                       : loaded 2 times (x 67B)
Class kotlin.coroutines.intrinsics.IntrinsicsKt__IntrinsicsJvmKt                      : loaded 2 times (x 67B)
Class kotlin.coroutines.jvm.internal.CoroutineStackFrame                              : loaded 2 times (x 66B)
Class org.gradle.tooling.internal.adapter.ProtocolToModelAdapter$MethodInvocationCache$MethodInvocationKey: loaded 2 times (x 69B)
Class org.gradle.util.internal.GUtil                                                  : loaded 2 times (x 67B)
Class com.google.common.io.ByteSource                                                 : loaded 2 times (x 80B)
Class com.android.utils.StringHelper                                                  : loaded 2 times (x 67B)
Class com.google.common.cache.AbstractCache$StatsCounter                              : loaded 2 times (x 66B)
Class [Lcom.amazon.ion.impl.bin.IonManagedBinaryWriter$SymbolState;                   : loaded 2 times (x 65B)
Class com.amazon.ion.impl._Private_IonWriterBase                                      : loaded 2 times (x 158B)
Class com.amazon.ion.system.IonSystemBuilder                                          : loaded 2 times (x 71B)
Class com.amazon.ion.IonCatalog                                                       : loaded 2 times (x 66B)
Class com.intellij.gradle.toolingExtension.impl.model.sourceSetModel.GradleSourceSetSerialisationService$SourceSetModelReadContext: loaded 2 times (x 68B)
Class com.intellij.gradle.toolingExtension.impl.model.utilTurnOffDefaultTasksModel.TurnOffDefaultTasks: loaded 2 times (x 66B)
Class kotlin.io.NoSuchFileException                                                   : loaded 2 times (x 78B)
Class kotlin.collections.CollectionsKt                                                : loaded 2 times (x 67B)
Class org.objectweb.asm.Context                                                       : loaded 2 times (x 68B)
Class com.google.common.collect.MapMakerInternalMap$1                                 : loaded 2 times (x 79B)
Class com.android.builder.model.v2.dsl.BuildType                                      : loaded 2 times (x 66B)
Class com.google.common.base.PairwiseEquivalence                                      : loaded 2 times (x 79B)
Class org.jetbrains.plugins.gradle.tooling.serialization.internal.IdeaProjectSerializationService$WriteContext$5: loaded 2 times (x 79B)
Class com.intellij.gradle.toolingExtension.impl.model.sourceSetModel.GradleSourceSetSerialisationService$SourceSetModelWriteContext: loaded 2 times (x 68B)
Class kotlin.coroutines.CoroutineContext                                              : loaded 2 times (x 66B)
Class kotlin.coroutines.jvm.internal.DebugProbesKt                                    : loaded 2 times (x 67B)
Class org.gradle.tooling.internal.adapter.WeakIdentityHashMap                         : loaded 2 times (x 72B)
Class org.gradle.tooling.GradleConnectionException                                    : loaded 2 times (x 79B)
Class org.gradle.tooling.internal.adapter.ProtocolToModelAdapter$1                    : loaded 2 times (x 71B)
Class org.gradle.tooling.internal.adapter.ProtocolToModelAdapter$ViewDecoration       : loaded 2 times (x 66B)
Class org.gradle.internal.installation.GradleInstallation                             : loaded 2 times (x 71B)
Class com.android.builder.model.v2.ide.ViewBindingOptions                             : loaded 2 times (x 66B)
Class com.google.common.base.Splitter$Strategy                                        : loaded 2 times (x 66B)
Class com.amazon.ion.impl._Private_RecyclingQueue$Recycler                            : loaded 2 times (x 66B)
Class com.amazon.ion.impl.lite.ContainerlessContext                                   : loaded 2 times (x 76B)
Class org.jetbrains.plugins.gradle.tooling.serialization.GradleExtensionsSerializationService$ReadContext: loaded 2 times (x 68B)
Class com.google.common.collect.ForwardingCollection                                  : loaded 2 times (x 126B)
Class com.google.common.collect.LexicographicalOrdering                               : loaded 2 times (x 111B)
Class org.gradle.tooling.internal.adapter.MethodInvoker                               : loaded 2 times (x 66B)
Class build_d5mdhodvhdi9ygnsd0pgs2lzl                                                 : loaded 2 times (x 176B)
Class com.android.ide.common.gradle.Part                                              : loaded 2 times (x 73B)
Class com.google.common.collect.Lists$RandomAccessReverseList                         : loaded 2 times (x 196B)
Class com.amazon.ion.UnexpectedEofException                                           : loaded 2 times (x 80B)
Class com.amazon.ion.IonSexp                                                          : loaded 2 times (x 66B)
Class com.amazon.ion.ReadOnlyValueException                                           : loaded 2 times (x 80B)
Class com.amazon.ion.impl._Private_LocalSymbolTable                                   : loaded 2 times (x 66B)
Class com.amazon.ion.BufferConfiguration$DataHandler                                  : loaded 2 times (x 66B)
Class com.amazon.ion.impl._Private_IonBinaryWriterBuilder                             : loaded 2 times (x 111B)
Class org.jetbrains.plugins.gradle.tooling.serialization.RepositoriesModelSerializationService$WriteContext: loaded 2 times (x 68B)
Class com.android.ide.gradle.model.LegacyAndroidGradlePluginProperties                : loaded 2 times (x 66B)
Class kotlin.ranges.IntProgression                                                    : loaded 2 times (x 77B)
Class kotlin.jvm.KotlinReflectionNotSupportedError                                    : loaded 2 times (x 78B)
Class org.gradle.tooling.model.gradle.GradleScript                                    : loaded 2 times (x 66B)

---------------  S Y S T E M  ---------------

OS:
 Windows 10 , 64 bit Build 19041 (10.0.19041.5915)
OS uptime: 0 days 8:58 hours

CPU: total 8 (initial active 8) (8 cores per cpu, 2 threads per core) family 23 model 24 stepping 1 microcode 0x0, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4a, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, clmul, bmi1, bmi2, adx, sha, fma, vzeroupper, clflush, clflushopt, rdtscp, f16c
Processor Information for the first 8 processors :
  Max Mhz: 3700, Current Mhz: 3700, Mhz Limit: 3700

Memory: 4k page, system-wide physical 22476M (3954M free)
TotalPageFile size 22476M (AvailPageFile size 221M)
current process WorkingSet (physical memory assigned to process): 1137M, peak: 1155M
current process commit charge ("private bytes"): 1181M, peak: 1537M

vm_info: OpenJDK 64-Bit Server VM (21.0.6+-13368085-b895.109) for windows-amd64 JRE (21.0.6+-13368085-b895.109), built on 2025-04-16T17:01:31Z by "builder" with MS VC++ 16.10 / 16.11 (VS2019)

END.
