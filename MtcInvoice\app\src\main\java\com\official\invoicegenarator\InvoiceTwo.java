package com.official.invoicegenarator;

import android.Manifest;
import android.content.ClipData;
import android.content.ClipboardManager;
import android.content.Intent;
import android.content.SharedPreferences;
import android.content.pm.PackageManager;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.pdf.PdfDocument;
import android.media.Image;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Environment;
import android.os.StrictMode;
import android.provider.Settings;
import android.text.Editable;
import android.text.TextWatcher;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuItem;
import android.view.MotionEvent;
import android.view.ScaleGestureDetector;
import android.view.View;
import android.webkit.WebChromeClient;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.ScrollView;
import android.widget.TextView;
import android.widget.Toast;
import android.widget.ZoomControls;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.AppCompatButton;
import androidx.appcompat.widget.AppCompatEditText;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.documentfile.provider.DocumentFile;

import com.bumptech.glide.Glide;
import com.google.android.material.appbar.MaterialToolbar;
import com.google.android.material.dialog.MaterialAlertDialogBuilder;

import java.io.IOException;
import java.io.OutputStream;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

public class InvoiceTwo extends AppCompatActivity {
    private static final int REQUEST_PERMISSION_CODE = 1100;
    private static final int REQUEST_PERMISSION_CODE_ANDROID_R = 2296;
    private static final int PICK_SIGNATURE_IMAGE = 100;
    private static final String SHARED_PREF_NAME = "SignaturePrefs";
    private static final String SIGNATURE_URI_KEY = "signature_uri";
    private static final int REQUEST_PDF_SELECT = 2;
    String fName = String.valueOf(System.currentTimeMillis());
    private LinearLayout tableContainer,banklay;
    private LinearLayout vat_layout;
    private boolean isFirstTable = true;
    private boolean isVatTable = true;
    CustomButtonEffect customButtonEffect;

    private ImageView btnAddTable,btnRemoveTable,add_vat_column,remove_vat_column,add_back_layout,remove_back_layout;
    private int serialNumber = 1; // Initialize serial number to 1
    private EditText date;
    EditText subjectEditText,edit_text;

    // Initialize variables for zoom and pan
    private float scaleFactor = 1.0f;
    private ScaleGestureDetector scaleGestureDetector;
    private float translationX = 0f, translationY = 0f;
    private float previousX = 0f, previousY = 0f;
    private boolean isScaling = false; // To differentiate between zoom and pan
    private LinearLayout sllayout;
    ImageView resetZoomBtn;
    ImageView downloadlist;

    private LinearLayout signatureLayout;
    private ImageView signatureImage;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_invoice_two);
        // Initialize API service for file operations using singleton pattern


        if (!checkPermission()) {
            requestPermission();
        }

        StrictMode.VmPolicy.Builder builder = new StrictMode.VmPolicy.Builder();
        StrictMode.setVmPolicy(builder.build());
        MaterialToolbar toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);
        downloadlist=findViewById(R.id.downloads);
        tableContainer = findViewById(R.id.table_container);
        vat_layout = findViewById(R.id.vat_layout);
        banklay = findViewById(R.id.banklay);
        btnAddTable=findViewById(R.id.add_column);
        btnRemoveTable=findViewById(R.id.remove_column);
        add_vat_column=findViewById(R.id.add_vat_column);
        remove_vat_column=findViewById(R.id.remove_vat_column);
        add_back_layout=findViewById(R.id.add_back_layout);
        remove_back_layout=findViewById(R.id.remove_back_layout);
        edit_text=findViewById(R.id.edit_text);
        date=findViewById(R.id.date);
        subjectEditText = findViewById(R.id.subject);
        customButtonEffect = new CustomButtonEffect(btnAddTable);
        customButtonEffect = new CustomButtonEffect(btnRemoveTable);
        customButtonEffect = new CustomButtonEffect(add_vat_column);
        customButtonEffect = new CustomButtonEffect(remove_vat_column);
        customButtonEffect = new CustomButtonEffect(add_back_layout);
        customButtonEffect = new CustomButtonEffect(remove_back_layout);
        resetZoomBtn = findViewById(R.id.resetZoomBtn);
// Get the current date
        SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy", Locale.getDefault());
        String currentDate = sdf.format(new Date());

// Set the current date to the EditText
        date.setText("Date: "+currentDate);
        findViewById(R.id.downloadBtn).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                EditText subjectEditText = findViewById(R.id.subject);  // Get the EditText reference
                String subjectText = subjectEditText.getText().toString().trim();

                // Check if the EditText is empty or still has the placeholder text
                if (subjectText.isEmpty() || subjectText.equals("SUBJECT:")) {
                    // Show a dialog if the input is empty
                    showAlertDialog();
                } else {
                    // Call layoutToPdfConverter if input is valid
                    layoutToPdfConverter();
                }
            }
        });

        downloadlist.setOnClickListener(v -> {
            Intent intent = new Intent(InvoiceTwo.this, DownloadListActivityFixed.class);
            startActivity(intent);
        });

        sllayout = findViewById(R.id.sllayout);
        ZoomControls zoomControls = findViewById(R.id.zoomControls);

        // Initialize ScaleGestureDetector for pinch-to-zoom
        scaleGestureDetector = new ScaleGestureDetector(this, new ScaleListener());

        // Set up zoom in and zoom out buttons
        zoomControls.setOnZoomInClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                scaleFactor += 0.1f; // Increase the scale factor
                sllayout.setScaleX(scaleFactor);
                sllayout.setScaleY(scaleFactor);
            }
        });

        zoomControls.setOnZoomOutClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                scaleFactor -= 0.1f; // Decrease the scale factor
                scaleFactor = Math.max(0.1f, scaleFactor); // Limit zoom out
                sllayout.setScaleX(scaleFactor);
                sllayout.setScaleY(scaleFactor);
            }
        });

        resetZoomBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                // Reset zoom
                scaleFactor = 1.0f;
                sllayout.setScaleX(scaleFactor);
                sllayout.setScaleY(scaleFactor);

                // Reset pan (translation)
                translationX = 0f;
                translationY = 0f;
                sllayout.setTranslationX(translationX);
                sllayout.setTranslationY(translationY);
            }
        });

        // Set a touch listener on the layout to detect pinch gestures and pan/slide
        sllayout.setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                scaleGestureDetector.onTouchEvent(event);  // Detect the pinch-to-zoom gesture

                // If the user is not scaling, allow panning (moving the view)
                if (!isScaling) {
                    switch (event.getAction()) {
                        case MotionEvent.ACTION_DOWN:
                            previousX = event.getX() - translationX;
                            previousY = event.getY() - translationY;
                            break;

                        case MotionEvent.ACTION_MOVE:
                            translationX = event.getX() - previousX;
                            translationY = event.getY() - previousY;

                            // Apply translation to the ScrollView
                            sllayout.setTranslationX(translationX);
                            sllayout.setTranslationY(translationY);
                            break;

                        case MotionEvent.ACTION_UP:
                            break;
                    }
                }
                return true;
            }
        });



        findViewById(R.id.uploadBtn).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                Intent intent = new Intent(Intent.ACTION_OPEN_DOCUMENT);
                intent.setType("application/pdf");
                startActivityForResult(intent, REQUEST_PDF_SELECT);
            }
        });
        btnAddTable.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                addTableLayout();
            }
        });

        btnRemoveTable.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                removeTableLayout();
            }
        });

        add_vat_column.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                addvatTableLayout();
            }
        });

        remove_vat_column.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                removevatTableLayout();
            }
        });
        add_back_layout.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                banklay.setVisibility(View.VISIBLE);
            }
        });

        remove_back_layout.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                banklay.setVisibility(View.GONE);
            }
        });


        signatureLayout = findViewById(R.id.signaturelay);
        signatureImage = findViewById(R.id.signature_image);

        // Load saved signature image if exists
        loadSavedSignature();

        findViewById(R.id.add_signature).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                findViewById(R.id.signaturelay).setVisibility(View.VISIBLE);
                Intent intent = new Intent(Intent.ACTION_PICK);
                intent.setType("image/*");
                startActivityForResult(intent, PICK_SIGNATURE_IMAGE);
            }
        });

        findViewById(R.id.add_signature).setOnLongClickListener(new View.OnLongClickListener() {
            @Override
            public boolean onLongClick(View v) {
                findViewById(R.id.signaturelay).setVisibility(View.GONE);
                return true;
            }
        });


        // Inflate the first table layout by default
        addTableLayout();
    }
    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        // Inflate the menu; this adds items to the action bar if it is present.
        getMenuInflater().inflate(R.menu.text_to_number, menu); // Replace with your menu resource
        return true;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        // Handle action bar item clicks here
        int id = item.getItemId();

        if (id == R.id.action_open_dialog) {
            showNumberToWordsDialog();
            return true;
        } else if (id == R.id.invoice_traker) {
            openInvoiceTracker(); // Define this method to handle invoice tracking actions
            return true;
        }

        return super.onOptionsItemSelected(item);
    }
    private void openInvoiceTracker() {
        DataUploadDialogFragment dialogFragment = new DataUploadDialogFragment();
        dialogFragment.show(getSupportFragmentManager(), "DataUploadDialog");
    }

   /* private void openInvoiceTracker() {
        new MaterialAlertDialogBuilder(this)
                .setTitle("Invoice Tracker")
                .setMessage("Choose an option for the Invoice Tracker:")
                .setPositiveButton("Upload Data", (dialog, which) -> {
                    // Open the DataUploadDialogFragment
                    DataUploadDialogFragment dialogFragment = new DataUploadDialogFragment();
                    dialogFragment.show(getSupportFragmentManager(), "DataUploadDialog");
                })
                .setNegativeButton("Cancel", (dialog, which) -> dialog.dismiss())
                .show();
    }*/
   @Override
   public void onBackPressed() {
       // Inflate the custom layout
       LayoutInflater inflater = getLayoutInflater();
       View dialogView = inflater.inflate(R.layout.dialog_exit_confirmation, null);

       // Reference the checkbox, title, and buttons from the layout
       CheckBox checkboxConfirm = dialogView.findViewById(R.id.checkbox_confirm);
       TextView exitTitle = dialogView.findViewById(R.id.exit_title);
       Button buttonCancel = dialogView.findViewById(R.id.button_cancel);
       Button buttonYes = dialogView.findViewById(R.id.button_yes);
       exitTitle.setText("Are you sure you want to close Second Invoice?");

       // Create the dialog
       AlertDialog dialog = new AlertDialog.Builder(this)
               .setView(dialogView)
               .create();

       // Set dialog background to transparent
       dialog.getWindow().setBackgroundDrawableResource(android.R.color.transparent);

       // Initially disable the "Yes" button
       buttonYes.setEnabled(false);

       // Enable "Yes" button only when the checkbox is checked
       checkboxConfirm.setOnCheckedChangeListener((buttonView, isChecked) -> {
           buttonYes.setEnabled(isChecked);
       });

       // Set "Cancel" button to dismiss the dialog
       buttonCancel.setOnClickListener(v -> dialog.dismiss());

       // Set "Yes" button to exit the activity
       buttonYes.setOnClickListener(v -> {
           dialog.dismiss();
           super.onBackPressed();
       });

       dialog.show();
   }

    private void showNumberToWordsDialog() {
        // Inflate the custom dialog layout
        LayoutInflater inflater = getLayoutInflater();
        View dialogView = inflater.inflate(R.layout.dialog_number_to_words, null);

        final EditText editTextNumber = dialogView.findViewById(R.id.editTextNumber);
        final TextView textViewWords = dialogView.findViewById(R.id.textViewWords);
        final Button buttonCopy = dialogView.findViewById(R.id.buttonCopy);

        editTextNumber.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {}

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                String input = s.toString();

                if (!input.isEmpty()) {
                    try {
                        long number = Long.parseLong(input);
                        String numberInWords = numToWord(String.valueOf(number));
                        textViewWords.setText("Total amount RO :-" + number + "/-"+" (Rial Omani " + numberInWords + " rial-Only)");
                        edit_text.setText(textViewWords.getText().toString());
                    } catch (NumberFormatException e) {
                        textViewWords.setText("Invalid number");
                    }
                } else {
                    textViewWords.setText("");
                }
            }

            @Override
            public void afterTextChanged(Editable s) {}
        });

        buttonCopy.setOnClickListener(v -> copyToClipboard(textViewWords.getText().toString()));

        // Create the Material AlertDialog
        MaterialAlertDialogBuilder builder = new MaterialAlertDialogBuilder(this);
        builder.setView(dialogView)
                .setNegativeButton("Cancel", (dialog, which) -> dialog.dismiss())
                .show();
    }

    private void copyToClipboard(String text) {

        ClipboardManager clipboard = (ClipboardManager) getSystemService(CLIPBOARD_SERVICE);

        ClipData clip = ClipData.newPlainText("Copied Text", text);

        clipboard.setPrimaryClip(clip);

    }


    private String numToWord(String inputNumber) {
        // Remove any leading zeros
        inputNumber = inputNumber.replaceFirst("^0+(?!$)", "");

        // Check for empty input or invalid characters
        if (inputNumber.isEmpty() || !inputNumber.matches("\\d+")) {
            return "Invalid input. Please enter a valid number.";
        }

        if (inputNumber.equals("0")) {
            return "Zero";
        }

        String[] once = {"", "One", "Two", "Three", "Four", "Five", "Six", "Seven", "Eight", "Nine"};
        String[] twos = {"Ten", "Eleven", "Twelve", "Thirteen", "Fourteen", "Fifteen", "Sixteen", "Seventeen", "Eighteen", "Nineteen"};
        String[] tens = {"", "", "Twenty", "Thirty", "Forty", "Fifty", "Sixty", "Seventy", "Eighty", "Ninety"};
        String[] thousands = {"", "Thousand", "Million", "Billion", "Trillion", "Quadrillion", "Quintillion", "Sextillion", "Septillion", "Octillion", "Nonillion", "Decillion"};

        StringBuilder words = new StringBuilder();
        int thousandIndex = 0;

        // Process the input number in reverse, 3 digits at a time
        for (int i = inputNumber.length(); i > 0; i -= 3) {
            int start = Math.max(i - 3, 0);
            String chunk = inputNumber.substring(start, i);

            // Parse the chunk
            String chunkWords = convertChunkToWords(chunk);

            if (!chunkWords.isEmpty()) {
                // Add chunk words and thousands
                words.insert(0, chunkWords + " " + thousands[thousandIndex] + " ");
            }

            thousandIndex++; // Increment the thousand index
        }

        return words.toString().trim();
    }

    // Helper method to convert a chunk (up to 3 digits) to words
    private String convertChunkToWords(String chunk) {
        String[] once = {"", "One", "Two", "Three", "Four", "Five", "Six", "Seven", "Eight", "Nine"};
        String[] twos = {"Ten", "Eleven", "Twelve", "Thirteen", "Fourteen", "Fifteen", "Sixteen", "Seventeen", "Eighteen", "Nineteen"};
        String[] tens = {"", "", "Twenty", "Thirty", "Forty", "Fifty", "Sixty", "Seventy", "Eighty", "Ninety"};

        StringBuilder chunkWords = new StringBuilder();
        int chunkNumber = Integer.parseInt(chunk); // Safe to parse since the chunk is at most 3 digits

        if (chunkNumber >= 100) {
            chunkWords.append(once[chunkNumber / 100]).append(" Hundred ");
            chunkNumber %= 100;
        }

        if (chunkNumber >= 20) {
            chunkWords.append(tens[chunkNumber / 10]).append(" ");
            chunkNumber %= 10;
        } else if (chunkNumber >= 10) {
            chunkWords.append(twos[chunkNumber - 10]).append(" ");
            chunkNumber = 0; // Reset chunk since it's processed
        }

        if (chunkNumber > 0) {
            chunkWords.append(once[chunkNumber]).append(" ");
        }

        return chunkWords.toString().trim();
    }






    // Custom ScaleListener class for handling pinch-to-zoom
    private class ScaleListener extends ScaleGestureDetector.SimpleOnScaleGestureListener {
        @Override
        public boolean onScale(ScaleGestureDetector detector) {
            isScaling = true;  // Indicate that scaling is happening

            scaleFactor *= detector.getScaleFactor();
            scaleFactor = Math.max(0.1f, Math.min(scaleFactor, 5.0f)); // Limit scale factor

            // Apply the scaling to the ScrollView
            sllayout.setScaleX(scaleFactor);
            sllayout.setScaleY(scaleFactor);
            return true;
        }

        @Override
        public void onScaleEnd(ScaleGestureDetector detector) {
            super.onScaleEnd(detector);
            isScaling = false;  // Reset the scaling state after pinch is done
        }
    }
    private void showAlertDialog() {
        new MaterialAlertDialogBuilder(InvoiceTwo.this)
                .setTitle("Empty Subject!")
                .setMessage("Please your subject before downloading.")
                .setPositiveButton("OK", null)
                .show();
    }
    private void addTableLayout() {
        LayoutInflater inflater = LayoutInflater.from(this);
        View tableLayout = inflater.inflate(R.layout.item_two, null);
        tableContainer.addView(tableLayout, tableContainer.getChildCount()); // Add new table layout at the end
        isFirstTable = false; // Set flag to false after adding the first table layout
        // Set the serial number for the new table layout
        EditText serialEditText = tableLayout.findViewById(R.id.serial);
        serialEditText.setText(String.valueOf(serialNumber));
        serialNumber++; // Increment serial number for next table layout
    }

    private void removeTableLayout() {
        if (!isFirstTable && tableContainer.getChildCount() > 1) {
            View lastTableLayout = tableContainer.getChildAt(tableContainer.getChildCount() - 1);
            EditText serialEditText = lastTableLayout.findViewById(R.id.serial);
            int currentSerialNumber = Integer.parseInt(serialEditText.getText().toString());
            serialNumber = currentSerialNumber; // Update serial number
            tableContainer.removeViewAt(tableContainer.getChildCount() - 1);
        }
    }


    //private void add vatcolumn()
    private void addvatTableLayout() {
        LayoutInflater inflater = LayoutInflater.from(this);
        View tableLayout = inflater.inflate(R.layout.texinfo_two, null);
        vat_layout.addView(tableLayout, vat_layout.getChildCount()); // Add new table layout at the end
        isVatTable = false; // Set flag to false after adding the first table layout
    }
    private void removevatTableLayout() {
        if (!isVatTable && vat_layout.getChildCount() > 1) {
            View tableLayout = vat_layout.getChildAt(vat_layout.getChildCount() - 1);
            vat_layout.removeViewAt(vat_layout.getChildCount() - 1);
        }
    }






    //private void add vatcolumn()



    private void layoutToPdfConverter() {
        if (checkPermission()) {
            LinearLayout layout = findViewById(R.id.sllayout);
            createPdf(layout);
        } else {
            requestPermission();
        }
    }

    private PdfDocument document;

    private void createPdf(LinearLayout layout) {
        document = new PdfDocument();
        PdfDocument.PageInfo pageInfo = new PdfDocument.PageInfo.Builder(layout.getWidth(), layout.getHeight(), 1).create();
        PdfDocument.Page page = document.startPage(pageInfo);

        Canvas canvas = page.getCanvas();
        canvas.drawColor(Color.WHITE);
        layout.draw(canvas);

        document.finishPage(page);

        Intent intent = new Intent(Intent.ACTION_OPEN_DOCUMENT_TREE);
        startActivityForResult(intent, 1);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

        if (requestCode == PICK_SIGNATURE_IMAGE && resultCode == RESULT_OK && data != null) {
            try {
                Uri imageUri = data.getData();
                if (imageUri != null) {
                    // Get the ImageView and Layout
                    ImageView signatureImage = findViewById(R.id.signature_image);
                    LinearLayout signatureLayout = findViewById(R.id.signaturelay);

                    // Make the signature layout visible
                    signatureLayout.setVisibility(View.VISIBLE);

                    // Load image using Glide (ensure Glide dependency is added)
                    Glide.with(this)
                            .load(imageUri)
                            .into(signatureImage);

                    // Save the signature image URI
                    saveSignatureUri(imageUri);

                    // Take persistable URI permission
                    final int takeFlags = Intent.FLAG_GRANT_READ_URI_PERMISSION;
                    getContentResolver().takePersistableUriPermission(imageUri, takeFlags);
                }
            } catch (Exception e) {
                e.printStackTrace();

            }
        } else if (requestCode == REQUEST_PDF_SELECT && resultCode == RESULT_OK) {
            final Uri pdfUri = data.getData();

            // Inflate the custom dialog layout
            View dialogView = LayoutInflater.from(this).inflate(R.layout.custom_name_dialog, null);
            final EditText editTextFileName = dialogView.findViewById(R.id.editTextFileName);
            Button btnSave = dialogView.findViewById(R.id.btnSave);
            Button btnCancel = dialogView.findViewById(R.id.btnCancel);

            // Create and show the dialog
            final AlertDialog dialog = new AlertDialog.Builder(this)
                    .setView(dialogView)
                    .setCancelable(false)
                    .create();

            // Set dialog window attributes for rounded corners
            if (dialog.getWindow() != null) {
                dialog.getWindow().setBackgroundDrawableResource(android.R.color.transparent);
            }

            dialog.show();

            // Handle save button click
            btnSave.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    String fileName = editTextFileName.getText().toString().trim();
                    if (fileName.isEmpty()) {
                        Toast.makeText(InvoiceTwo.this, "Please enter a file name", Toast.LENGTH_SHORT).show();
                        return;
                    }

                    if (!fileName.endsWith(".pdf")) {
                        fileName += ".pdf";
                    }

                    dialog.dismiss();
                    uploadPdfToServer(pdfUri, fileName);
                }
            });

            // Handle cancel button click
            btnCancel.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    dialog.dismiss();
                }
            });
        } else if (requestCode == 1 && resultCode == RESULT_OK) {
            Uri treeUri = data.getData();
            DocumentFile pickedDir = DocumentFile.fromTreeUri(this, treeUri);

            // Create a custom layout for the file name input
            LayoutInflater inflater = getLayoutInflater();
            View dialogView = inflater.inflate(R.layout.custom_file_name_dialog, null);

            final AppCompatEditText fileNameInput = dialogView.findViewById(R.id.file_name_input);
            Button saveButton = dialogView.findViewById(R.id.save_button);
            Button cancelButton = dialogView.findViewById(R.id.cancel_button);

            // Set the title and buttons
            TextView titleView = dialogView.findViewById(R.id.title_text);
            titleView.setText("Enter file name");

            // Create an AlertDialog to get the file name from the user
            final AlertDialog alertDialog = new AlertDialog.Builder(this)
                    .setView(dialogView)
                    .create();
            alertDialog.show();

            saveButton.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    String fileName = fileNameInput.getText().toString();
                    if (!fileName.endsWith(".pdf")) {
                        fileName += ".pdf";
                    }
                    DocumentFile file = pickedDir.createFile("application/pdf", fileName);
                    try {
                        Log.d("PDF_CREATION", "Writing PDF to file...");
                        OutputStream out = getContentResolver().openOutputStream(file.getUri());
                        document.writeTo(out);
                        out.close();
                        document.close();
                        Log.d("PDF_CREATION", "PDF generated successfully!");

                        // Upload the PDF to server with the custom file name
                        uploadPdfToServer(file.getUri(), fileName);
                        alertDialog.dismiss(); // Dismiss the dialog
                    } catch (IOException e) {
                        Log.e("PDF_CREATION", "Error generating PDF: " + e.getMessage());
                        e.printStackTrace();
                    }
                }
            });

            cancelButton.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    alertDialog.dismiss(); // Dismiss the dialog
                }
            });
        }
    }

    private void saveSignatureUri(Uri uri) {
        SharedPreferences sharedPreferences = getSharedPreferences(SHARED_PREF_NAME, MODE_PRIVATE);
        SharedPreferences.Editor editor = sharedPreferences.edit();
        editor.putString(SIGNATURE_URI_KEY, uri.toString());
        editor.apply();
    }

    private void loadSavedSignature() {
        SharedPreferences sharedPreferences = getSharedPreferences(SHARED_PREF_NAME, MODE_PRIVATE);
        String uriString = sharedPreferences.getString(SIGNATURE_URI_KEY, null);

        if (uriString != null) {
            Uri uri = Uri.parse(uriString);
            ImageView signatureImage = findViewById(R.id.signature_image);
            findViewById(R.id.signaturelay).setVisibility(View.VISIBLE);
            signatureImage.setImageURI(uri);
        }
    }

    private void uploadPdfToServer(Uri pdfUri, String fileName) {
        final AlertDialog alertDialog = new AlertDialog.Builder(this)
                .setTitle("Uploading PDF...")
                .setMessage("Please wait...")
                .setView(R.layout.loading_dialog)
                .create();

        alertDialog.show();
        
        // Create a temporary file from the URI
        java.io.File pdfFile = createTempFileFromUri(pdfUri, fileName);
        if (pdfFile == null) {
            alertDialog.dismiss();
            Toast.makeText(this, "Failed to prepare PDF for upload", Toast.LENGTH_SHORT).show();
            return;
        }
        
        // Create a new thread for network operations
        new Thread(new Runnable() {
            @Override
            public void run() {
                try {
                    // Create multipart request
                    String boundary = "*****" + System.currentTimeMillis() + "*****";
                    String lineEnd = "\r\n";
                    String twoHyphens = "--";
                    
                    // Use the server URL from Config class
                    String serverUrl = Config.UPLOAD_URL;
                    
                    // Open a connection to the server
                    java.net.URL url = new java.net.URL(serverUrl);
                    java.net.HttpURLConnection connection = (java.net.HttpURLConnection) url.openConnection();
                    
                    // Set up the connection for a POST request
                    connection.setDoInput(true);
                    connection.setDoOutput(true);
                    connection.setUseCaches(false);
                    connection.setRequestMethod("POST");
                    connection.setRequestProperty("Connection", "Keep-Alive");
                    connection.setRequestProperty("Content-Type", "multipart/form-data; boundary=" + boundary);
                    
                    // Create output stream to write data to connection
                    java.io.DataOutputStream outputStream = new java.io.DataOutputStream(connection.getOutputStream());
                    
                    // Add the file data to the request
                    outputStream.writeBytes(twoHyphens + boundary + lineEnd);
                    outputStream.writeBytes("Content-Disposition: form-data; name=\"pdf_file\"; filename=\"" + fileName + "\"" + lineEnd);
                    outputStream.writeBytes("Content-Type: application/pdf" + lineEnd);
                    outputStream.writeBytes(lineEnd);
                    
                    // Read the file and write it to the output stream
                    java.io.FileInputStream fileInputStream = new java.io.FileInputStream(pdfFile);
                    int bytesAvailable = fileInputStream.available();
                    int maxBufferSize = 1024 * 1024;
                    int bufferSize = Math.min(bytesAvailable, maxBufferSize);
                    byte[] buffer = new byte[bufferSize];
                    
                    int bytesRead = fileInputStream.read(buffer, 0, bufferSize);
                    while (bytesRead > 0) {
                        outputStream.write(buffer, 0, bytesRead);
                        bytesAvailable = fileInputStream.available();
                        bufferSize = Math.min(bytesAvailable, maxBufferSize);
                        bytesRead = fileInputStream.read(buffer, 0, bufferSize);
                    }
                    
                    // Add user information if needed
                    outputStream.writeBytes(lineEnd);
                    outputStream.writeBytes(twoHyphens + boundary + lineEnd);
                    outputStream.writeBytes("Content-Disposition: form-data; name=\"uploaded_by\"" + lineEnd);
                    outputStream.writeBytes(lineEnd);
                    outputStream.writeBytes("InvoiceTwo App");
                    outputStream.writeBytes(lineEnd);
                    
                    // End of multipart request
                    outputStream.writeBytes(lineEnd);
                    outputStream.writeBytes(twoHyphens + boundary + twoHyphens + lineEnd);
                    
                    // Close streams
                    outputStream.flush();
                    outputStream.close();
                    fileInputStream.close();
                    
                    // Get the server response
                    int responseCode = connection.getResponseCode();
                    if (responseCode == 200) {
                        java.io.InputStream inputStream = connection.getInputStream();
                        java.io.BufferedReader reader = new java.io.BufferedReader(new java.io.InputStreamReader(inputStream));
                        StringBuilder response = new StringBuilder();
                        String line;
                        while ((line = reader.readLine()) != null) {
                            response.append(line);
                        }
                        reader.close();
                        
                        // Parse the JSON response
                        final String jsonResponse = response.toString();
                        Log.d("PDF_UPLOAD", "Server response: " + jsonResponse);
                        
                        // Update UI on the main thread
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                alertDialog.dismiss();
                                try {
                                    // Simple JSON parsing
                                    if (jsonResponse.contains("\"success\":true")) {
                                        Toast.makeText(InvoiceTwo.this, "PDF uploaded successfully", Toast.LENGTH_SHORT).show();
                                    } else {
                                        Toast.makeText(InvoiceTwo.this, "Upload failed: " + jsonResponse, Toast.LENGTH_SHORT).show();
                                    }
                                } catch (Exception e) {
                                    Toast.makeText(InvoiceTwo.this, "Error parsing response", Toast.LENGTH_SHORT).show();
                                    Log.e("PDF_UPLOAD", "Error parsing JSON: " + e.getMessage());
                                }
                            }
                        });
                    } else {
                        // Handle error response
                        final int finalResponseCode = responseCode;
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                alertDialog.dismiss();
                                Toast.makeText(InvoiceTwo.this, "Upload failed with code: " + finalResponseCode, Toast.LENGTH_SHORT).show();
                            }
                        });
                    }
                } catch (final Exception e) {
                    // Handle exceptions
                    Log.e("PDF_UPLOAD", "Error uploading PDF: " + e.getMessage());
                    e.printStackTrace();
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            alertDialog.dismiss();
                            Toast.makeText(InvoiceTwo.this, "Error uploading PDF: " + e.getMessage(), Toast.LENGTH_SHORT).show();
                        }
                    });
                } finally {
                    // Delete the temporary file
                    if (pdfFile != null && pdfFile.exists()) {
                        pdfFile.delete();
                    }
                }
            }
        }).start();
    }

    /**
     * Create a temporary file from URI for upload
     */
    private java.io.File createTempFileFromUri(Uri uri, String fileName) {
        try {
            java.io.InputStream inputStream = getContentResolver().openInputStream(uri);
            if (inputStream == null) return null;

            // Create temp file in cache directory
            java.io.File tempFile = new java.io.File(getCacheDir(), fileName);
            java.io.FileOutputStream outputStream = new java.io.FileOutputStream(tempFile);

            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }

            outputStream.close();
            inputStream.close();

            return tempFile;
        } catch (Exception e) {
            Log.e("FileUtils", "Error creating temp file: " + e.getMessage());
            return null;
        }
    }

    private boolean checkPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            return Environment.isExternalStorageManager();
        } else {
            int result = ContextCompat.checkSelfPermission(InvoiceTwo.this, Manifest.permission.READ_EXTERNAL_STORAGE);
            int result1 = ContextCompat.checkSelfPermission(InvoiceTwo.this, Manifest.permission.WRITE_EXTERNAL_STORAGE);
            return result == PackageManager.PERMISSION_GRANTED && result1 == PackageManager.PERMISSION_GRANTED;
        }
    }

    private void requestPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            try {
                Intent intent = new Intent(Settings.ACTION_MANAGE_APP_ALL_FILES_ACCESS_PERMISSION);
                intent.addCategory("android.intent.category.DEFAULT");
                intent.setData(Uri.parse(String.format("package:%s", getApplicationContext().getPackageName())));
                startActivityForResult(intent, REQUEST_PERMISSION_CODE_ANDROID_R);
            } catch (Exception e) {
                Intent intent = new Intent();
                intent.setAction(Settings.ACTION_MANAGE_ALL_FILES_ACCESS_PERMISSION);
                startActivityForResult(intent, REQUEST_PERMISSION_CODE_ANDROID_R);
            }
        } else {
            //below android 11
            ActivityCompat.requestPermissions(InvoiceTwo.this, new String[]{Manifest.permission.WRITE_EXTERNAL_STORAGE, Manifest.permission.READ_EXTERNAL_STORAGE}, REQUEST_PERMISSION_CODE);
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == REQUEST_PERMISSION_CODE) {
            if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                layoutToPdfConverter();
            } else {

            }
        }
    }


}