package com.official.invoicegenarator.utils;

import android.content.Context;
import android.util.Log;

import com.android.volley.AuthFailureError;
import com.android.volley.DefaultRetryPolicy;
import com.android.volley.Request;
import com.android.volley.RequestQueue;
import com.android.volley.toolbox.StringRequest;
import com.android.volley.toolbox.Volley;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.official.invoicegenarator.Config;

import org.json.JSONException;
import org.json.JSONObject;

import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

public class ApiClient {
    private static final String TAG = "ApiClient";
    private static ApiClient instance;
    private final RequestQueue requestQueue;
    private final Gson gson;

    private ApiClient(Context context) {
        requestQueue = Volley.newRequestQueue(context.getApplicationContext());
        gson = new Gson();
    }

    public static synchronized ApiClient getInstance(Context context) {
        if (instance == null) {
            instance = new ApiClient(context);
        }
        return instance;
    }

    public interface ApiResponseListener {
        void onSuccess(JSONObject response);
        void onError(String message);
    }

    public void createInvoiceDataItem(Map<String, String> data, ApiResponseListener listener) {
        String url = BASE_URL + "/invoice_tracker/invoice_data_items.php";
        makeRequest(Request.Method.POST, url, data, listener);
    }

    public void updateInvoiceDataItem(int id, Map<String, String> data, ApiResponseListener listener) {
        String url = BASE_URL + "/invoice_tracker/invoice_data_items.php/" + id;
        makeRequest(Request.Method.PUT, url, data, listener);
    }

    public void deleteInvoiceDataItem(int id, ApiResponseListener listener) {
        String url = BASE_URL + "/invoice_tracker/invoice_data_items.php/" + id;
        makeRequest(Request.Method.DELETE, url, null, listener);
    }

    public void getInvoiceDataItems(ApiResponseListener listener) {
        String url = BASE_URL + "/invoice_tracker/invoice_data_items.php";
        makeRequest(Request.Method.GET, url, null, listener);
    }

    public void getInvoiceDataItem(int id, ApiResponseListener listener) {
        String url = BASE_URL + "/invoice_tracker/invoice_data_items.php/" + id;
        makeRequest(Request.Method.GET, url, null, listener);
    }

    private void makeRequest(int method, String url, Map<String, String> data, ApiResponseListener listener) {
        StringRequest stringRequest = new StringRequest(method, url,
                response -> {
                    try {
                        JSONObject jsonResponse = new JSONObject(response);
                        listener.onSuccess(jsonResponse);
                    } catch (JSONException e) {
                        Log.e(TAG, "JSON parsing error: " + e.getMessage());
                        listener.onError("Error parsing response");
                    }
                },
                error -> {
                    String errorMessage = "Unknown error";
                    if (error.networkResponse != null && error.networkResponse.data != null) {
                        errorMessage = new String(error.networkResponse.data, StandardCharsets.UTF_8);
                    } else if (error.getMessage() != null) {
                        errorMessage = error.getMessage();
                    }
                    Log.e(TAG, "API Error: " + errorMessage);
                    listener.onError(errorMessage);
                }) {
            @Override
            public byte[] getBody() throws AuthFailureError {
                if (data != null && !data.isEmpty()) {
                    JSONObject jsonObject = new JSONObject(data);
                    return jsonObject.toString().getBytes(StandardCharsets.UTF_8);
                }
                return null;
            }

            @Override
            public String getBodyContentType() {
                return "application/json; charset=utf-8";
            }
        };

        // Set retry policy
        stringRequest.setRetryPolicy(new DefaultRetryPolicy(
                10000, // 10 seconds timeout
                DefaultRetryPolicy.DEFAULT_MAX_RETRIES,
                DefaultRetryPolicy.DEFAULT_BACKOFF_MULT));

        // Add request to the queue
        requestQueue.add(stringRequest);
    }
}
