<?php
/**
 * Database Test for Attendance System
 */

require_once '../config/config.php';

try {
    $pdo = getDbConnection();
    echo "<h3>Database Connection: SUCCESS</h3>";
    
    // Check if attendance tables exist
    $tables = [
        'workers',
        'attendance_records', 
        'attendance_alerts',
        'departments'
    ];
    
    echo "<h4>Table Status:</h4>";
    foreach ($tables as $table) {
        $stmt = $pdo->prepare("SHOW TABLES LIKE ?");
        $stmt->execute([$table]);
        $exists = $stmt->fetch();
        
        if ($exists) {
            echo "<p style='color: green;'>✓ Table '$table' exists</p>";
            
            // Count records
            $countStmt = $pdo->prepare("SELECT COUNT(*) as count FROM `$table`");
            $countStmt->execute();
            $count = $countStmt->fetch()['count'];
            echo "<p style='margin-left: 20px;'>Records: $count</p>";
        } else {
            echo "<p style='color: red;'>✗ Table '$table' does not exist</p>";
        }
    }
    
    echo "<h4>Session Info:</h4>";
    echo "<p>Session ID: " . session_id() . "</p>";
    echo "<p>Admin User: " . (isset($_SESSION['admin_user']) ? 'SET' : 'NOT SET') . "</p>";
    echo "<p>User ID: " . (isset($_SESSION['user_id']) ? $_SESSION['user_id'] : 'NOT SET') . "</p>";
    
} catch (Exception $e) {
    echo "<h3 style='color: red;'>Database Connection: FAILED</h3>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
}
?>
