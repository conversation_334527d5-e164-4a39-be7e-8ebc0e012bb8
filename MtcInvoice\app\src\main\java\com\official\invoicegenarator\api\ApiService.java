package com.official.invoicegenarator.api;

import com.official.invoicegenarator.model.FileListResponse;

import retrofit2.Call;
import retrofit2.http.DELETE;
import retrofit2.http.GET;
import retrofit2.http.Query;

public interface ApiService {
    @GET("files/list.php")
    Call<FileListResponse> getFileList(
            @Query("page") int page,
            @Query("limit") int limit
    );

    @DELETE("files/delete.php")
    Call<FileListResponse> deleteFile(
            @Query("id") int fileId
    );
}
