<?php
require_once __DIR__ . '/../config/config.php';

header('Content-Type: application/json');

$page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
$limit = isset($_GET['limit']) ? max(1, intval($_GET['limit'])) : 10;
$offset = ($page - 1) * $limit;

// Get the base URL for file links
$protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://';
$host = $_SERVER['HTTP_HOST'];
$basePath = dirname(dirname(dirname($_SERVER['PHP_SELF']))); // Go up one more level to remove 'api/files'
$baseUrl = rtrim($protocol . $host . $basePath, '/');

$db = new Database();
$conn = $db->getConnection();

if ($conn) {
    // Get total count
    $countStmt = $conn->prepare("SELECT COUNT(*) as total FROM documents");
    $countStmt->execute();
    $totalRows = $countStmt->fetch()['total'];
    $totalPages = ceil($totalRows / $limit);

    // Get paginated result
    $stmt = $conn->prepare("SELECT id, file_name, file_size_kb, upload_date 
                            FROM documents 
                            ORDER BY upload_date DESC 
                            LIMIT ? OFFSET ?");
    $stmt->bindValue(1, $limit, PDO::PARAM_INT);
    $stmt->bindValue(2, $offset, PDO::PARAM_INT);
    $stmt->execute();

    $files = [];
    // Get the correct upload directory path
    $uploadDir = $_SERVER['DOCUMENT_ROOT'] . '/MtcInvoiceNewProject/api/uploads/pdfs/';
    $diskFiles = [];
    
    // Safely scan directory
    if (is_dir($uploadDir)) {
        $diskFiles = array_diff(scandir($uploadDir), array('.', '..'));
    } else {
        error_log("Upload directory not found: " . $uploadDir);
    }
    
    while ($row = $stmt->fetch()) {
        $fileName = $row['file_name'];
        $filePath = 'pdfs/' . $fileName;  // This matches what's in the database
        $actualFileName = $fileName;  // Default to the filename in the database
        
        // Check if file exists in the uploads directory
        if (!in_array($fileName, $diskFiles)) {
            error_log("File not found in uploads directory: " . $fileName);
            error_log("Available files: " . print_r($diskFiles, true));
        }
        
        // Generate the file URL with the actual filename
        $fileUrl = "$baseUrl/api/uploads/pdfs/" . $actualFileName;
        
        // Debug log
        error_log("Generated URL for {$fileName}: {$fileUrl}");
        
        // Set display name (use the original filename from database)
        $displayName = $fileName;
        
        $files[] = [
            "id" => $row['id'],
            "name" => $displayName,  // Original filename from database
            "filename" => $actualFileName,  // Actual filename with timestamp from filesystem
            "size" => round($row['file_size_kb'], 2) . " KB",
            "upload_date" => date("Y-m-d H:i", strtotime($row['upload_date'])),
            "url" => $fileUrl,
            "download_url" => $fileUrl . '?download=1'  // Add download parameter for force download
        ];
    }

    echo json_encode([
        "success" => true,
        "current_page" => $page,
        "total_pages" => $totalPages,
        "total_files" => $totalRows,
        "files" => $files,
        "base_url" => $baseUrl  // For reference
    ]);
} else {
    echo json_encode([
        "success" => false,
        "message" => "Database connection failed."
    ]);
}
