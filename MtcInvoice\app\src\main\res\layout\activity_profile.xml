<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@drawable/gradient_background">

    <!-- Toolbar -->
    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/colorPrimary"
        android:theme="@style/ThemeOverlay.MaterialComponents.Dark.ActionBar"
        app:title="Profile"
        app:titleTextColor="@android:color/white"
        app:navigationIcon="@drawable/ic_arrow_back" />

    <!-- Main Content -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:padding="16dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- Profile Header -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="24dp"
                    android:gravity="center">

                    <!-- Profile Icon -->
                    <ImageView
                        android:layout_width="80dp"
                        android:layout_height="80dp"
                        android:layout_marginBottom="16dp"
                        android:src="@drawable/ic_person_circle"
                        android:tint="@color/colorPrimary" />

                    <!-- User Name -->
                    <TextView
                        android:id="@+id/textUserName"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Loading..."
                        android:textSize="24sp"
                        android:textStyle="bold"
                        android:textColor="@color/colorPrimary"
                        android:layout_marginBottom="8dp" />

                    <!-- Login Status -->
                    <TextView
                        android:id="@+id/textLoginStatus"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Checking..."
                        android:textSize="14sp"
                        android:textStyle="italic" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Profile Information -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Account Information"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/colorPrimary"
                        android:layout_marginBottom="16dp" />

                    <!-- Email -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="12dp">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/ic_email"
                            android:tint="@color/colorPrimary"
                            android:layout_marginEnd="12dp"
                            android:layout_gravity="center_vertical" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Email"
                                android:textSize="12sp"
                                android:textColor="@android:color/darker_gray" />

                            <TextView
                                android:id="@+id/textUserEmail"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Loading..."
                                android:textSize="16sp"
                                android:textColor="@android:color/black" />

                        </LinearLayout>

                    </LinearLayout>

                    <!-- Role -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/ic_person"
                            android:tint="@color/colorPrimary"
                            android:layout_marginEnd="12dp"
                            android:layout_gravity="center_vertical" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Role"
                                android:textSize="12sp"
                                android:textColor="@android:color/darker_gray" />

                            <TextView
                                android:id="@+id/textUserRole"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Loading..."
                                android:textSize="16sp"
                                android:textColor="@android:color/black" />

                        </LinearLayout>

                    </LinearLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Actions -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Actions"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/colorPrimary"
                        android:layout_marginBottom="16dp" />

                    <!-- Refresh Profile Button -->
                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/buttonRefreshProfile"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Refresh Profile"
                        android:textColor="@android:color/white"
                        android:backgroundTint="@color/colorPrimary"
                        android:layout_marginBottom="12dp"
                        app:icon="@drawable/ic_refresh"
                        app:iconGravity="textStart"
                        style="@style/Widget.MaterialComponents.Button" />

                    <!-- Logout Button -->
                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/buttonLogout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Logout"
                        android:textColor="@android:color/white"
                        android:backgroundTint="@android:color/holo_red_dark"
                        app:icon="@drawable/ic_logout"
                        app:iconGravity="textStart"
                        style="@style/Widget.MaterialComponents.Button" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

        </LinearLayout>

    </ScrollView>

</LinearLayout>
