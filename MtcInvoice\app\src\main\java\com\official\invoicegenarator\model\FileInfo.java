package com.official.invoicegenarator.model;

import com.google.gson.annotations.SerializedName;

public class FileInfo {
    @SerializedName("id")
    private int id;
    
    @SerializedName("name")
    private String displayName;
    
    @SerializedName("filename")
    private String filename;
    
    @SerializedName("size")
    private String size;
    
    @SerializedName("upload_date")
    private String uploadDate;
    
    @SerializedName("url")
    private String url;
    
    @SerializedName("download_url")
    private String downloadUrl;

    // Getters and Setters
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    /**
     * Returns the display name of the file (without timestamp)
     */
    public String getName() {
        return displayName != null ? displayName : filename;
    }

    public void setName(String name) {
        this.displayName = name;
    }
    
    /**
     * Returns the actual filename on the server (with timestamp)
     */
    public String getFilename() {
        return filename != null ? filename : displayName;
    }
    
    public void setFilename(String filename) {
        this.filename = filename;
    }

    public String getSize() {
        return size;
    }

    public void setSize(String size) {
        this.size = size;
    }

    public String getUploadDate() {
        return uploadDate;
    }

    public void setUploadDate(String uploadDate) {
        this.uploadDate = uploadDate;
    }
    
    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    /**
     * Returns the URL to view the file
     * @return URL to view the file
     */
    public String getViewUrl() {
        // If URL is already set and absolute, return it
        if (url != null && (url.startsWith("http://") || url.startsWith("https://"))) {
            return url;
        }
        // Otherwise construct from base URL
        return Config.UPLOADS_BASE_URL + Config.PDF_UPLOAD_PATH + getFilename();
    }

    /**
     * Returns the URL to download the file
     * @return URL to download the file
     */
    public String getDownloadUrl() {
        // If download URL is explicitly set, use it
        if (downloadUrl != null && !downloadUrl.isEmpty()) {
            return downloadUrl;
        }
        // Otherwise construct download URL from base URL and filename
        return Config.UPLOADS_BASE_URL + Config.PDF_UPLOAD_PATH + getFilename();
    }
    
    public void setDownloadUrl(String downloadUrl) {
        this.downloadUrl = downloadUrl;
    }
    
    /**
     * Returns the file size in bytes
     * @return size in bytes, or 0 if size is not available or invalid
     */
    public long getSizeInBytes() {
        if (size == null || size.isEmpty()) {
            return 0;
        }
        try {
            // Remove any non-numeric characters (except decimal point if needed)
            String numericSize = size.replaceAll("[^0-9.]", "");
            return (long) Double.parseDouble(numericSize);
        } catch (NumberFormatException e) {
            return 0;
        }
    }
}
