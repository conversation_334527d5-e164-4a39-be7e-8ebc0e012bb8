<?php
/**
 * Worker Manager Class
 * 
 * Handles worker-related operations including CRUD operations,
 * schedule management, and worker analytics.
 * 
 * @package MtcInvoice Attendance
 * @version 1.0
 */

require_once __DIR__ . '/../api/config/config.php';

class WorkerManager {
    private $pdo;
    
    public function __construct() {
        $database = new Database();
        $this->pdo = $database->getConnection();
        
        if (!$this->pdo) {
            throw new Exception('Database connection failed');
        }
    }
    
    /**
     * Create a new worker
     */
    public function createWorker($data) {
        try {
            // Validate required fields
            $required = ['employee_id', 'name', 'department', 'position'];
            foreach ($required as $field) {
                if (!isset($data[$field]) || empty(trim($data[$field]))) {
                    throw new Exception("Missing required field: $field");
                }
            }
            
            // Sanitize data
            $data = $this->sanitizeWorkerData($data);
            
            // Check if employee_id already exists
            if ($this->getWorkerByEmployeeId($data['employee_id'])) {
                throw new Exception('Employee ID already exists');
            }
            
            // Split name into first_name and last_name if not provided
            if (!isset($data['first_name']) || !isset($data['last_name'])) {
                $nameParts = explode(' ', $data['name'], 2);
                $data['first_name'] = $nameParts[0];
                $data['last_name'] = isset($nameParts[1]) ? $nameParts[1] : '';
            }
            
            // Insert worker
            $stmt = $this->pdo->prepare("
                INSERT INTO workers (
                    employee_id, user_id, name, first_name, last_name, email, phone,
                    department, position, hire_date, work_schedule, hourly_rate,
                    manager_id, profile_image, emergency_contact, status, created_by
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            $stmt->execute([
                $data['employee_id'],
                $data['user_id'] ?? null,
                $data['name'],
                $data['first_name'],
                $data['last_name'],
                $data['email'] ?? null,
                $data['phone'] ?? null,
                $data['department'],
                $data['position'],
                $data['hire_date'] ?? date('Y-m-d'),
                isset($data['work_schedule']) ? json_encode($data['work_schedule']) : null,
                $data['hourly_rate'] ?? 0.00,
                $data['manager_id'] ?? null,
                $data['profile_image'] ?? null,
                isset($data['emergency_contact']) ? json_encode($data['emergency_contact']) : null,
                $data['status'] ?? 'active',
                $data['created_by'] ?? 1
            ]);
            
            $workerId = $this->pdo->lastInsertId();
            
            // Create default work schedule if not provided
            if (!isset($data['work_schedule'])) {
                $this->createDefaultWorkSchedule($workerId, $data['created_by'] ?? 1);
            }
            
            return $this->getWorkerById($workerId);
            
        } catch (Exception $e) {
            error_log("WorkerManager::createWorker error: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * Update worker information
     */
    public function updateWorker($id, $data) {
        try {
            // Check if worker exists
            if (!$this->getWorkerById($id)) {
                throw new Exception('Worker not found');
            }
            
            // Sanitize data
            $data = $this->sanitizeWorkerData($data);
            
            // Check employee_id uniqueness if being updated
            if (isset($data['employee_id'])) {
                $existing = $this->getWorkerByEmployeeId($data['employee_id']);
                if ($existing && $existing['id'] != $id) {
                    throw new Exception('Employee ID already exists');
                }
            }
            
            // Build update query
            $updates = [];
            $values = [];
            
            $allowedFields = [
                'employee_id', 'user_id', 'name', 'first_name', 'last_name', 'email', 'phone',
                'department', 'position', 'hire_date', 'work_schedule', 'hourly_rate',
                'manager_id', 'profile_image', 'emergency_contact', 'status'
            ];
            
            foreach ($allowedFields as $field) {
                if (isset($data[$field])) {
                    $updates[] = "$field = ?";
                    if (in_array($field, ['work_schedule', 'emergency_contact']) && is_array($data[$field])) {
                        $values[] = json_encode($data[$field]);
                    } else {
                        $values[] = $data[$field];
                    }
                }
            }
            
            if (empty($updates)) {
                throw new Exception('No valid fields provided for update');
            }
            
            $values[] = $id;
            
            $sql = "UPDATE workers SET " . implode(', ', $updates) . " WHERE id = ?";
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($values);
            
            return $this->getWorkerById($id);
            
        } catch (Exception $e) {
            error_log("WorkerManager::updateWorker error: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * Get worker by ID
     */
    public function getWorkerById($id) {
        try {
            $stmt = $this->pdo->prepare("
                SELECT w.*, 
                       m.name as manager_name,
                       u.email as user_email,
                       (SELECT COUNT(*) FROM attendance_records ar WHERE ar.worker_id = w.id) as total_attendance_records
                FROM workers w
                LEFT JOIN workers m ON w.manager_id = m.id
                LEFT JOIN users u ON w.user_id = u.id
                WHERE w.id = ? AND w.deleted_at IS NULL
            ");
            $stmt->execute([$id]);
            $worker = $stmt->fetch();
            
            if ($worker) {
                // Decode JSON fields
                if ($worker['work_schedule']) {
                    $worker['work_schedule'] = json_decode($worker['work_schedule'], true);
                }
                if ($worker['emergency_contact']) {
                    $worker['emergency_contact'] = json_decode($worker['emergency_contact'], true);
                }
                
                // Get current work schedule
                $worker['current_schedule'] = $this->getCurrentWorkSchedule($id);
            }
            
            return $worker;
            
        } catch (Exception $e) {
            error_log("WorkerManager::getWorkerById error: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Get worker by employee ID
     */
    public function getWorkerByEmployeeId($employeeId) {
        try {
            $stmt = $this->pdo->prepare("
                SELECT * FROM workers 
                WHERE employee_id = ? AND deleted_at IS NULL
            ");
            $stmt->execute([$employeeId]);
            return $stmt->fetch();
            
        } catch (Exception $e) {
            error_log("WorkerManager::getWorkerByEmployeeId error: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Get workers with filters
     */
    public function getWorkers($filters = []) {
        try {
            $where = ['w.deleted_at IS NULL'];
            $params = [];
            
            // Apply filters
            if (!empty($filters['department'])) {
                $where[] = 'w.department = ?';
                $params[] = $filters['department'];
            }
            
            if (!empty($filters['status'])) {
                $where[] = 'w.status = ?';
                $params[] = $filters['status'];
            }
            
            if (!empty($filters['manager_id'])) {
                $where[] = 'w.manager_id = ?';
                $params[] = $filters['manager_id'];
            }
            
            if (!empty($filters['search'])) {
                $where[] = '(w.name LIKE ? OR w.employee_id LIKE ? OR w.email LIKE ?)';
                $searchTerm = '%' . $filters['search'] . '%';
                $params[] = $searchTerm;
                $params[] = $searchTerm;
                $params[] = $searchTerm;
            }
            
            // Pagination
            $page = isset($filters['page']) ? max(1, (int)$filters['page']) : 1;
            $limit = isset($filters['limit']) ? min(100, max(1, (int)$filters['limit'])) : 50;
            $offset = ($page - 1) * $limit;
            
            // Count total records
            $countSql = "
                SELECT COUNT(*) as total 
                FROM workers w
                WHERE " . implode(' AND ', $where);
            $countStmt = $this->pdo->prepare($countSql);
            $countStmt->execute($params);
            $total = $countStmt->fetch()['total'];
            
            // Get workers
            $sql = "
                SELECT w.*, 
                       m.name as manager_name,
                       (SELECT COUNT(*) FROM attendance_records ar WHERE ar.worker_id = w.id AND ar.attendance_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)) as recent_attendance_count
                FROM workers w
                LEFT JOIN workers m ON w.manager_id = m.id
                WHERE " . implode(' AND ', $where) . "
                ORDER BY w.department ASC, w.name ASC
                LIMIT ? OFFSET ?
            ";
            
            $params[] = $limit;
            $params[] = $offset;
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);
            $workers = $stmt->fetchAll();
            
            // Decode JSON fields
            foreach ($workers as &$worker) {
                if ($worker['work_schedule']) {
                    $worker['work_schedule'] = json_decode($worker['work_schedule'], true);
                }
                if ($worker['emergency_contact']) {
                    $worker['emergency_contact'] = json_decode($worker['emergency_contact'], true);
                }
            }
            
            return [
                'workers' => $workers,
                'pagination' => [
                    'page' => $page,
                    'limit' => $limit,
                    'total' => $total,
                    'pages' => ceil($total / $limit)
                ]
            ];
            
        } catch (Exception $e) {
            error_log("WorkerManager::getWorkers error: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * Get departments list
     */
    public function getDepartments() {
        try {
            $stmt = $this->pdo->query("
                SELECT department, COUNT(*) as worker_count
                FROM workers 
                WHERE deleted_at IS NULL AND status = 'active'
                GROUP BY department
                ORDER BY department
            ");
            return $stmt->fetchAll();
            
        } catch (Exception $e) {
            error_log("WorkerManager::getDepartments error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get managers list
     */
    public function getManagers() {
        try {
            $stmt = $this->pdo->query("
                SELECT id, employee_id, name, department, position
                FROM workers 
                WHERE deleted_at IS NULL 
                AND status = 'active'
                AND (position LIKE '%manager%' OR position LIKE '%supervisor%' OR position LIKE '%lead%')
                ORDER BY name
            ");
            return $stmt->fetchAll();
            
        } catch (Exception $e) {
            error_log("WorkerManager::getManagers error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Create default work schedule for a worker
     */
    private function createDefaultWorkSchedule($workerId, $createdBy) {
        try {
            $stmt = $this->pdo->prepare("
                INSERT INTO work_schedules (
                    worker_id, schedule_name, schedule_type, 
                    monday_start, monday_end, tuesday_start, tuesday_end,
                    wednesday_start, wednesday_end, thursday_start, thursday_end,
                    friday_start, friday_end, break_duration_minutes,
                    is_active, effective_from, created_by
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            $stmt->execute([
                $workerId,
                'Standard Schedule',
                'fixed',
                '09:00:00', '17:00:00', // Monday
                '09:00:00', '17:00:00', // Tuesday
                '09:00:00', '17:00:00', // Wednesday
                '09:00:00', '17:00:00', // Thursday
                '09:00:00', '17:00:00', // Friday
                60, // break duration
                true, // is_active
                date('Y-m-d'), // effective_from
                $createdBy
            ]);
            
        } catch (Exception $e) {
            error_log("WorkerManager::createDefaultWorkSchedule error: " . $e->getMessage());
        }
    }
    
    /**
     * Get current work schedule for a worker
     */
    public function getCurrentWorkSchedule($workerId) {
        try {
            $stmt = $this->pdo->prepare("
                SELECT * FROM work_schedules 
                WHERE worker_id = ? 
                AND is_active = 1 
                AND effective_from <= CURDATE()
                AND (effective_to IS NULL OR effective_to >= CURDATE())
                ORDER BY effective_from DESC
                LIMIT 1
            ");
            $stmt->execute([$workerId]);
            return $stmt->fetch();
            
        } catch (Exception $e) {
            error_log("WorkerManager::getCurrentWorkSchedule error: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Sanitize worker data
     */
    private function sanitizeWorkerData($data) {
        $sanitized = [];
        
        foreach ($data as $key => $value) {
            if (is_string($value)) {
                $sanitized[$key] = htmlspecialchars(strip_tags(trim($value)), ENT_QUOTES, 'UTF-8');
            } elseif (is_array($value)) {
                $sanitized[$key] = $value; // Keep arrays as-is for JSON fields
            } else {
                $sanitized[$key] = $value;
            }
        }
        
        return $sanitized;
    }
    
    /**
     * Soft delete worker
     */
    public function deleteWorker($id) {
        try {
            $stmt = $this->pdo->prepare("
                UPDATE workers 
                SET deleted_at = NOW(), status = 'terminated'
                WHERE id = ?
            ");
            $stmt->execute([$id]);
            
            return $stmt->rowCount() > 0;
            
        } catch (Exception $e) {
            error_log("WorkerManager::deleteWorker error: " . $e->getMessage());
            throw $e;
        }
    }
}
