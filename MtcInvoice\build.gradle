// Top-level build file where you can add configuration options common to all sub-projects/modules.
buildscript {
    dependencies {
        classpath 'com.android.tools.build:gradle:8.11.1'
        // Google Services plugin removed - no longer using Firebase
    }
}

plugins {
    id 'com.android.application' version '8.11.1' apply false
    id 'com.android.library' version '8.11.1' apply false
}

tasks.register('clean', Delete) {
    delete rootProject.layout.buildDirectory
}