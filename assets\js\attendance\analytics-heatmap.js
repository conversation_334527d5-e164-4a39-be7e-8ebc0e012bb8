/**
 * Attendance Heatmap Visualization
 * 
 * Interactive heatmap showing attendance patterns over time
 * with color-coded status indicators and hover tooltips.
 * 
 * @package MtcInvoice Attendance
 * @version 1.0
 */

class AttendanceHeatmap {
    constructor(containerId, options = {}) {
        this.container = document.getElementById(containerId);
        this.options = {
            apiBaseUrl: '/MtcInvoiceNewProject/api/attendance',
            cellSize: 12,
            cellPadding: 2,
            monthsToShow: 3,
            statusColors: {
                present: '#28a745',
                late: '#ffc107',
                absent: '#dc3545',
                sick_leave: '#fd7e14',
                vacation: '#6f42c1',
                remote_work: '#6c757d',
                holiday: '#17a2b8',
                empty: '#f8f9fa'
            },
            ...options
        };
        
        this.heatmapData = [];
        this.currentWorker = null;
        
        this.init();
    }
    
    async init() {
        try {
            await this.loadHeatmapData();
            this.renderHeatmap();
            this.setupEventListeners();
        } catch (error) {
            console.error('Failed to initialize heatmap:', error);
            this.showError('Failed to load heatmap data');
        }
    }
    
    setupEventListeners() {
        // Worker filter change
        const workerSelect = document.getElementById('heatmapWorker');
        if (workerSelect) {
            workerSelect.addEventListener('change', (e) => {
                this.currentWorker = e.target.value || null;
                this.loadHeatmapData().then(() => this.renderHeatmap());
            });
        }
    }
    
    async loadHeatmapData() {
        try {
            const endDate = new Date();
            const startDate = new Date();
            startDate.setMonth(startDate.getMonth() - this.options.monthsToShow);
            
            const params = new URLSearchParams({
                start_date: startDate.toISOString().split('T')[0],
                end_date: endDate.toISOString().split('T')[0]
            });
            
            if (this.currentWorker) {
                params.append('worker_id', this.currentWorker);
            }
            
            const response = await fetch(`${this.options.apiBaseUrl}/analytics.php/heatmap?${params}`);
            const data = await response.json();
            
            if (data.success) {
                this.heatmapData = data.data || [];
                this.processHeatmapData();
            } else {
                throw new Error(data.message || 'Failed to load heatmap data');
            }
            
        } catch (error) {
            console.error('Failed to load heatmap data:', error);
            this.heatmapData = [];
        }
    }
    
    processHeatmapData() {
        // Group data by date for easier lookup
        this.dataByDate = {};
        this.heatmapData.forEach(record => {
            const date = record.attendance_date;
            if (!this.dataByDate[date]) {
                this.dataByDate[date] = [];
            }
            this.dataByDate[date].push(record);
        });
    }
    
    renderHeatmap() {
        if (!this.container) return;
        
        this.container.innerHTML = '';
        
        const endDate = new Date();
        const startDate = new Date();
        startDate.setMonth(startDate.getMonth() - this.options.monthsToShow);
        
        // Create SVG container
        const svg = this.createSVG();
        this.container.appendChild(svg);
        
        // Render calendar grid
        this.renderCalendarGrid(svg, startDate, endDate);
        
        // Add legend
        this.renderLegend();
    }
    
    createSVG() {
        const weeksToShow = Math.ceil(this.options.monthsToShow * 4.33); // Approximate weeks per month
        const width = (this.options.cellSize + this.options.cellPadding) * weeksToShow + 100;
        const height = (this.options.cellSize + this.options.cellPadding) * 7 + 100; // 7 days per week
        
        const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
        svg.setAttribute('width', width);
        svg.setAttribute('height', height);
        svg.setAttribute('class', 'heatmap-svg');
        
        return svg;
    }
    
    renderCalendarGrid(svg, startDate, endDate) {
        const current = new Date(startDate);
        const cellSize = this.options.cellSize;
        const cellPadding = this.options.cellPadding;
        
        // Start from the beginning of the week
        current.setDate(current.getDate() - current.getDay());
        
        let weekIndex = 0;
        const monthLabels = new Set();
        
        while (current <= endDate) {
            const weekStart = new Date(current);
            
            // Render week
            for (let dayIndex = 0; dayIndex < 7; dayIndex++) {
                const cellDate = new Date(current);
                const dateStr = cellDate.toISOString().split('T')[0];
                
                const x = weekIndex * (cellSize + cellPadding) + 50;
                const y = dayIndex * (cellSize + cellPadding) + 50;
                
                // Create cell
                const rect = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
                rect.setAttribute('x', x);
                rect.setAttribute('y', y);
                rect.setAttribute('width', cellSize);
                rect.setAttribute('height', cellSize);
                rect.setAttribute('rx', 2);
                rect.setAttribute('class', 'heatmap-cell');
                
                // Determine cell color and data
                const cellData = this.getCellData(dateStr);
                const color = this.getCellColor(cellData);
                rect.setAttribute('fill', color);
                
                // Add tooltip
                const tooltip = this.createTooltip(cellDate, cellData);
                rect.setAttribute('data-tooltip', tooltip);
                
                // Add hover effects
                rect.addEventListener('mouseenter', (e) => this.showTooltip(e, tooltip));
                rect.addEventListener('mouseleave', () => this.hideTooltip());
                
                svg.appendChild(rect);
                
                current.setDate(current.getDate() + 1);
            }
            
            // Add month label
            const monthLabel = weekStart.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });
            if (!monthLabels.has(monthLabel)) {
                monthLabels.add(monthLabel);
                
                const text = document.createElementNS('http://www.w3.org/2000/svg', 'text');
                text.setAttribute('x', weekIndex * (cellSize + cellPadding) + 50);
                text.setAttribute('y', 40);
                text.setAttribute('class', 'heatmap-month-label');
                text.textContent = monthLabel;
                svg.appendChild(text);
            }
            
            weekIndex++;
        }
        
        // Add day labels
        const dayLabels = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
        dayLabels.forEach((day, index) => {
            const text = document.createElementNS('http://www.w3.org/2000/svg', 'text');
            text.setAttribute('x', 40);
            text.setAttribute('y', index * (cellSize + cellPadding) + 50 + cellSize / 2);
            text.setAttribute('class', 'heatmap-day-label');
            text.setAttribute('text-anchor', 'end');
            text.setAttribute('dominant-baseline', 'middle');
            text.textContent = day;
            svg.appendChild(text);
        });
    }
    
    getCellData(dateStr) {
        const records = this.dataByDate[dateStr] || [];
        
        if (records.length === 0) {
            return { status: 'empty', count: 0 };
        }
        
        // If single worker, return their status
        if (this.currentWorker) {
            const record = records.find(r => r.worker_id == this.currentWorker);
            return record ? { status: record.status, count: 1, record } : { status: 'empty', count: 0 };
        }
        
        // For all workers, calculate dominant status
        const statusCounts = {};
        records.forEach(record => {
            statusCounts[record.status] = (statusCounts[record.status] || 0) + 1;
        });
        
        const dominantStatus = Object.keys(statusCounts).reduce((a, b) => 
            statusCounts[a] > statusCounts[b] ? a : b
        );
        
        return {
            status: dominantStatus,
            count: records.length,
            statusCounts,
            records
        };
    }
    
    getCellColor(cellData) {
        const status = cellData.status;
        const baseColor = this.options.statusColors[status] || this.options.statusColors.empty;
        
        if (status === 'empty') {
            return baseColor;
        }
        
        // For multiple workers, adjust opacity based on attendance rate
        if (!this.currentWorker && cellData.statusCounts) {
            const totalWorkers = Object.values(cellData.statusCounts).reduce((a, b) => a + b, 0);
            const presentWorkers = (cellData.statusCounts.present || 0) + (cellData.statusCounts.late || 0);
            const attendanceRate = presentWorkers / totalWorkers;
            
            // Adjust color intensity based on attendance rate
            const opacity = 0.3 + (attendanceRate * 0.7); // 0.3 to 1.0
            return this.hexToRgba(baseColor, opacity);
        }
        
        return baseColor;
    }
    
    hexToRgba(hex, alpha) {
        const r = parseInt(hex.slice(1, 3), 16);
        const g = parseInt(hex.slice(3, 5), 16);
        const b = parseInt(hex.slice(5, 7), 16);
        return `rgba(${r}, ${g}, ${b}, ${alpha})`;
    }
    
    createTooltip(date, cellData) {
        const dateStr = date.toLocaleDateString('en-US', { 
            weekday: 'long', 
            year: 'numeric', 
            month: 'long', 
            day: 'numeric' 
        });
        
        if (cellData.status === 'empty') {
            return `${dateStr}\nNo attendance data`;
        }
        
        if (this.currentWorker) {
            const record = cellData.record;
            let tooltip = `${dateStr}\nStatus: ${this.formatStatus(cellData.status)}`;
            
            if (record) {
                if (record.check_in_time) tooltip += `\nCheck In: ${record.check_in_time}`;
                if (record.check_out_time) tooltip += `\nCheck Out: ${record.check_out_time}`;
                if (record.productivity_score) tooltip += `\nProductivity: ${record.productivity_score}/5`;
                if (record.notes) tooltip += `\nNotes: ${record.notes}`;
            }
            
            return tooltip;
        }
        
        // Multiple workers tooltip
        let tooltip = `${dateStr}\nTotal Workers: ${cellData.count}`;
        
        if (cellData.statusCounts) {
            Object.entries(cellData.statusCounts).forEach(([status, count]) => {
                tooltip += `\n${this.formatStatus(status)}: ${count}`;
            });
        }
        
        return tooltip;
    }
    
    formatStatus(status) {
        return status.split('_').map(word => 
            word.charAt(0).toUpperCase() + word.slice(1)
        ).join(' ');
    }
    
    showTooltip(event, content) {
        // Remove existing tooltip
        this.hideTooltip();
        
        const tooltip = document.createElement('div');
        tooltip.className = 'heatmap-tooltip';
        tooltip.innerHTML = content.replace(/\n/g, '<br>');
        
        document.body.appendChild(tooltip);
        
        // Position tooltip
        const rect = event.target.getBoundingClientRect();
        tooltip.style.left = (rect.left + rect.width / 2) + 'px';
        tooltip.style.top = (rect.top - tooltip.offsetHeight - 10) + 'px';
        
        // Adjust if tooltip goes off screen
        const tooltipRect = tooltip.getBoundingClientRect();
        if (tooltipRect.right > window.innerWidth) {
            tooltip.style.left = (window.innerWidth - tooltipRect.width - 10) + 'px';
        }
        if (tooltipRect.left < 0) {
            tooltip.style.left = '10px';
        }
        if (tooltipRect.top < 0) {
            tooltip.style.top = (rect.bottom + 10) + 'px';
        }
    }
    
    hideTooltip() {
        const existingTooltip = document.querySelector('.heatmap-tooltip');
        if (existingTooltip) {
            existingTooltip.remove();
        }
    }
    
    renderLegend() {
        const legend = document.createElement('div');
        legend.className = 'heatmap-legend';
        
        legend.innerHTML = `
            <div class="legend-title">Status Legend</div>
            <div class="legend-items">
                ${Object.entries(this.options.statusColors).map(([status, color]) => {
                    if (status === 'empty') return '';
                    return `
                        <div class="legend-item">
                            <div class="legend-color" style="background-color: ${color}"></div>
                            <span class="legend-label">${this.formatStatus(status)}</span>
                        </div>
                    `;
                }).join('')}
            </div>
            ${!this.currentWorker ? `
                <div class="legend-note">
                    <small>Color intensity represents attendance rate when viewing all workers</small>
                </div>
            ` : ''}
        `;
        
        this.container.appendChild(legend);
    }
    
    showError(message) {
        this.container.innerHTML = `
            <div class="heatmap-error">
                <i class="bi bi-exclamation-triangle text-warning"></i>
                <p>${message}</p>
            </div>
        `;
    }
}

// Initialize heatmap when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    const heatmapContainer = document.getElementById('attendanceHeatmap');
    if (heatmapContainer) {
        window.attendanceHeatmap = new AttendanceHeatmap('attendanceHeatmap');
    }
});
