<?php
/**
 * Attendance Analytics Page
 * 
 * Advanced analytics dashboard with interactive charts, heatmaps,
 * pattern recognition, and detailed reporting capabilities.
 * 
 * @package MtcInvoice Attendance
 * @version 1.0
 */

session_start();
require_once '../../api/config/config.php';

// Check if user is logged in and has appropriate permissions
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit();
}

// Check user role
$user_role = $_SESSION['role'] ?? 'user';
if (!in_array($user_role, ['admin', 'manager'])) {
    header('Location: ../dashboard.php');
    exit();
}

$page_title = 'Attendance Analytics';
include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="page-title">Attendance Analytics</h1>
                        <p class="page-description">Advanced analytics and insights for attendance patterns</p>
                    </div>
                    <div class="page-actions">
                        <div class="date-range-picker me-3">
                            <input type="date" class="form-control" id="analyticsStartDate" value="<?= date('Y-m-01') ?>">
                            <span class="mx-2">to</span>
                            <input type="date" class="form-control" id="analyticsEndDate" value="<?= date('Y-m-d') ?>">
                        </div>
                        <button class="btn btn-outline-primary me-2" id="refreshAnalytics">
                            <i class="bi bi-arrow-clockwise"></i> Refresh
                        </button>
                        <button class="btn btn-primary" onclick="exportAnalytics()">
                            <i class="bi bi-download"></i> Export Report
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Analytics Overview Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="analytics-card">
                <div class="card-icon bg-primary">
                    <i class="bi bi-graph-up"></i>
                </div>
                <div class="card-content">
                    <h3 id="overallAttendanceRate">-</h3>
                    <p>Overall Attendance Rate</p>
                    <small class="trend" id="attendanceRateTrend"></small>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="analytics-card">
                <div class="card-icon bg-success">
                    <i class="bi bi-clock"></i>
                </div>
                <div class="card-content">
                    <h3 id="punctualityRate">-</h3>
                    <p>Punctuality Rate</p>
                    <small class="trend" id="punctualityTrend"></small>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="analytics-card">
                <div class="card-icon bg-warning">
                    <i class="bi bi-exclamation-triangle"></i>
                </div>
                <div class="card-content">
                    <h3 id="totalAlerts">-</h3>
                    <p>Active Alerts</p>
                    <small class="trend" id="alertsTrend"></small>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="analytics-card">
                <div class="card-icon bg-info">
                    <i class="bi bi-star"></i>
                </div>
                <div class="card-content">
                    <h3 id="avgProductivity">-</h3>
                    <p>Avg Productivity Score</p>
                    <small class="trend" id="productivityTrend"></small>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Analytics Content -->
    <div class="row">
        <!-- Attendance Trends Chart -->
        <div class="col-lg-8 mb-4">
            <div class="analytics-chart-card">
                <div class="chart-header">
                    <h5>Attendance Trends</h5>
                    <div class="chart-controls">
                        <div class="btn-group" role="group">
                            <input type="radio" class="btn-check" name="trendPeriod" id="trendDaily" value="daily" checked>
                            <label class="btn btn-outline-primary btn-sm" for="trendDaily">Daily</label>
                            
                            <input type="radio" class="btn-check" name="trendPeriod" id="trendWeekly" value="weekly">
                            <label class="btn btn-outline-primary btn-sm" for="trendWeekly">Weekly</label>
                            
                            <input type="radio" class="btn-check" name="trendPeriod" id="trendMonthly" value="monthly">
                            <label class="btn btn-outline-primary btn-sm" for="trendMonthly">Monthly</label>
                        </div>
                    </div>
                </div>
                <div class="chart-body">
                    <canvas id="attendanceTrendsChart"></canvas>
                </div>
            </div>
        </div>
        
        <!-- Department Performance -->
        <div class="col-lg-4 mb-4">
            <div class="analytics-chart-card">
                <div class="chart-header">
                    <h5>Department Performance</h5>
                </div>
                <div class="chart-body">
                    <canvas id="departmentPerformanceChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Attendance Heatmap -->
        <div class="col-lg-8 mb-4">
            <div class="analytics-chart-card">
                <div class="chart-header">
                    <h5>Attendance Heatmap</h5>
                    <div class="chart-controls">
                        <select class="form-select form-select-sm" id="heatmapWorker" style="width: 200px;">
                            <option value="">All Workers</option>
                        </select>
                    </div>
                </div>
                <div class="chart-body">
                    <div id="attendanceHeatmap"></div>
                </div>
            </div>
        </div>
        
        <!-- Status Distribution -->
        <div class="col-lg-4 mb-4">
            <div class="analytics-chart-card">
                <div class="chart-header">
                    <h5>Status Distribution</h5>
                </div>
                <div class="chart-body">
                    <canvas id="statusDistributionChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Productivity Analysis -->
        <div class="col-lg-6 mb-4">
            <div class="analytics-chart-card">
                <div class="chart-header">
                    <h5>Productivity by Day of Week</h5>
                </div>
                <div class="chart-body">
                    <canvas id="productivityByDayChart"></canvas>
                </div>
            </div>
        </div>
        
        <!-- Emoji Usage Analytics -->
        <div class="col-lg-6 mb-4">
            <div class="analytics-chart-card">
                <div class="chart-header">
                    <h5>Popular Emoji Tags</h5>
                </div>
                <div class="chart-body">
                    <div id="emojiAnalytics"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Pattern Recognition Results -->
    <div class="row">
        <div class="col-12 mb-4">
            <div class="analytics-chart-card">
                <div class="chart-header">
                    <h5>Pattern Recognition & Insights</h5>
                    <button class="btn btn-outline-primary btn-sm" onclick="runPatternAnalysis()">
                        <i class="bi bi-search"></i> Run Analysis
                    </button>
                </div>
                <div class="chart-body">
                    <div id="patternInsights">
                        <div class="text-center py-4">
                            <i class="bi bi-lightbulb text-muted" style="font-size: 3rem;"></i>
                            <p class="text-muted mt-3">Click "Run Analysis" to discover attendance patterns and insights</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Top Performers Table -->
    <div class="row">
        <div class="col-lg-8 mb-4">
            <div class="analytics-chart-card">
                <div class="chart-header">
                    <h5>Top Performers</h5>
                    <div class="chart-controls">
                        <select class="form-select form-select-sm" id="performanceMetric" style="width: 180px;">
                            <option value="attendance_rate">Attendance Rate</option>
                            <option value="punctuality_rate">Punctuality Rate</option>
                            <option value="avg_productivity_score">Productivity Score</option>
                        </select>
                    </div>
                </div>
                <div class="chart-body">
                    <div class="table-responsive">
                        <table class="table table-hover" id="topPerformersTable">
                            <thead>
                                <tr>
                                    <th>Rank</th>
                                    <th>Employee</th>
                                    <th>Department</th>
                                    <th>Attendance Rate</th>
                                    <th>Punctuality Rate</th>
                                    <th>Productivity Score</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Data will be populated here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Quick Stats -->
        <div class="col-lg-4 mb-4">
            <div class="analytics-chart-card">
                <div class="chart-header">
                    <h5>Quick Statistics</h5>
                </div>
                <div class="chart-body">
                    <div class="quick-stats">
                        <div class="stat-item">
                            <div class="stat-label">Total Working Days</div>
                            <div class="stat-value" id="totalWorkingDays">-</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-label">Total Overtime Hours</div>
                            <div class="stat-value" id="totalOvertimeHours">-</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-label">Average Daily Attendance</div>
                            <div class="stat-value" id="avgDailyAttendance">-</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-label">Most Productive Day</div>
                            <div class="stat-value" id="mostProductiveDay">-</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-label">Peak Attendance Time</div>
                            <div class="stat-value" id="peakAttendanceTime">-</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Export Modal -->
<div class="modal fade" id="exportModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Export Analytics Report</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="exportForm">
                    <div class="mb-3">
                        <label class="form-label">Report Type</label>
                        <select class="form-select" id="exportReportType" required>
                            <option value="summary">Executive Summary</option>
                            <option value="detailed">Detailed Analytics</option>
                            <option value="department">Department Analysis</option>
                            <option value="worker">Individual Worker Report</option>
                        </select>
                    </div>
                    
                    <div class="mb-3" id="workerSelectDiv" style="display: none;">
                        <label class="form-label">Select Worker</label>
                        <select class="form-select" id="exportWorker">
                            <option value="">Select Worker</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Format</label>
                        <select class="form-select" id="exportFormat" required>
                            <option value="pdf">PDF Report</option>
                            <option value="excel">Excel Spreadsheet</option>
                            <option value="csv">CSV Data</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="includeCharts" checked>
                            <label class="form-check-label" for="includeCharts">
                                Include Charts and Visualizations
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="generateExport()">
                    <i class="bi bi-download"></i> Generate Report
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Include Chart.js and other required libraries -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns/dist/chartjs-adapter-date-fns.bundle.min.js"></script>
<script src="../../assets/js/attendance/analytics-charts.js"></script>
<script src="../../assets/js/attendance/analytics-heatmap.js"></script>
<script src="../../assets/js/attendance/analytics-patterns.js"></script>

<link rel="stylesheet" href="../../assets/css/attendance/analytics.css">

<?php include '../includes/footer.php'; ?>
