/**
 * Attendance Dashboard Styles
 * 
 * Modern dashboard styles with cards, metrics, charts, and responsive design
 * for the attendance management dashboard.
 * 
 * @package MtcInvoice Attendance
 * @version 1.0
 */

/* Dashboard Container */
.attendance-dashboard {
    background: #f8f9fa;
    min-height: 100vh;
    padding: 20px;
}

.dashboard-header {
    background: white;
    padding: 20px;
    border-radius: 12px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.dashboard-controls {
    display: flex;
    align-items: center;
}

.dashboard-content {
    /* Content styling handled by individual components */
}

/* Metric Cards */
.metric-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 15px;
    transition: all 0.3s ease;
    height: 100%;
}

.metric-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.metric-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
}

.metric-icon.bg-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.metric-icon.bg-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

.metric-icon.bg-warning {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
}

.metric-icon.bg-info {
    background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
}

.metric-content {
    flex: 1;
}

.metric-value {
    font-size: 28px;
    font-weight: 700;
    color: #495057;
    line-height: 1;
    margin-bottom: 5px;
}

.metric-label {
    font-size: 14px;
    color: #6c757d;
    font-weight: 500;
}

/* Chart Cards */
.chart-card,
.alerts-card,
.performers-card,
.emoji-stats-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.chart-header {
    padding: 20px 20px 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 15px;
    margin-bottom: 15px;
}

.chart-header h6 {
    margin: 0;
    font-weight: 600;
    color: #495057;
}

.chart-controls {
    display: flex;
    gap: 5px;
}

.trend-btn {
    font-size: 12px;
    padding: 5px 12px;
    border-radius: 20px;
}

.trend-btn.active {
    background-color: #667eea;
    border-color: #667eea;
    color: white;
}

.chart-body {
    padding: 0 20px 20px 20px;
    flex: 1;
    position: relative;
    min-height: 300px;
}

.chart-body canvas {
    max-height: 300px;
}

/* Alerts Card */
.alerts-list {
    padding: 0 20px 20px 20px;
    max-height: 350px;
    overflow-y: auto;
}

.alert-item {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 12px;
    border-radius: 8px;
    margin-bottom: 10px;
    transition: all 0.3s ease;
}

.alert-item:hover {
    background: #f8f9fa;
}

.alert-item.severity-critical {
    border-left: 4px solid #dc3545;
    background: rgba(220, 53, 69, 0.05);
}

.alert-item.severity-high {
    border-left: 4px solid #fd7e14;
    background: rgba(253, 126, 20, 0.05);
}

.alert-item.severity-medium {
    border-left: 4px solid #ffc107;
    background: rgba(255, 193, 7, 0.05);
}

.alert-item.severity-low {
    border-left: 4px solid #17a2b8;
    background: rgba(23, 162, 184, 0.05);
}

.alert-icon {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: rgba(220, 53, 69, 0.1);
    color: #dc3545;
    font-size: 12px;
    flex-shrink: 0;
}

.alert-content {
    flex: 1;
    min-width: 0;
}

.alert-message {
    font-size: 14px;
    color: #495057;
    margin-bottom: 5px;
    line-height: 1.4;
}

.alert-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
    color: #6c757d;
}

.worker-name {
    font-weight: 500;
}

.alert-time {
    font-style: italic;
}

.no-alerts,
.no-data,
.error {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
    font-style: italic;
}

/* Top Performers Card */
.performers-list {
    padding: 0 20px 20px 20px;
    max-height: 350px;
    overflow-y: auto;
}

.performer-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 10px;
    background: #f8f9fa;
    transition: all 0.3s ease;
}

.performer-item:hover {
    background: #e9ecef;
    transform: translateX(5px);
}

.performer-rank {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 14px;
    flex-shrink: 0;
}

.performer-info {
    flex: 1;
}

.performer-name {
    font-weight: 600;
    color: #495057;
    margin-bottom: 5px;
}

.performer-stats {
    display: flex;
    gap: 15px;
    font-size: 12px;
    color: #6c757d;
}

.attendance-rate {
    color: #28a745;
    font-weight: 500;
}

.productivity-score {
    color: #ffc107;
    font-weight: 500;
}

/* Emoji Stats Card */
.emoji-stats-list {
    padding: 0 20px 20px 20px;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 15px;
    max-height: 350px;
    overflow-y: auto;
}

.emoji-stat-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px;
    border-radius: 8px;
    background: #f8f9fa;
    transition: all 0.3s ease;
}

.emoji-stat-item:hover {
    background: #e9ecef;
    transform: scale(1.05);
}

.emoji-icon {
    font-size: 24px;
    flex-shrink: 0;
}

.emoji-info {
    flex: 1;
    min-width: 0;
}

.emoji-count {
    font-weight: 600;
    color: #495057;
    font-size: 16px;
    line-height: 1;
}

.emoji-productivity {
    font-size: 11px;
    color: #6c757d;
    margin-top: 2px;
}

/* Animations */
@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.spin {
    animation: spin 1s linear infinite;
}

/* Loading States */
.loading-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

/* Responsive Design */
@media (max-width: 768px) {
    .attendance-dashboard {
        padding: 15px;
    }
    
    .dashboard-header {
        padding: 15px;
    }
    
    .dashboard-controls {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }
    
    .metric-card {
        padding: 15px;
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }
    
    .metric-value {
        font-size: 24px;
    }
    
    .chart-header {
        padding: 15px 15px 0 15px;
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }
    
    .chart-controls {
        justify-content: center;
    }
    
    .chart-body {
        padding: 0 15px 15px 15px;
        min-height: 250px;
    }
    
    .chart-body canvas {
        max-height: 250px;
    }
    
    .alerts-list,
    .performers-list {
        padding: 0 15px 15px 15px;
        max-height: 300px;
    }
    
    .emoji-stats-list {
        padding: 0 15px 15px 15px;
        grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
        gap: 10px;
    }
    
    .performer-item {
        padding: 12px;
    }
    
    .performer-stats {
        flex-direction: column;
        gap: 5px;
    }
    
    .alert-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }
}

@media (max-width: 576px) {
    .attendance-dashboard {
        padding: 10px;
    }
    
    .dashboard-header {
        padding: 12px;
    }
    
    .metric-card {
        padding: 12px;
    }
    
    .metric-value {
        font-size: 20px;
    }
    
    .metric-label {
        font-size: 12px;
    }
    
    .chart-header h6 {
        font-size: 14px;
    }
    
    .trend-btn {
        font-size: 11px;
        padding: 4px 8px;
    }
    
    .alert-item,
    .performer-item {
        padding: 10px;
    }
    
    .alert-message {
        font-size: 13px;
    }
    
    .performer-name {
        font-size: 14px;
    }
    
    .performer-stats {
        font-size: 11px;
    }
    
    .emoji-stat-item {
        padding: 8px;
        flex-direction: column;
        text-align: center;
        gap: 5px;
    }
    
    .emoji-icon {
        font-size: 20px;
    }
    
    .emoji-count {
        font-size: 14px;
    }
    
    .emoji-productivity {
        font-size: 10px;
    }
}

/* Dark mode support (if needed) */
@media (prefers-color-scheme: dark) {
    .attendance-dashboard {
        background: #1a1a1a;
        color: #e9ecef;
    }
    
    .dashboard-header,
    .metric-card,
    .chart-card,
    .alerts-card,
    .performers-card,
    .emoji-stats-card {
        background: #2d3748;
        border-color: #4a5568;
    }
    
    .metric-value,
    .chart-header h6,
    .performer-name,
    .alert-message {
        color: #e9ecef;
    }
    
    .metric-label,
    .alert-meta,
    .performer-stats,
    .emoji-productivity {
        color: #a0aec0;
    }
    
    .performer-item,
    .emoji-stat-item {
        background: #4a5568;
    }
    
    .performer-item:hover,
    .emoji-stat-item:hover {
        background: #5a6578;
    }
    
    .alert-item:hover {
        background: #4a5568;
    }
}
