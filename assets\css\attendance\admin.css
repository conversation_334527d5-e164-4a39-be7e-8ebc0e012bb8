/**
 * Attendance Admin Interface Styles
 * 
 * Styles for the attendance management admin interface
 * including dashboard, tables, forms, and responsive design.
 * 
 * @package MtcInvoice Attendance
 * @version 1.0
 */

/* Page Header */
.page-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 30px;
    border-radius: 12px;
    margin-bottom: 30px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.page-title {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 5px;
}

.page-description {
    font-size: 1.1rem;
    opacity: 0.9;
    margin-bottom: 0;
}

.page-actions {
    display: flex;
    gap: 10px;
    align-items: center;
}

.page-actions .btn {
    border-color: rgba(255, 255, 255, 0.3);
    color: white;
}

.page-actions .btn-primary {
    background-color: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
}

.page-actions .btn:hover {
    background-color: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.7);
}

/* Navigation Tabs */
.card-header-tabs {
    border-bottom: none;
    margin-bottom: 0;
}

.card-header-tabs .nav-link {
    border: none;
    color: #6c757d;
    font-weight: 500;
    padding: 12px 20px;
    border-radius: 8px 8px 0 0;
    transition: all 0.3s ease;
}

.card-header-tabs .nav-link:hover {
    background-color: #f8f9fa;
    color: #495057;
}

.card-header-tabs .nav-link.active {
    background-color: white;
    color: #667eea;
    border-bottom: 3px solid #667eea;
}

.card-header-tabs .nav-link i {
    margin-right: 8px;
}

.card-header-tabs .badge {
    margin-left: 5px;
}

/* Workers Management */
.workers-management {
    padding: 20px 0;
}

.workers-actions {
    display: flex;
    align-items: center;
    gap: 15px;
}

.workers-actions .input-group {
    flex-shrink: 0;
}

/* Workers Table */
#workersTable {
    margin-top: 20px;
}

#workersTable th {
    background-color: #f8f9fa;
    border-top: none;
    font-weight: 600;
    color: #495057;
    padding: 15px 12px;
}

#workersTable td {
    padding: 15px 12px;
    vertical-align: middle;
}

.worker-avatar .avatar-circle {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 14px;
}

.attendance-rate .badge {
    font-size: 12px;
    padding: 6px 10px;
}

/* Reports Section */
.reports-section {
    padding: 20px 0;
}

.report-card {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    padding: 20px;
    text-align: center;
    transition: all 0.3s ease;
    height: 100%;
}

.report-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    border-color: #667eea;
}

.report-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    margin: 0 auto 15px;
}

.report-content h6 {
    font-weight: 600;
    color: #495057;
    margin-bottom: 10px;
}

.report-content p {
    color: #6c757d;
    font-size: 14px;
    margin-bottom: 15px;
}

.custom-report-section {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 25px;
    border: 1px solid #e9ecef;
}

.custom-report-section h5 {
    color: #495057;
    font-weight: 600;
    margin-bottom: 20px;
}

/* Alerts Section */
.alerts-section {
    padding: 20px 0;
}

.alerts-actions {
    display: flex;
    gap: 10px;
}

.alerts-filters {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
}

.alert-card {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    margin-bottom: 15px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.alert-card:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.alert-card.severity-critical {
    border-left: 4px solid #dc3545;
}

.alert-card.severity-high {
    border-left: 4px solid #fd7e14;
}

.alert-card.severity-medium {
    border-left: 4px solid #ffc107;
}

.alert-card.severity-low {
    border-left: 4px solid #17a2b8;
}

.alert-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px 10px;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.alert-severity .badge {
    font-size: 11px;
    padding: 4px 8px;
}

.alert-body {
    padding: 15px 20px;
}

.alert-worker {
    font-size: 16px;
    color: #495057;
    margin-bottom: 8px;
}

.alert-message {
    color: #6c757d;
    margin-bottom: 10px;
    line-height: 1.5;
}

.alert-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* Settings Section */
.settings-section {
    padding: 20px 0;
}

.settings-section h5 {
    color: #495057;
    font-weight: 600;
    margin-bottom: 25px;
}

.settings-section .form-label {
    font-weight: 500;
    color: #495057;
}

.settings-section .form-control,
.settings-section .form-select {
    border-radius: 8px;
    border: 1px solid #ced4da;
    padding: 10px 15px;
}

.settings-section .form-control:focus,
.settings-section .form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.form-check-input:checked {
    background-color: #667eea;
    border-color: #667eea;
}

/* Modal Enhancements */
.modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-bottom: none;
}

.modal-header .btn-close {
    filter: invert(1);
}

.modal-body {
    padding: 25px;
}

.modal-body .form-label {
    font-weight: 500;
    color: #495057;
}

.modal-body .form-control,
.modal-body .form-select {
    border-radius: 8px;
    border: 1px solid #ced4da;
    padding: 10px 15px;
}

.modal-body .form-control:focus,
.modal-body .form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* Button Enhancements */
.btn-group-sm .btn {
    padding: 4px 8px;
    font-size: 12px;
}

.btn-outline-primary:hover {
    background-color: #667eea;
    border-color: #667eea;
}

.btn-outline-info:hover {
    background-color: #17a2b8;
    border-color: #17a2b8;
}

.btn-outline-danger:hover {
    background-color: #dc3545;
    border-color: #dc3545;
}

.btn-outline-success:hover {
    background-color: #28a745;
    border-color: #28a745;
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* PWA Styles */
.pwa-install-banner,
.pwa-update-banner,
.pwa-notification-banner {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
    border: 1px solid #e9ecef;
    z-index: 9999;
    max-width: 350px;
    transform: translateX(400px);
    transition: transform 0.3s ease;
}

.pwa-install-banner.show,
.pwa-update-banner.show,
.pwa-notification-banner.show {
    transform: translateX(0);
}

.install-banner-content,
.update-banner-content,
.notification-banner-content {
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 15px;
}

.install-banner-icon,
.update-banner-icon,
.notification-banner-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 18px;
    flex-shrink: 0;
}

.install-banner-text,
.update-banner-text,
.notification-banner-text {
    flex: 1;
}

.install-banner-text h6,
.update-banner-text h6,
.notification-banner-text h6 {
    margin: 0 0 5px 0;
    font-weight: 600;
    color: #495057;
}

.install-banner-text p,
.update-banner-text p,
.notification-banner-text p {
    margin: 0;
    font-size: 14px;
    color: #6c757d;
}

.install-banner-actions,
.update-banner-actions,
.notification-banner-actions {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.pwa-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    border-left: 4px solid #667eea;
    z-index: 9999;
    max-width: 400px;
    transform: translateX(450px);
    transition: transform 0.3s ease;
}

.pwa-notification.show {
    transform: translateX(0);
}

.pwa-notification-success {
    border-left-color: #28a745;
}

.pwa-notification-warning {
    border-left-color: #ffc107;
}

.pwa-notification-error {
    border-left-color: #dc3545;
}

.notification-content {
    padding: 15px 20px;
    display: flex;
    align-items: center;
    gap: 12px;
}

.notification-icon {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #667eea;
    flex-shrink: 0;
}

.pwa-notification-success .notification-icon {
    color: #28a745;
}

.pwa-notification-warning .notification-icon {
    color: #ffc107;
}

.pwa-notification-error .notification-icon {
    color: #dc3545;
}

.notification-message {
    flex: 1;
    font-size: 14px;
    color: #495057;
    line-height: 1.4;
}

.notification-close {
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.2s ease;
}

.notification-close:hover {
    background-color: #f8f9fa;
}

/* PWA Install Button in Header */
.pwa-install-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: none;
}

.pwa-install-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(102, 126, 234, 0.3);
}

.pwa-install-btn.show {
    display: inline-block;
}

/* Offline Indicator */
.offline-indicator {
    position: fixed;
    bottom: 20px;
    left: 20px;
    background: #dc3545;
    color: white;
    padding: 10px 15px;
    border-radius: 25px;
    font-size: 12px;
    font-weight: 500;
    z-index: 9998;
    display: none;
    align-items: center;
    gap: 8px;
}

.offline-indicator.show {
    display: flex;
}

.offline-indicator .pulse-dot {
    width: 8px;
    height: 8px;
    background: white;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* PWA Splash Screen Styles */
.pwa-splash {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 99999;
    color: white;
    text-align: center;
}

.pwa-splash-logo {
    width: 120px;
    height: 120px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 48px;
    margin-bottom: 30px;
    animation: fadeInUp 0.8s ease;
}

.pwa-splash-title {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 10px;
    animation: fadeInUp 0.8s ease 0.2s both;
}

.pwa-splash-subtitle {
    font-size: 16px;
    opacity: 0.8;
    animation: fadeInUp 0.8s ease 0.4s both;
}

.pwa-splash-loader {
    margin-top: 40px;
    width: 40px;
    height: 40px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-top: 3px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite, fadeInUp 0.8s ease 0.6s both;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .page-header {
        padding: 20px;
        text-align: center;
    }
    
    .page-title {
        font-size: 1.5rem;
    }
    
    .page-description {
        font-size: 1rem;
    }
    
    .page-actions {
        flex-direction: column;
        margin-top: 15px;
        width: 100%;
    }
    
    .page-actions .btn {
        width: 100%;
    }
    
    .workers-actions {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }
    
    .workers-actions .input-group {
        width: 100%;
    }
    
    .alerts-actions {
        flex-direction: column;
        gap: 10px;
    }
    
    .alerts-filters .row {
        gap: 10px;
    }
    
    .alert-header {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }
    
    .alert-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }
    
    .custom-report-section .row {
        gap: 15px;
    }
    
    .custom-report-section .col-md-3 {
        margin-bottom: 15px;
    }
}

@media (max-width: 576px) {
    .page-header {
        padding: 15px;
    }
    
    .card-header-tabs .nav-link {
        padding: 10px 12px;
        font-size: 14px;
    }
    
    .card-header-tabs .nav-link i {
        margin-right: 5px;
    }
    
    .workers-management,
    .reports-section,
    .alerts-section,
    .settings-section {
        padding: 15px 0;
    }
    
    #workersTable th,
    #workersTable td {
        padding: 10px 8px;
        font-size: 14px;
    }
    
    .worker-avatar .avatar-circle {
        width: 28px;
        height: 28px;
        font-size: 12px;
    }
    
    .report-card {
        padding: 15px;
    }
    
    .report-icon {
        width: 50px;
        height: 50px;
        font-size: 20px;
        margin-bottom: 12px;
    }
    
    .alert-card {
        margin-bottom: 10px;
    }
    
    .alert-header,
    .alert-body {
        padding: 12px 15px;
    }
    
    .custom-report-section {
        padding: 20px;
    }
    
    .modal-body {
        padding: 20px;
    }
}

/* Table Responsive Enhancements */
@media (max-width: 992px) {
    .table-responsive {
        border-radius: 8px;
    }
    
    #workersTable {
        font-size: 14px;
    }
    
    .btn-group-sm .btn {
        padding: 3px 6px;
        font-size: 11px;
    }
}

/* Print Styles */
@media print {
    .page-actions,
    .workers-actions,
    .alerts-actions,
    .btn,
    .modal {
        display: none !important;
    }
    
    .page-header {
        background: none !important;
        color: black !important;
        box-shadow: none !important;
    }
    
    .card {
        border: none !important;
        box-shadow: none !important;
    }
    
    .alert-card {
        border: 1px solid #ccc !important;
        box-shadow: none !important;
    }
}
