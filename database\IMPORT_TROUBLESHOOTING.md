# Database Import Troubleshooting Guide

## Common SQL Import Issues and Solutions

### Issue 1: Table 'workers' doesn't exist (Error #1146)

**Problem:** You're trying to run an ALTER TABLE statement on a table that doesn't exist yet.

**Solution:** Use the clean import file instead:

1. **Use the Clean Import File:**
   ```
   database/attendance_clean_import.sql
   ```
   This file creates all tables from scratch without dependencies.

2. **Or use the Web Import Tool:**
   ```
   http://your-domain/MtcInvoiceNewProject/admin/attendance/import_database.php
   ```

### Issue 2: Foreign Key Constraint Errors

**Problem:** Foreign key references fail because referenced tables don't exist.

**Solution:** The clean import file creates tables in the correct order:
1. departments
2. workers  
3. attendance_records
4. attendance_alerts
5. Foreign key constraints (added last)

### Issue 3: JSON Column Type Not Supported

**Problem:** Older MySQL versions don't support JSON columns.

**Solution:** 
- **MySQL 5.7+:** JSON is supported
- **MySQL 5.6 or older:** Replace JSON with TEXT in the schema

**Quick Fix for Older MySQL:**
```sql
-- Replace this:
`work_schedule` JSON NULL COMMENT 'Flexible work schedules',

-- With this:
`work_schedule` TEXT NULL COMMENT 'Flexible work schedules',
```

### Issue 4: Duplicate Entry Errors

**Problem:** Trying to insert data that already exists.

**Solution:** Use the "Safe Import" mode in the web tool, or add `IGNORE` to INSERT statements:
```sql
INSERT IGNORE INTO `workers` ...
```

## Import Methods

### Method 1: Web-Based Import (Recommended)

1. Navigate to: `admin/attendance/import_database.php`
2. Choose "Safe Import" to preserve existing data
3. Choose "Fresh Import" to start clean (⚠️ deletes existing data)
4. Click "Start Import"

### Method 2: phpMyAdmin Import

1. Open phpMyAdmin
2. Select your database
3. Go to "Import" tab
4. Choose file: `database/attendance_clean_import.sql`
5. Click "Go"

### Method 3: Command Line Import

```bash
mysql -u username -p database_name < database/attendance_clean_import.sql
```

### Method 4: Manual Table Creation

If all else fails, create tables manually:

```sql
-- 1. Create departments table first
CREATE TABLE `departments` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `name` varchar(100) NOT NULL,
    `description` text,
    PRIMARY KEY (`id`)
);

-- 2. Create workers table
CREATE TABLE `workers` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `employee_id` varchar(50) NOT NULL,
    `name` varchar(255) NOT NULL,
    `email` varchar(255) DEFAULT NULL,
    `department` varchar(100) NOT NULL,
    `position` varchar(100) NOT NULL,
    `status` enum('active','inactive','terminated') DEFAULT 'active',
    PRIMARY KEY (`id`),
    UNIQUE KEY `employee_id` (`employee_id`)
);

-- 3. Continue with other tables...
```

## Verification Steps

After import, verify the installation:

1. **Check Tables Exist:**
   ```sql
   SHOW TABLES LIKE 'workers';
   SHOW TABLES LIKE 'attendance_records';
   ```

2. **Check Sample Data:**
   ```sql
   SELECT COUNT(*) FROM workers;
   SELECT COUNT(*) FROM departments;
   ```

3. **Test Web Interface:**
   - Visit: `admin/attendance/test_db.php`
   - Should show "Database Connection: SUCCESS"

## Database Requirements

- **MySQL Version:** 5.7+ (recommended) or 5.6+ (with modifications)
- **PHP Version:** 7.4+ or 8.0+
- **Required Extensions:** PDO, PDO_MySQL
- **Permissions:** CREATE, ALTER, INSERT, SELECT, UPDATE, DELETE

## File Locations

- **Clean Import:** `database/attendance_clean_import.sql`
- **Original Schema:** `database/attendance_schema.sql`
- **Web Import Tool:** `admin/attendance/import_database.php`
- **Database Test:** `admin/attendance/test_db.php`

## Common Error Messages and Solutions

### Error: "Unknown column 'user_id' in 'field list'"
**Solution:** The table structure is outdated. Use the clean import file.

### Error: "Table 'workers' already exists"
**Solution:** Use "Safe Import" mode or add `IF NOT EXISTS` to CREATE statements.

### Error: "Cannot add foreign key constraint"
**Solution:** Ensure referenced tables exist first. The clean import handles this automatically.

### Error: "Access denied for user"
**Solution:** Check database credentials in `admin/config/config.php`.

## Support

If you continue to have issues:

1. Check the error log: `admin/attendance/test_db.php`
2. Verify database connection: `admin/attendance/test_api.php`
3. Use the simple test page: `admin/attendance/simple_test.php`

## Quick Start Commands

```bash
# 1. Navigate to project directory
cd /path/to/MtcInvoiceNewProject

# 2. Import database
mysql -u username -p database_name < database/attendance_clean_import.sql

# 3. Test the installation
# Visit: http://your-domain/MtcInvoiceNewProject/admin/attendance/
```

## Success Indicators

✅ **Import Successful When:**
- All tables created without errors
- Sample data inserted (5 workers, 6 departments)
- Web interface loads without errors
- Test pages show "SUCCESS" status

❌ **Import Failed When:**
- Error messages during import
- Tables missing or empty
- Web interface shows database errors
- Test pages show "FAILED" status
