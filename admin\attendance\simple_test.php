<?php
/**
 * Simple Attendance Test Page
 */

require_once '../config/config.php';

$page_title = 'Attendance Test';
$current_page = 'attendance';

include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5>Attendance System Test</h5>
                </div>
                <div class="card-body">
                    <h6>Configuration Test</h6>
                    <ul>
                        <li>Admin URL: <?= ADMIN_URL ?></li>
                        <li>API Base URL: <?= API_BASE_URL ?></li>
                        <li>Base URL: <?= BASE_URL ?></li>
                        <li>Current User: <?= getCurrentUser()['name'] ?? 'Not logged in' ?></li>
                        <li>Session Status: <?= session_status() === PHP_SESSION_ACTIVE ? 'Active' : 'Inactive' ?></li>
                    </ul>
                    
                    <h6>Database Test</h6>
                    <div id="db-test">Testing database connection...</div>
                    
                    <h6>API Test</h6>
                    <div id="api-test">Testing API endpoints...</div>
                    
                    <h6>File Test</h6>
                    <div id="file-test">
                        <?php
                        $files = [
                            BASE_URL . 'assets/js/attendance/calendar.js',
                            BASE_URL . 'assets/js/attendance/dashboard.js',
                            BASE_URL . 'assets/css/attendance/calendar.css'
                        ];
                        
                        foreach ($files as $file) {
                            echo "<p>File: $file</p>";
                        }
                        ?>
                    </div>
                    
                    <div class="mt-3">
                        <a href="index.php" class="btn btn-primary">Go to Full Attendance Dashboard</a>
                        <a href="import_database.php" class="btn btn-success">Import Database (Recommended)</a>
                        <a href="setup_db.php" class="btn btn-warning">Setup Database (Legacy)</a>
                        <a href="test_db.php" class="btn btn-info">Test Database</a>
                        <a href="test_api.php" class="btn btn-secondary">Test API</a>
                    </div>

                    <div class="mt-3">
                        <div class="alert alert-info">
                            <h6><i class="bi bi-info-circle"></i> Having SQL Import Issues?</h6>
                            <p class="mb-2">If you're getting "Table doesn't exist" errors, use the <strong>Import Database</strong> tool above.</p>
                            <p class="mb-0">It handles table creation in the correct order and avoids dependency issues.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Test database connection
fetch('test_db.php')
    .then(response => response.text())
    .then(data => {
        document.getElementById('db-test').innerHTML = data.includes('SUCCESS') ? 
            '<span style="color: green;">✓ Database connection successful</span>' : 
            '<span style="color: red;">✗ Database connection failed</span>';
    })
    .catch(error => {
        document.getElementById('db-test').innerHTML = '<span style="color: red;">✗ Error testing database</span>';
    });

// Test API endpoints
fetch('<?= API_BASE_URL ?>attendance/workers.php')
    .then(response => {
        document.getElementById('api-test').innerHTML = response.ok ? 
            '<span style="color: green;">✓ API endpoints accessible</span>' : 
            '<span style="color: red;">✗ API endpoints not accessible</span>';
    })
    .catch(error => {
        document.getElementById('api-test').innerHTML = '<span style="color: red;">✗ Error accessing API</span>';
    });
</script>

<?php include '../includes/footer.php'; ?>
