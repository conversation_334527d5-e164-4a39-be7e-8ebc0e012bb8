/**
 * Attendance Timeline Calendar
 * 
 * Interactive calendar with drag-and-drop functionality, emoji status indicators,
 * and smooth navigation for attendance management.
 * 
 * @package MtcInvoice Attendance
 * @version 1.0
 */

class AttendanceCalendar {
    constructor(containerId, options = {}) {
        this.container = document.getElementById(containerId);
        this.options = {
            apiBaseUrl: '/MtcInvoiceNewProject/api/attendance',
            currentDate: new Date(),
            viewMode: 'month', // month, week, day
            enableDragDrop: true,
            enableEmojis: true,
            autoRefresh: false,
            refreshInterval: 30000, // 30 seconds
            ...options
        };
        
        this.workers = [];
        this.attendanceData = [];
        this.holidays = [];
        this.selectedCells = [];
        this.draggedStatus = null;
        
        this.statusConfig = {
            present: { emoji: '✅', color: '#28a745', label: 'Present' },
            absent: { emoji: '❌', color: '#dc3545', label: 'Absent' },
            late: { emoji: '🟡', color: '#ffc107', label: 'Late' },
            half_day: { emoji: '🕒', color: '#17a2b8', label: 'Half Day' },
            holiday: { emoji: '🏖️', color: '#6f42c1', label: 'Holiday' },
            sick_leave: { emoji: '😷', color: '#fd7e14', label: 'Sick Leave' },
            vacation: { emoji: '🏝️', color: '#20c997', label: 'Vacation' },
            remote_work: { emoji: '🏠', color: '#6c757d', label: 'Remote Work' },
            business_trip: { emoji: '✈️', color: '#495057', label: 'Business Trip' }
        };
        
        this.init();
    }
    
    async init() {
        try {
            this.createCalendarStructure();
            this.setupEventListeners();
            await this.loadInitialData();
            this.renderCalendar();
            
            if (this.options.autoRefresh) {
                this.startAutoRefresh();
            }
        } catch (error) {
            console.error('Failed to initialize attendance calendar:', error);
            this.showError('Failed to initialize calendar');
        }
    }
    
    createCalendarStructure() {
        this.container.innerHTML = `
            <div class="attendance-calendar">
                <div class="calendar-header">
                    <div class="calendar-controls">
                        <div class="view-controls">
                            <button class="btn btn-sm btn-outline-primary view-btn" data-view="day">Day</button>
                            <button class="btn btn-sm btn-primary view-btn" data-view="week">Week</button>
                            <button class="btn btn-sm btn-outline-primary view-btn" data-view="month">Month</button>
                        </div>
                        <div class="navigation-controls">
                            <button class="btn btn-sm btn-outline-secondary" id="prevPeriod">
                                <i class="bi bi-chevron-left"></i>
                            </button>
                            <h5 class="current-period mb-0"></h5>
                            <button class="btn btn-sm btn-outline-secondary" id="nextPeriod">
                                <i class="bi bi-chevron-right"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-info" id="todayBtn">Today</button>
                        </div>
                        <div class="action-controls">
                            <button class="btn btn-sm btn-success" id="refreshBtn">
                                <i class="bi bi-arrow-clockwise"></i> Refresh
                            </button>
                            <div class="dropdown">
                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                    <i class="bi bi-funnel"></i> Filter
                                </button>
                                <div class="dropdown-menu">
                                    <div class="px-3 py-2">
                                        <label class="form-label">Department</label>
                                        <select class="form-select form-select-sm" id="departmentFilter">
                                            <option value="">All Departments</option>
                                        </select>
                                    </div>
                                    <div class="px-3 py-2">
                                        <label class="form-label">Status</label>
                                        <select class="form-select form-select-sm" id="statusFilter">
                                            <option value="">All Status</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="status-palette" style="display: none;">
                        <div class="palette-header">
                            <span>Drag status to calendar cells</span>
                            <button class="btn btn-sm btn-outline-secondary" id="closePalette">×</button>
                        </div>
                        <div class="status-items">
                            ${Object.entries(this.statusConfig).map(([status, config]) => `
                                <div class="status-item" data-status="${status}" draggable="true">
                                    <span class="status-emoji">${config.emoji}</span>
                                    <span class="status-label">${config.label}</span>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                </div>
                
                <div class="calendar-body">
                    <div class="workers-sidebar">
                        <div class="sidebar-header">
                            <h6>Workers</h6>
                            <div class="search-box">
                                <input type="text" class="form-control form-control-sm" placeholder="Search workers..." id="workerSearch">
                            </div>
                        </div>
                        <div class="workers-list"></div>
                    </div>
                    
                    <div class="calendar-grid-container">
                        <div class="calendar-grid">
                            <div class="grid-header"></div>
                            <div class="grid-body"></div>
                        </div>
                    </div>
                </div>
                
                <div class="calendar-footer">
                    <div class="legend">
                        ${Object.entries(this.statusConfig).map(([status, config]) => `
                            <span class="legend-item">
                                <span class="legend-emoji">${config.emoji}</span>
                                <span class="legend-label">${config.label}</span>
                            </span>
                        `).join('')}
                    </div>
                </div>
            </div>
            
            <!-- Quick Edit Modal -->
            <div class="modal fade" id="quickEditModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">Edit Attendance</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="quickEditForm">
                                <input type="hidden" id="editWorkerId">
                                <input type="hidden" id="editDate">
                                
                                <div class="mb-3">
                                    <label class="form-label">Worker</label>
                                    <input type="text" class="form-control" id="editWorkerName" readonly>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">Date</label>
                                    <input type="date" class="form-control" id="editDateInput">
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">Status</label>
                                    <select class="form-select" id="editStatus" required>
                                        ${Object.entries(this.statusConfig).map(([status, config]) => `
                                            <option value="${status}">${config.emoji} ${config.label}</option>
                                        `).join('')}
                                    </select>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <label class="form-label">Check In</label>
                                        <input type="time" class="form-control" id="editCheckIn">
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">Check Out</label>
                                        <input type="time" class="form-control" id="editCheckOut">
                                    </div>
                                </div>
                                
                                <div class="mb-3 mt-3">
                                    <label class="form-label">Break Duration (minutes)</label>
                                    <input type="number" class="form-control" id="editBreakDuration" min="0" max="480">
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">Emoji Tags</label>
                                    <div class="emoji-selector">
                                        <div class="emoji-categories">
                                            <button type="button" class="btn btn-sm btn-outline-primary emoji-cat-btn active" data-category="work">Work</button>
                                            <button type="button" class="btn btn-sm btn-outline-primary emoji-cat-btn" data-category="mood">Mood</button>
                                            <button type="button" class="btn btn-sm btn-outline-primary emoji-cat-btn" data-category="reason">Reason</button>
                                        </div>
                                        <div class="emoji-grid" id="emojiGrid">
                                            <!-- Emoji options will be populated here -->
                                        </div>
                                        <input type="hidden" id="selectedEmojis">
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">Notes</label>
                                    <textarea class="form-control" id="editNotes" rows="3"></textarea>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">Productivity Score (1-5)</label>
                                    <select class="form-select" id="editProductivity">
                                        <option value="">Not rated</option>
                                        <option value="1">1 - Poor</option>
                                        <option value="2">2 - Below Average</option>
                                        <option value="3">3 - Average</option>
                                        <option value="4">4 - Good</option>
                                        <option value="5">5 - Excellent</option>
                                    </select>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                            <button type="button" class="btn btn-primary" id="saveAttendance">Save</button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // Initialize emoji categories
        this.emojiCategories = {
            work: ['😊', '💻', '🎯', '📈', '🚧', '⏰', '🏢', '🔧', '📋', '💡'],
            mood: ['😊', '😐', '😔', '😴', '🤒', '😷', '🤗', '😎', '🤔', '😤'],
            reason: ['🚗', '🚌', '⏰', '👨‍👩‍👧‍👦', '🏥', '📞', '✈️', '🏠', '☕', '🌧️']
        };
    }
    
    setupEventListeners() {
        // View mode buttons
        this.container.querySelectorAll('.view-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const view = e.target.dataset.view;
                this.changeViewMode(view);
            });
        });
        
        // Navigation buttons
        document.getElementById('prevPeriod').addEventListener('click', () => this.navigatePeriod(-1));
        document.getElementById('nextPeriod').addEventListener('click', () => this.navigatePeriod(1));
        document.getElementById('todayBtn').addEventListener('click', () => this.goToToday());
        document.getElementById('refreshBtn').addEventListener('click', () => this.refresh());
        
        // Filter controls
        document.getElementById('departmentFilter').addEventListener('change', () => this.applyFilters());
        document.getElementById('statusFilter').addEventListener('change', () => this.applyFilters());
        
        // Worker search
        document.getElementById('workerSearch').addEventListener('input', (e) => this.filterWorkers(e.target.value));
        
        // Drag and drop for status palette
        if (this.options.enableDragDrop) {
            this.setupDragAndDrop();
        }
        
        // Quick edit modal
        document.getElementById('saveAttendance').addEventListener('click', () => this.saveAttendanceRecord());
        
        // Emoji selector
        this.setupEmojiSelector();
    }
    
    async loadInitialData() {
        try {
            // Load workers
            const workersResponse = await fetch(`${this.options.apiBaseUrl}/workers.php`);
            const workersData = await workersResponse.json();
            this.workers = workersData.data.workers || [];
            
            // Load holidays
            const holidaysResponse = await fetch(`${this.options.apiBaseUrl}/holidays.php`);
            const holidaysData = await holidaysResponse.json();
            this.holidays = holidaysData.data || [];
            
            // Load attendance data for current period
            await this.loadAttendanceData();
            
            // Populate filter dropdowns
            this.populateFilters();
            
        } catch (error) {
            console.error('Failed to load initial data:', error);
            throw error;
        }
    }
    
    async loadAttendanceData() {
        try {
            const { startDate, endDate } = this.getCurrentPeriodDates();
            
            const params = new URLSearchParams({
                start_date: startDate,
                end_date: endDate,
                limit: 1000
            });
            
            // Apply filters
            const departmentFilter = document.getElementById('departmentFilter')?.value;
            const statusFilter = document.getElementById('statusFilter')?.value;
            
            if (departmentFilter) params.append('department', departmentFilter);
            if (statusFilter) params.append('status', statusFilter);
            
            const response = await fetch(`${this.options.apiBaseUrl}/records.php/timeline?${params}`);
            const data = await response.json();
            
            if (data.success) {
                this.attendanceData = data.data.timeline_data || [];
            } else {
                throw new Error(data.message || 'Failed to load attendance data');
            }
            
        } catch (error) {
            console.error('Failed to load attendance data:', error);
            this.attendanceData = [];
        }
    }
    
    getCurrentPeriodDates() {
        const current = new Date(this.options.currentDate);
        let startDate, endDate;
        
        switch (this.options.viewMode) {
            case 'day':
                startDate = endDate = current.toISOString().split('T')[0];
                break;
                
            case 'week':
                const startOfWeek = new Date(current);
                startOfWeek.setDate(current.getDate() - current.getDay());
                const endOfWeek = new Date(startOfWeek);
                endOfWeek.setDate(startOfWeek.getDate() + 6);
                
                startDate = startOfWeek.toISOString().split('T')[0];
                endDate = endOfWeek.toISOString().split('T')[0];
                break;
                
            case 'month':
            default:
                startDate = new Date(current.getFullYear(), current.getMonth(), 1).toISOString().split('T')[0];
                endDate = new Date(current.getFullYear(), current.getMonth() + 1, 0).toISOString().split('T')[0];
                break;
        }
        
        return { startDate, endDate };
    }
    
    renderCalendar() {
        this.updatePeriodHeader();
        this.renderWorkersList();
        this.renderCalendarGrid();
    }
    
    updatePeriodHeader() {
        const current = new Date(this.options.currentDate);
        let periodText;
        
        switch (this.options.viewMode) {
            case 'day':
                periodText = current.toLocaleDateString('en-US', { 
                    weekday: 'long', 
                    year: 'numeric', 
                    month: 'long', 
                    day: 'numeric' 
                });
                break;
                
            case 'week':
                const startOfWeek = new Date(current);
                startOfWeek.setDate(current.getDate() - current.getDay());
                const endOfWeek = new Date(startOfWeek);
                endOfWeek.setDate(startOfWeek.getDate() + 6);
                
                periodText = `${startOfWeek.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })} - ${endOfWeek.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' })}`;
                break;
                
            case 'month':
            default:
                periodText = current.toLocaleDateString('en-US', { year: 'numeric', month: 'long' });
                break;
        }
        
        this.container.querySelector('.current-period').textContent = periodText;
    }
    
    renderWorkersList() {
        const workersList = this.container.querySelector('.workers-list');
        const searchTerm = document.getElementById('workerSearch').value.toLowerCase();
        
        let filteredWorkers = this.workers;
        if (searchTerm) {
            filteredWorkers = this.workers.filter(worker => 
                worker.name.toLowerCase().includes(searchTerm) ||
                worker.employee_id.toLowerCase().includes(searchTerm) ||
                worker.department.toLowerCase().includes(searchTerm)
            );
        }
        
        workersList.innerHTML = filteredWorkers.map(worker => `
            <div class="worker-item" data-worker-id="${worker.id}">
                <div class="worker-avatar">
                    ${worker.profile_image ? 
                        `<img src="${worker.profile_image}" alt="${worker.name}">` :
                        `<div class="avatar-circle">${worker.name.charAt(0)}</div>`
                    }
                </div>
                <div class="worker-info">
                    <div class="worker-name">${worker.name}</div>
                    <div class="worker-details">
                        <small class="text-muted">${worker.employee_id} • ${worker.department}</small>
                    </div>
                </div>
            </div>
        `).join('');
    }
    
    renderCalendarGrid() {
        const { startDate, endDate } = this.getCurrentPeriodDates();
        const gridHeader = this.container.querySelector('.grid-header');
        const gridBody = this.container.querySelector('.grid-body');

        // Generate date columns
        const dates = this.generateDateRange(startDate, endDate);

        // Render header
        gridHeader.innerHTML = `
            <div class="grid-cell header-cell worker-column">Worker</div>
            ${dates.map(date => `
                <div class="grid-cell header-cell date-column" data-date="${date.toISOString().split('T')[0]}">
                    <div class="date-label">
                        <div class="day-name">${date.toLocaleDateString('en-US', { weekday: 'short' })}</div>
                        <div class="day-number">${date.getDate()}</div>
                    </div>
                </div>
            `).join('')}
        `;

        // Render body
        const searchTerm = document.getElementById('workerSearch').value.toLowerCase();
        let filteredWorkers = this.workers;
        if (searchTerm) {
            filteredWorkers = this.workers.filter(worker =>
                worker.name.toLowerCase().includes(searchTerm) ||
                worker.employee_id.toLowerCase().includes(searchTerm) ||
                worker.department.toLowerCase().includes(searchTerm)
            );
        }

        gridBody.innerHTML = filteredWorkers.map(worker => `
            <div class="grid-row" data-worker-id="${worker.id}">
                <div class="grid-cell worker-cell">
                    <div class="worker-info">
                        <div class="worker-name">${worker.name}</div>
                        <small class="text-muted">${worker.employee_id}</small>
                    </div>
                </div>
                ${dates.map(date => {
                    const dateStr = date.toISOString().split('T')[0];
                    const attendance = this.getAttendanceForWorkerAndDate(worker.id, dateStr);
                    const isHoliday = this.isHoliday(dateStr);

                    return `
                        <div class="grid-cell attendance-cell ${isHoliday ? 'holiday-cell' : ''}"
                             data-worker-id="${worker.id}"
                             data-date="${dateStr}"
                             data-attendance-id="${attendance?.id || ''}"
                             title="${this.getCellTooltip(worker, dateStr, attendance, isHoliday)}">
                            ${this.renderAttendanceCell(attendance, isHoliday)}
                        </div>
                    `;
                }).join('')}
            </div>
        `).join('');

        // Setup cell click handlers
        this.setupCellHandlers();
    }

    generateDateRange(startDate, endDate) {
        const dates = [];
        const current = new Date(startDate);
        const end = new Date(endDate);

        while (current <= end) {
            dates.push(new Date(current));
            current.setDate(current.getDate() + 1);
        }

        return dates;
    }

    getAttendanceForWorkerAndDate(workerId, date) {
        return this.attendanceData.find(record =>
            record.worker_id == workerId && record.date === date
        );
    }

    isHoliday(date) {
        return this.holidays.some(holiday => holiday.holiday_date === date);
    }

    renderAttendanceCell(attendance, isHoliday) {
        if (isHoliday) {
            return `<span class="status-indicator holiday">${this.statusConfig.holiday.emoji}</span>`;
        }

        if (!attendance) {
            return `<span class="status-indicator empty">-</span>`;
        }

        const config = this.statusConfig[attendance.status] || this.statusConfig.present;
        let content = `<span class="status-indicator ${attendance.status}">${config.emoji}</span>`;

        // Add time indicators for present/late status
        if (attendance.status === 'present' || attendance.status === 'late') {
            if (attendance.check_in) {
                content += `<small class="time-indicator">${attendance.check_in.substring(0, 5)}</small>`;
            }
        }

        // Add emoji tags if available
        if (attendance.emoji_tags && attendance.emoji_tags.length > 0) {
            content += `<div class="emoji-tags">${attendance.emoji_tags.slice(0, 3).join('')}</div>`;
        }

        return content;
    }

    getCellTooltip(worker, date, attendance, isHoliday) {
        if (isHoliday) {
            const holiday = this.holidays.find(h => h.holiday_date === date);
            return `Holiday: ${holiday?.name || 'Public Holiday'}`;
        }

        if (!attendance) {
            return `${worker.name} - ${date}\nNo attendance record`;
        }

        const config = this.statusConfig[attendance.status];
        let tooltip = `${worker.name} - ${date}\nStatus: ${config.label}`;

        if (attendance.check_in) {
            tooltip += `\nCheck In: ${attendance.check_in}`;
        }

        if (attendance.check_out) {
            tooltip += `\nCheck Out: ${attendance.check_out}`;
        }

        if (attendance.overtime && attendance.overtime > 0) {
            tooltip += `\nOvertime: ${Math.round(attendance.overtime)} minutes`;
        }

        if (attendance.notes) {
            tooltip += `\nNotes: ${attendance.notes}`;
        }

        return tooltip;
    }

    setupCellHandlers() {
        // Click handler for attendance cells
        this.container.querySelectorAll('.attendance-cell').forEach(cell => {
            cell.addEventListener('click', (e) => {
                const workerId = cell.dataset.workerId;
                const date = cell.dataset.date;
                const attendanceId = cell.dataset.attendanceId;

                this.openQuickEditModal(workerId, date, attendanceId);
            });

            // Double-click for quick status toggle
            cell.addEventListener('dblclick', (e) => {
                e.preventDefault();
                const workerId = cell.dataset.workerId;
                const date = cell.dataset.date;

                this.quickToggleStatus(workerId, date);
            });
        });
    }

    setupDragAndDrop() {
        // Make status items draggable
        this.container.querySelectorAll('.status-item').forEach(item => {
            item.addEventListener('dragstart', (e) => {
                this.draggedStatus = e.target.dataset.status;
                e.dataTransfer.effectAllowed = 'copy';
            });
        });

        // Make attendance cells drop targets
        this.container.addEventListener('dragover', (e) => {
            if (e.target.classList.contains('attendance-cell')) {
                e.preventDefault();
                e.dataTransfer.dropEffect = 'copy';
                e.target.classList.add('drag-over');
            }
        });

        this.container.addEventListener('dragleave', (e) => {
            if (e.target.classList.contains('attendance-cell')) {
                e.target.classList.remove('drag-over');
            }
        });

        this.container.addEventListener('drop', (e) => {
            if (e.target.classList.contains('attendance-cell') && this.draggedStatus) {
                e.preventDefault();
                e.target.classList.remove('drag-over');

                const workerId = e.target.dataset.workerId;
                const date = e.target.dataset.date;

                this.updateAttendanceStatus(workerId, date, this.draggedStatus);
                this.draggedStatus = null;
            }
        });

        // Toggle status palette
        const togglePaletteBtn = document.createElement('button');
        togglePaletteBtn.className = 'btn btn-sm btn-outline-primary';
        togglePaletteBtn.innerHTML = '<i class="bi bi-palette"></i> Status Palette';
        togglePaletteBtn.addEventListener('click', () => this.toggleStatusPalette());

        this.container.querySelector('.action-controls').appendChild(togglePaletteBtn);
    }

    toggleStatusPalette() {
        const palette = this.container.querySelector('.status-palette');
        palette.style.display = palette.style.display === 'none' ? 'block' : 'none';
    }

    async updateAttendanceStatus(workerId, date, status) {
        try {
            const existingAttendance = this.getAttendanceForWorkerAndDate(workerId, date);

            const data = {
                worker_id: workerId,
                attendance_date: date,
                status: status
            };

            let response;
            if (existingAttendance) {
                // Update existing record
                response = await fetch(`${this.options.apiBaseUrl}/records.php/${existingAttendance.id}`, {
                    method: 'PUT',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(data)
                });
            } else {
                // Create new record
                response = await fetch(`${this.options.apiBaseUrl}/records.php`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(data)
                });
            }

            const result = await response.json();

            if (result.success) {
                // Update local data
                if (existingAttendance) {
                    Object.assign(existingAttendance, result.data);
                } else {
                    this.attendanceData.push(result.data);
                }

                // Re-render the specific cell
                this.renderCalendarGrid();
                this.showSuccess('Attendance updated successfully');
            } else {
                throw new Error(result.message || 'Failed to update attendance');
            }

        } catch (error) {
            console.error('Failed to update attendance:', error);
            this.showError('Failed to update attendance: ' + error.message);
        }
    }

    showError(message) {
        this.showNotification(message, 'danger');
    }

    showSuccess(message) {
        this.showNotification(message, 'success');
    }

    showNotification(message, type = 'info') {
        const notificationDiv = document.createElement('div');
        notificationDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        notificationDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; max-width: 400px;';
        notificationDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(notificationDiv);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (notificationDiv.parentNode) {
                notificationDiv.parentNode.removeChild(notificationDiv);
            }
        }, 5000);
    }

    // Navigation methods
    changeViewMode(viewMode) {
        this.options.viewMode = viewMode;

        // Update button states
        this.container.querySelectorAll('.view-btn').forEach(btn => {
            btn.classList.toggle('btn-primary', btn.dataset.view === viewMode);
            btn.classList.toggle('btn-outline-primary', btn.dataset.view !== viewMode);
        });

        this.loadAttendanceData().then(() => this.renderCalendar());
    }

    navigatePeriod(direction) {
        const current = new Date(this.options.currentDate);

        switch (this.options.viewMode) {
            case 'day':
                current.setDate(current.getDate() + direction);
                break;
            case 'week':
                current.setDate(current.getDate() + (direction * 7));
                break;
            case 'month':
                current.setMonth(current.getMonth() + direction);
                break;
        }

        this.options.currentDate = current;
        this.loadAttendanceData().then(() => this.renderCalendar());
    }

    goToToday() {
        this.options.currentDate = new Date();
        this.loadAttendanceData().then(() => this.renderCalendar());
    }

    async refresh() {
        const refreshBtn = document.getElementById('refreshBtn');
        const originalContent = refreshBtn.innerHTML;

        refreshBtn.innerHTML = '<i class="bi bi-arrow-clockwise spin"></i> Refreshing...';
        refreshBtn.disabled = true;

        try {
            await this.loadAttendanceData();
            this.renderCalendar();
            this.showSuccess('Calendar refreshed successfully');
        } catch (error) {
            this.showError('Failed to refresh calendar');
        } finally {
            refreshBtn.innerHTML = originalContent;
            refreshBtn.disabled = false;
        }
    }

    applyFilters() {
        this.loadAttendanceData().then(() => this.renderCalendar());
    }

    filterWorkers(searchTerm) {
        this.renderWorkersList();
        this.renderCalendarGrid();
    }

    populateFilters() {
        // Populate department filter
        const departmentFilter = document.getElementById('departmentFilter');
        const departments = [...new Set(this.workers.map(w => w.department))].sort();

        departmentFilter.innerHTML = '<option value="">All Departments</option>' +
            departments.map(dept => `<option value="${dept}">${dept}</option>`).join('');

        // Populate status filter
        const statusFilter = document.getElementById('statusFilter');
        statusFilter.innerHTML = '<option value="">All Status</option>' +
            Object.entries(this.statusConfig).map(([status, config]) =>
                `<option value="${status}">${config.emoji} ${config.label}</option>`
            ).join('');
    }

    // Quick edit modal methods
    openQuickEditModal(workerId, date, attendanceId) {
        const worker = this.workers.find(w => w.id == workerId);
        const attendance = attendanceId ? this.attendanceData.find(a => a.id == attendanceId) : null;

        if (!worker) return;

        // Populate form
        document.getElementById('editWorkerId').value = workerId;
        document.getElementById('editDate').value = date;
        document.getElementById('editWorkerName').value = worker.name;
        document.getElementById('editDateInput').value = date;
        document.getElementById('editStatus').value = attendance?.status || 'present';
        document.getElementById('editCheckIn').value = attendance?.check_in || '';
        document.getElementById('editCheckOut').value = attendance?.check_out || '';
        document.getElementById('editBreakDuration').value = attendance?.break_duration || 60;
        document.getElementById('editNotes').value = attendance?.notes || '';
        document.getElementById('editProductivity').value = attendance?.productivity_score || '';

        // Set emoji tags
        if (attendance?.emoji_tags) {
            document.getElementById('selectedEmojis').value = JSON.stringify(attendance.emoji_tags);
            this.updateEmojiDisplay();
        } else {
            document.getElementById('selectedEmojis').value = '[]';
            this.updateEmojiDisplay();
        }

        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('quickEditModal'));
        modal.show();
    }

    setupEmojiSelector() {
        // Emoji category buttons
        this.container.querySelectorAll('.emoji-cat-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                // Update active button
                this.container.querySelectorAll('.emoji-cat-btn').forEach(b => b.classList.remove('active'));
                e.target.classList.add('active');

                // Show emojis for category
                this.showEmojiCategory(e.target.dataset.category);
            });
        });

        // Initialize with work category
        this.showEmojiCategory('work');
    }

    showEmojiCategory(category) {
        const emojiGrid = document.getElementById('emojiGrid');
        const emojis = this.emojiCategories[category] || [];

        emojiGrid.innerHTML = emojis.map(emoji => `
            <button type="button" class="btn btn-outline-light emoji-btn" data-emoji="${emoji}">
                ${emoji}
            </button>
        `).join('');

        // Add click handlers
        emojiGrid.querySelectorAll('.emoji-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.toggleEmoji(e.target.dataset.emoji);
            });
        });
    }

    toggleEmoji(emoji) {
        const selectedEmojis = JSON.parse(document.getElementById('selectedEmojis').value || '[]');
        const index = selectedEmojis.indexOf(emoji);

        if (index > -1) {
            selectedEmojis.splice(index, 1);
        } else {
            selectedEmojis.push(emoji);
        }

        document.getElementById('selectedEmojis').value = JSON.stringify(selectedEmojis);
        this.updateEmojiDisplay();
    }

    updateEmojiDisplay() {
        const selectedEmojis = JSON.parse(document.getElementById('selectedEmojis').value || '[]');

        // Update button states
        document.querySelectorAll('.emoji-btn').forEach(btn => {
            const isSelected = selectedEmojis.includes(btn.dataset.emoji);
            btn.classList.toggle('btn-primary', isSelected);
            btn.classList.toggle('btn-outline-light', !isSelected);
        });
    }

    async saveAttendanceRecord() {
        try {
            const formData = {
                worker_id: document.getElementById('editWorkerId').value,
                attendance_date: document.getElementById('editDateInput').value,
                status: document.getElementById('editStatus').value,
                check_in_time: document.getElementById('editCheckIn').value || null,
                check_out_time: document.getElementById('editCheckOut').value || null,
                break_duration_minutes: parseInt(document.getElementById('editBreakDuration').value) || 0,
                emoji_tags: JSON.parse(document.getElementById('selectedEmojis').value || '[]'),
                notes: document.getElementById('editNotes').value || null,
                productivity_score: document.getElementById('editProductivity').value || null
            };

            const attendanceId = document.getElementById('editDate').value;
            const existingAttendance = this.getAttendanceForWorkerAndDate(formData.worker_id, formData.attendance_date);

            let response;
            if (existingAttendance) {
                response = await fetch(`${this.options.apiBaseUrl}/records.php/${existingAttendance.id}`, {
                    method: 'PUT',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(formData)
                });
            } else {
                response = await fetch(`${this.options.apiBaseUrl}/records.php`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(formData)
                });
            }

            const result = await response.json();

            if (result.success) {
                // Update local data
                if (existingAttendance) {
                    Object.assign(existingAttendance, result.data);
                } else {
                    this.attendanceData.push(result.data);
                }

                // Close modal and refresh calendar
                bootstrap.Modal.getInstance(document.getElementById('quickEditModal')).hide();
                this.renderCalendarGrid();
                this.showSuccess('Attendance saved successfully');
            } else {
                throw new Error(result.message || 'Failed to save attendance');
            }

        } catch (error) {
            console.error('Failed to save attendance:', error);
            this.showError('Failed to save attendance: ' + error.message);
        }
    }

    quickToggleStatus(workerId, date) {
        const attendance = this.getAttendanceForWorkerAndDate(workerId, date);
        let newStatus;

        if (!attendance || attendance.status === 'absent') {
            newStatus = 'present';
        } else if (attendance.status === 'present') {
            newStatus = 'late';
        } else if (attendance.status === 'late') {
            newStatus = 'absent';
        } else {
            newStatus = 'present';
        }

        this.updateAttendanceStatus(workerId, date, newStatus);
    }

    startAutoRefresh() {
        setInterval(() => {
            this.loadAttendanceData().then(() => this.renderCalendar());
        }, this.options.refreshInterval);
    }
}

// Initialize calendar when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    // Auto-initialize if container exists
    const calendarContainer = document.getElementById('attendanceCalendar');
    if (calendarContainer) {
        window.attendanceCalendar = new AttendanceCalendar('attendanceCalendar', {
            apiBaseUrl: '/MtcInvoiceNewProject/api/attendance'
        });
    }
});
