<?php
/**
 * Holidays API Endpoint
 * 
 * @package MtcInvoice Attendance
 * @version 1.0
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once '../config/config.php';

// Get request method and path
$method = $_SERVER['REQUEST_METHOD'];
$path = $_SERVER['REQUEST_URI'];
$segments = explode('/', trim(parse_url($path, PHP_URL_PATH), '/'));

// Remove 'api/attendance/holidays.php' from segments
$segments = array_slice($segments, 3);

/**
 * Send JSON response
 */
function sendJsonResponse($status, $data = null, $message = null) {
    http_response_code($status);
    echo json_encode([
        'success' => $status >= 200 && $status < 300,
        'data' => $data,
        'message' => $message,
        'timestamp' => date('Y-m-d H:i:s')
    ]);
    exit();
}

/**
 * Validate required fields
 */
function validateRequired($data, $fields) {
    $missing = [];
    foreach ($fields as $field) {
        if (!isset($data[$field]) || empty(trim($data[$field]))) {
            $missing[] = $field;
        }
    }
    return $missing;
}

/**
 * Sanitize input data
 */
function sanitizeInput($data) {
    if (is_array($data)) {
        return array_map('sanitizeInput', $data);
    }
    return htmlspecialchars(strip_tags(trim($data)), ENT_QUOTES, 'UTF-8');
}

// Handle the request based on method and endpoint
try {
    switch ($method) {
        case 'GET':
            // Get single holiday or list
            if (isset($segments[0]) && is_numeric($segments[0])) {
                getHoliday($segments[0]);
            } else {
                getHolidays();
            }
            break;
            
        case 'POST':
            // Create new holiday
            createHoliday();
            break;
            
        case 'PUT':
        case 'PATCH':
            // Update existing holiday
            if (isset($segments[0]) && is_numeric($segments[0])) {
                updateHoliday($segments[0]);
            } else {
                sendJsonResponse(400, null, 'Holiday ID is required for update');
            }
            break;
            
        case 'DELETE':
            // Delete holiday
            if (isset($segments[0]) && is_numeric($segments[0])) {
                deleteHoliday($segments[0]);
            } else {
                sendJsonResponse(400, null, 'Holiday ID is required for deletion');
            }
            break;
            
        default:
            sendJsonResponse(405, null, 'Method not allowed');
    }
} catch (Exception $e) {
    error_log("Holidays API error: " . $e->getMessage());
    sendJsonResponse(500, null, 'Internal server error');
}

/**
 * Get all holidays with optional filtering
 */
function getHolidays() {
    try {
        $pdo = (new Database())->getConnection();
        if (!$pdo) {
            sendJsonResponse(500, null, 'Database connection failed');
            return;
        }
        
        // Build query with filters
        $where = ['1=1'];
        $params = [];
        
        // Filter by year
        if (isset($_GET['year']) && !empty($_GET['year'])) {
            $where[] = 'YEAR(holiday_date) = ?';
            $params[] = $_GET['year'];
        }
        
        // Filter by type
        if (isset($_GET['type']) && !empty($_GET['type'])) {
            $where[] = 'type = ?';
            $params[] = $_GET['type'];
        }
        
        // Filter by status
        if (isset($_GET['status']) && !empty($_GET['status'])) {
            $where[] = 'status = ?';
            $params[] = $_GET['status'];
        }
        
        // Filter by date range
        if (isset($_GET['start_date']) && !empty($_GET['start_date'])) {
            $where[] = 'holiday_date >= ?';
            $params[] = $_GET['start_date'];
        }
        
        if (isset($_GET['end_date']) && !empty($_GET['end_date'])) {
            $where[] = 'holiday_date <= ?';
            $params[] = $_GET['end_date'];
        }
        
        // Pagination
        $page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
        $limit = isset($_GET['limit']) ? min(100, max(1, (int)$_GET['limit'])) : 50;
        $offset = ($page - 1) * $limit;
        
        // Count total records
        $countSql = "SELECT COUNT(*) as total FROM holidays WHERE " . implode(' AND ', $where);
        $countStmt = $pdo->prepare($countSql);
        $countStmt->execute($params);
        $total = $countStmt->fetch()['total'];
        
        // Get holidays
        $sql = "SELECT id, name, holiday_date, type, description, is_recurring, status, created_at, updated_at 
                FROM holidays 
                WHERE " . implode(' AND ', $where) . "
                ORDER BY holiday_date ASC 
                LIMIT ? OFFSET ?";
        
        $params[] = $limit;
        $params[] = $offset;
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        $holidays = $stmt->fetchAll();
        
        sendJsonResponse(200, [
            'holidays' => $holidays,
            'pagination' => [
                'page' => $page,
                'limit' => $limit,
                'total' => $total,
                'pages' => ceil($total / $limit)
            ]
        ]);
        
    } catch (Exception $e) {
        error_log("Get holidays error: " . $e->getMessage());
        sendJsonResponse(500, null, 'Failed to retrieve holidays');
    }
}

/**
 * Get single holiday by ID
 */
function getHoliday($id) {
    try {
        $pdo = (new Database())->getConnection();
        if (!$pdo) {
            sendJsonResponse(500, null, 'Database connection failed');
            return;
        }
        
        $stmt = $pdo->prepare("
            SELECT id, name, holiday_date, type, description, is_recurring, status, created_at, updated_at 
            FROM holidays 
            WHERE id = ?
        ");
        $stmt->execute([$id]);
        $holiday = $stmt->fetch();
        
        if (!$holiday) {
            sendJsonResponse(404, null, 'Holiday not found');
            return;
        }
        
        sendJsonResponse(200, $holiday);
        
    } catch (Exception $e) {
        error_log("Get holiday error: " . $e->getMessage());
        sendJsonResponse(500, null, 'Failed to retrieve holiday');
    }
}

/**
 * Create new holiday
 */
function createHoliday() {
    try {
        $data = json_decode(file_get_contents('php://input'), true);
        
        if (empty($data)) {
            sendJsonResponse(400, null, 'No data provided');
            return;
        }
        
        // Validate required fields
        $required = ['name', 'holiday_date'];
        $missing = validateRequired($data, $required);
        if (!empty($missing)) {
            sendJsonResponse(400, null, 'Missing required fields: ' . implode(', ', $missing));
            return;
        }
        
        // Sanitize input
        $data = sanitizeInput($data);
        
        $pdo = (new Database())->getConnection();
        if (!$pdo) {
            sendJsonResponse(500, null, 'Database connection failed');
            return;
        }
        
        // Check if holiday already exists for this date
        $stmt = $pdo->prepare("SELECT id FROM holidays WHERE holiday_date = ? AND name = ?");
        $stmt->execute([$data['holiday_date'], $data['name']]);
        if ($stmt->fetch()) {
            sendJsonResponse(409, null, 'Holiday already exists for this date');
            return;
        }
        
        // Insert new holiday
        $stmt = $pdo->prepare("
            INSERT INTO holidays (name, holiday_date, type, description, is_recurring, status, created_by) 
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ");
        
        $stmt->execute([
            $data['name'],
            $data['holiday_date'],
            $data['type'] ?? 'public',
            $data['description'] ?? null,
            isset($data['is_recurring']) ? (int)$data['is_recurring'] : 0,
            $data['status'] ?? 'active',
            1 // TODO: Get from authenticated user
        ]);
        
        $holidayId = $pdo->lastInsertId();
        
        // Get the created holiday
        $stmt = $pdo->prepare("
            SELECT id, name, holiday_date, type, description, is_recurring, status, created_at, updated_at 
            FROM holidays 
            WHERE id = ?
        ");
        $stmt->execute([$holidayId]);
        $holiday = $stmt->fetch();
        
        sendJsonResponse(201, $holiday, 'Holiday created successfully');
        
    } catch (Exception $e) {
        error_log("Create holiday error: " . $e->getMessage());
        sendJsonResponse(500, null, 'Failed to create holiday');
    }
}

/**
 * Update existing holiday
 */
function updateHoliday($id) {
    try {
        $data = json_decode(file_get_contents('php://input'), true);

        if (empty($data)) {
            sendJsonResponse(400, null, 'No data provided for update');
            return;
        }

        $pdo = (new Database())->getConnection();
        if (!$pdo) {
            sendJsonResponse(500, null, 'Database connection failed');
            return;
        }

        // Check if holiday exists
        $stmt = $pdo->prepare("SELECT id FROM holidays WHERE id = ?");
        $stmt->execute([$id]);
        if (!$stmt->fetch()) {
            sendJsonResponse(404, null, 'Holiday not found');
            return;
        }

        // Sanitize input
        $data = sanitizeInput($data);

        // Check if holiday already exists for this date (excluding current holiday)
        if (isset($data['holiday_date']) && isset($data['name'])) {
            $stmt = $pdo->prepare("SELECT id FROM holidays WHERE holiday_date = ? AND name = ? AND id != ?");
            $stmt->execute([$data['holiday_date'], $data['name'], $id]);
            if ($stmt->fetch()) {
                sendJsonResponse(409, null, 'Holiday already exists for this date');
                return;
            }
        }

        // Prepare the SQL query
        $updates = [];
        $values = [];

        $allowedFields = [
            'name', 'holiday_date', 'type', 'description', 'is_recurring', 'status'
        ];

        foreach ($allowedFields as $field) {
            if (isset($data[$field])) {
                $updates[] = "$field = ?";
                if ($field === 'is_recurring') {
                    $values[] = (int)$data[$field];
                } else {
                    $values[] = $data[$field];
                }
            }
        }

        if (empty($updates)) {
            sendJsonResponse(400, null, 'No valid fields provided for update');
            return;
        }

        $values[] = $id;

        $sql = "UPDATE holidays SET " . implode(', ', $updates) . " WHERE id = ?";
        $stmt = $pdo->prepare($sql);
        $stmt->execute($values);

        // Get the updated holiday
        $stmt = $pdo->prepare("
            SELECT id, name, holiday_date, type, description, is_recurring, status, created_at, updated_at
            FROM holidays
            WHERE id = ?
        ");
        $stmt->execute([$id]);
        $holiday = $stmt->fetch();

        sendJsonResponse(200, $holiday, 'Holiday updated successfully');

    } catch (Exception $e) {
        error_log("Update holiday error: " . $e->getMessage());
        sendJsonResponse(500, null, 'Failed to update holiday');
    }
}

/**
 * Delete holiday
 */
function deleteHoliday($id) {
    try {
        $pdo = (new Database())->getConnection();
        if (!$pdo) {
            sendJsonResponse(500, null, 'Database connection failed');
            return;
        }

        // Check if holiday exists
        $stmt = $pdo->prepare("SELECT id FROM holidays WHERE id = ?");
        $stmt->execute([$id]);
        if (!$stmt->fetch()) {
            sendJsonResponse(404, null, 'Holiday not found');
            return;
        }

        // Delete the holiday
        $stmt = $pdo->prepare("DELETE FROM holidays WHERE id = ?");
        $stmt->execute([$id]);

        sendJsonResponse(200, null, 'Holiday deleted successfully');

    } catch (Exception $e) {
        error_log("Delete holiday error: " . $e->getMessage());
        sendJsonResponse(500, null, 'Failed to delete holiday');
    }
}
