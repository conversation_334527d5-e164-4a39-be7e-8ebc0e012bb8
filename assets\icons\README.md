# PWA Icons Directory

This directory contains the Progressive Web App icons for the Attendance Management System.

## Required Icons

The following icon sizes are needed for full PWA support:

- **icon-72x72.png** - Small icon for older devices
- **icon-96x96.png** - Standard small icon
- **icon-128x128.png** - Medium icon
- **icon-144x144.png** - Large icon for high-DPI devices
- **icon-152x152.png** - iOS icon size
- **icon-192x192.png** - Standard large icon (required)
- **icon-384x384.png** - Extra large icon
- **icon-512x512.png** - Maximum size icon (required)

## Additional Icons

- **badge-72x72.png** - Notification badge icon
- **dashboard-shortcut.png** - Dashboard shortcut icon
- **calendar-shortcut.png** - Calendar shortcut icon
- **analytics-shortcut.png** - Analytics shortcut icon
- **add-worker-shortcut.png** - Add worker shortcut icon

## Icon Guidelines

1. **Design**: Use the MtcInvoice brand colors (#667eea, #764ba2)
2. **Content**: Include a calendar or attendance-related symbol
3. **Format**: PNG format with transparent background
4. **Quality**: High-resolution, crisp edges
5. **Consistency**: Maintain consistent design across all sizes

## Generating Icons

You can use online tools like:
- [PWA Icon Generator](https://www.pwabuilder.com/imageGenerator)
- [App Icon Generator](https://appicon.co/)
- [Favicon Generator](https://realfavicongenerator.net/)

Or create them manually using design tools like:
- Adobe Illustrator/Photoshop
- Figma
- Canva
- GIMP (free alternative)

## Installation

1. Create icons in the required sizes
2. Save them in this directory with the exact filenames listed above
3. Ensure all icons are optimized for web (compressed but high quality)
4. Test the PWA installation to verify icons display correctly

## Current Status

⚠️ **Icons needed**: Please create and add the required icon files to enable full PWA functionality.
