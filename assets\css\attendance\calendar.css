/**
 * Attendance Calendar Styles
 * 
 * Modern, responsive styles for the interactive attendance calendar
 * with timeline view, drag-and-drop functionality, and emoji indicators.
 * 
 * @package MtcInvoice Attendance
 * @version 1.0
 */

/* Main Calendar Container */
.attendance-calendar {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    min-height: 600px;
}

/* Calendar Header */
.calendar-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
}

.calendar-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 15px;
}

.view-controls {
    display: flex;
    gap: 5px;
}

.view-controls .btn {
    border-color: rgba(255, 255, 255, 0.3);
    color: white;
}

.view-controls .btn-primary {
    background-color: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
}

.navigation-controls {
    display: flex;
    align-items: center;
    gap: 15px;
}

.current-period {
    font-weight: 600;
    min-width: 200px;
    text-align: center;
}

.action-controls {
    display: flex;
    gap: 10px;
    align-items: center;
}

/* Status Palette */
.status-palette {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 15px;
    margin-top: 15px;
    backdrop-filter: blur(10px);
}

.palette-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    font-size: 14px;
}

.status-items {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.status-item {
    display: flex;
    align-items: center;
    gap: 8px;
    background: rgba(255, 255, 255, 0.2);
    padding: 8px 12px;
    border-radius: 20px;
    cursor: grab;
    transition: all 0.3s ease;
    user-select: none;
}

.status-item:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

.status-item:active {
    cursor: grabbing;
}

.status-emoji {
    font-size: 16px;
}

.status-label {
    font-size: 12px;
    font-weight: 500;
}

/* Calendar Body */
.calendar-body {
    display: flex;
    height: 500px;
    overflow: hidden;
}

/* Workers Sidebar */
.workers-sidebar {
    width: 250px;
    background: #f8f9fa;
    border-right: 1px solid #dee2e6;
    display: flex;
    flex-direction: column;
}

.sidebar-header {
    padding: 15px;
    border-bottom: 1px solid #dee2e6;
    background: white;
}

.sidebar-header h6 {
    margin-bottom: 10px;
    color: #495057;
    font-weight: 600;
}

.search-box input {
    border-radius: 20px;
    border: 1px solid #ced4da;
    padding: 8px 15px;
}

.workers-list {
    flex: 1;
    overflow-y: auto;
    padding: 10px;
}

.worker-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 5px;
}

.worker-item:hover {
    background: rgba(102, 126, 234, 0.1);
    transform: translateX(5px);
}

.worker-avatar img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

.avatar-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 16px;
}

.worker-info {
    flex: 1;
    min-width: 0;
}

.worker-name {
    font-weight: 500;
    color: #495057;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.worker-details {
    font-size: 12px;
    color: #6c757d;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Calendar Grid */
.calendar-grid-container {
    flex: 1;
    overflow: auto;
    background: white;
}

.calendar-grid {
    min-width: 100%;
    display: table;
    border-collapse: separate;
    border-spacing: 0;
}

.grid-header {
    display: table-row;
    background: #f8f9fa;
    position: sticky;
    top: 0;
    z-index: 10;
}

.grid-body {
    display: table-row-group;
}

.grid-row {
    display: table-row;
}

.grid-row:nth-child(even) {
    background: #fafbfc;
}

.grid-row:hover {
    background: rgba(102, 126, 234, 0.05);
}

.grid-cell {
    display: table-cell;
    border: 1px solid #e9ecef;
    vertical-align: middle;
    position: relative;
}

.header-cell {
    background: #f8f9fa;
    font-weight: 600;
    color: #495057;
    text-align: center;
    padding: 12px 8px;
    border-bottom: 2px solid #dee2e6;
}

.worker-column {
    width: 200px;
    text-align: left;
    padding: 12px 15px;
    position: sticky;
    left: 0;
    background: #f8f9fa;
    z-index: 5;
}

.date-column {
    width: 80px;
    min-width: 80px;
}

.date-label {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.day-name {
    font-size: 11px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    color: #6c757d;
}

.day-number {
    font-size: 16px;
    font-weight: 600;
    color: #495057;
}

.worker-cell {
    width: 200px;
    padding: 12px 15px;
    position: sticky;
    left: 0;
    background: white;
    z-index: 5;
    border-right: 2px solid #dee2e6;
}

.attendance-cell {
    width: 80px;
    height: 60px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    padding: 5px;
}

.attendance-cell:hover {
    background: rgba(102, 126, 234, 0.1);
    transform: scale(1.05);
}

.attendance-cell.drag-over {
    background: rgba(40, 167, 69, 0.2);
    border: 2px dashed #28a745;
}

.attendance-cell.holiday-cell {
    background: rgba(111, 66, 193, 0.1);
}

.status-indicator {
    font-size: 20px;
    display: block;
    margin-bottom: 2px;
}

.status-indicator.empty {
    color: #dee2e6;
    font-size: 16px;
}

.time-indicator {
    display: block;
    font-size: 10px;
    color: #6c757d;
    font-weight: 500;
}

.emoji-tags {
    font-size: 12px;
    margin-top: 2px;
    line-height: 1;
}

/* Calendar Footer */
.calendar-footer {
    background: #f8f9fa;
    padding: 15px 20px;
    border-top: 1px solid #dee2e6;
}

.legend {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    justify-content: center;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 12px;
    color: #6c757d;
}

.legend-emoji {
    font-size: 14px;
}

/* Quick Edit Modal */
.emoji-selector {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 15px;
    background: #f8f9fa;
}

.emoji-categories {
    display: flex;
    gap: 5px;
    margin-bottom: 15px;
}

.emoji-cat-btn {
    font-size: 12px;
    padding: 5px 12px;
}

.emoji-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
    gap: 5px;
    max-height: 120px;
    overflow-y: auto;
}

.emoji-btn {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    font-size: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #dee2e6;
}

.emoji-btn:hover {
    transform: scale(1.1);
}

/* Animations */
@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.spin {
    animation: spin 1s linear infinite;
}

/* Responsive Design */
@media (max-width: 768px) {
    .calendar-controls {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }
    
    .navigation-controls {
        justify-content: center;
    }
    
    .action-controls {
        justify-content: center;
    }
    
    .calendar-body {
        flex-direction: column;
        height: auto;
    }
    
    .workers-sidebar {
        width: 100%;
        max-height: 200px;
    }
    
    .worker-column,
    .worker-cell {
        width: 150px;
    }
    
    .date-column,
    .attendance-cell {
        width: 60px;
    }
    
    .current-period {
        min-width: auto;
        font-size: 14px;
    }
    
    .legend {
        justify-content: flex-start;
        gap: 10px;
    }
    
    .legend-item {
        font-size: 11px;
    }
}

@media (max-width: 576px) {
    .calendar-header {
        padding: 15px;
    }
    
    .view-controls .btn {
        font-size: 12px;
        padding: 5px 10px;
    }
    
    .worker-column,
    .worker-cell {
        width: 120px;
        padding: 8px 10px;
    }
    
    .date-column,
    .attendance-cell {
        width: 50px;
        height: 50px;
    }
    
    .status-indicator {
        font-size: 16px;
    }
    
    .time-indicator {
        font-size: 9px;
    }
    
    .emoji-tags {
        font-size: 10px;
    }
}
