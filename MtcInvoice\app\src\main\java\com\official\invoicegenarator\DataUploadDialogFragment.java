package com.official.invoicegenarator;

import android.content.Context;
import android.content.SharedPreferences;
import android.os.Build;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.DialogFragment;
import android.text.Editable;
import android.text.TextWatcher;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageButton;
import android.widget.Toast;
import com.google.android.material.bottomsheet.BottomSheetDialogFragment;

import java.io.DataOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.function.Consumer;

public class DataUploadDialogFragment extends BottomSheetDialogFragment {

    private static final String PREFS_NAME = "InvoiceTrackerPrefs";
    private EditText editTextDescription, editTextLocation, editTextQR, editTextLPO, editTextINB, editTextAmount, editTextW_A, editTextPaymentStatus;
    private ImageButton buttonUpload;
    private SharedPreferences sharedPreferences;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        sharedPreferences = requireContext().getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fgdata_upload, container, false);



        editTextDescription = view.findViewById(R.id.editTextDescription);
        editTextLocation = view.findViewById(R.id.editTextLocation);
        editTextQR = view.findViewById(R.id.editTextQR);
        editTextLPO = view.findViewById(R.id.editTextLPO);
        editTextINB = view.findViewById(R.id.editTextINB);
        editTextAmount = view.findViewById(R.id.editTextAmount);
        editTextW_A = view.findViewById(R.id.editTextW_A);
        editTextPaymentStatus = view.findViewById(R.id.payment_status);
        buttonUpload = view.findViewById(R.id.buttonUpload);

        loadSavedData(); // Load saved data into EditText fields
        setupTextWatchers(); // Save data to SharedPreferences on text change


        return view;
    }

    // Call this from AsyncTask or background thread



    private void loadSavedData() {
        editTextDescription.setText(sharedPreferences.getString("description", ""));
        editTextLocation.setText(sharedPreferences.getString("location", ""));
        editTextQR.setText(sharedPreferences.getString("qr", ""));
        editTextLPO.setText(sharedPreferences.getString("lpo", ""));
        editTextINB.setText(sharedPreferences.getString("inb", ""));
        editTextAmount.setText(sharedPreferences.getString("amount", ""));
        editTextW_A.setText(sharedPreferences.getString("w_a", ""));
        editTextPaymentStatus.setText(sharedPreferences.getString("paymentStatus", ""));
    }

    private void setupTextWatchers() {
        editTextDescription.addTextChangedListener(new SimpleTextWatcher(s -> saveToPrefs("description", s)));
        editTextLocation.addTextChangedListener(new SimpleTextWatcher(s -> saveToPrefs("location", s)));
        editTextQR.addTextChangedListener(new SimpleTextWatcher(s -> saveToPrefs("qr", s)));
        editTextLPO.addTextChangedListener(new SimpleTextWatcher(s -> saveToPrefs("lpo", s)));
        editTextINB.addTextChangedListener(new SimpleTextWatcher(s -> saveToPrefs("inb", s)));
        editTextAmount.addTextChangedListener(new SimpleTextWatcher(s -> saveToPrefs("amount", s)));
        editTextW_A.addTextChangedListener(new SimpleTextWatcher(s -> saveToPrefs("w_a", s)));
        editTextPaymentStatus.addTextChangedListener(new SimpleTextWatcher(s -> saveToPrefs("paymentStatus", s)));
    }

    private void saveToPrefs(String key, String value) {
        sharedPreferences.edit().putString(key, value).apply();
    }



    private void clearFields() {
        editTextDescription.setText("");
        editTextLocation.setText("");
        editTextQR.setText("");
        editTextLPO.setText("");
        editTextINB.setText("");
        editTextAmount.setText("");
        editTextW_A.setText("");
        editTextPaymentStatus.setText("");
    }

    private void clearSavedData() {
        sharedPreferences.edit().clear().apply();
    }

    private static class SimpleTextWatcher implements TextWatcher {
        private final Consumer<String> callback;

        SimpleTextWatcher(Consumer<String> callback) {
            this.callback = callback;
        }

        @Override
        public void beforeTextChanged(CharSequence s, int start, int count, int after) {}

        @Override
        public void onTextChanged(CharSequence s, int start, int before, int count) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                callback.accept(s.toString());
            }
        }

        @Override
        public void afterTextChanged(Editable s) {}
    }
}
