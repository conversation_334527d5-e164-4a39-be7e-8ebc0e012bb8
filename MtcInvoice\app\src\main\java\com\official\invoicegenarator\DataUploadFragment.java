package com.official.invoicegenarator;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.Toast;

import androidx.fragment.app.Fragment;

import com.official.invoicegenarator.utils.ApiClient;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.HashMap;
import java.util.Map;

public class DataUploadFragment extends Fragment {

    private EditText editTextDescription, editTextLocation, editTextQR, editTextLPO, editTextINB, editTextAmount, editTextW_A, editTextPaymentStatus;
    private Button buttonUpload;
    private ApiClient apiClient;

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_data_upload, container, false);

        // Initialize API client
        apiClient = ApiClient.getInstance(requireContext());

        // Initialize UI components
        initializeViews(view);
        
        buttonUpload.setOnClickListener(v -> uploadData());

        return view;
    }

    private void initializeViews(View view) {
        editTextDescription = view.findViewById(R.id.editTextDescription);
        editTextLocation = view.findViewById(R.id.editTextLocation);
        editTextQR = view.findViewById(R.id.editTextQR);
        editTextLPO = view.findViewById(R.id.editTextLPO);
        editTextINB = view.findViewById(R.id.editTextINB);
        editTextAmount = view.findViewById(R.id.editTextAmount);
        editTextW_A = view.findViewById(R.id.editTextW_A);
        editTextPaymentStatus = view.findViewById(R.id.payment_status);
        buttonUpload = view.findViewById(R.id.buttonUpload);
    }

    private void uploadData() {
        // Validate required fields
        String description = editTextDescription.getText().toString().trim();
        String location = editTextLocation.getText().toString().trim();
        String qr = editTextQR.getText().toString().trim();
        
        if (description.isEmpty() || location.isEmpty() || qr.isEmpty()) {
            Toast.makeText(requireContext(), "Please fill in all required fields", Toast.LENGTH_SHORT).show();
            return;
        }

        // Prepare data for API
        Map<String, String> data = new HashMap<>();
        data.put("description", description);
        data.put("location", location);
        data.put("qr", qr);
        
        // Add optional fields if they're not empty
        if (!editTextLPO.getText().toString().trim().isEmpty()) {
            data.put("lpo", editTextLPO.getText().toString().trim());
        }
        if (!editTextINB.getText().toString().trim().isEmpty()) {
            data.put("inb", editTextINB.getText().toString().trim());
        }
        if (!editTextAmount.getText().toString().trim().isEmpty()) {
            data.put("amount", editTextAmount.getText().toString().trim());
        }
        if (!editTextW_A.getText().toString().trim().isEmpty()) {
            data.put("w_a", editTextW_A.getText().toString().trim());
        }
        if (!editTextPaymentStatus.getText().toString().trim().isEmpty()) {
            data.put("payment_status", editTextPaymentStatus.getText().toString().trim());
        }

        // Show loading
        buttonUpload.setEnabled(false);
        buttonUpload.setText("Uploading...");

        // Make API call
        apiClient.createInvoiceDataItem(data, new ApiClient.ApiResponseListener() {
            @Override
            public void onSuccess(JSONObject response) {
                requireActivity().runOnUiThread(() -> {
                    try {
                        String message = response.optString("message", "Data uploaded successfully");
                        Toast.makeText(requireContext(), message, Toast.LENGTH_SHORT).show();
                        clearFields();
                    } catch (Exception e) {
                        Toast.makeText(requireContext(), "Data uploaded successfully", Toast.LENGTH_SHORT).show();
                        clearFields();
                    }
                    buttonUpload.setEnabled(true);
                    buttonUpload.setText("Upload");
                });
            }

            @Override
            public void onError(String message) {
                requireActivity().runOnUiThread(() -> {
                    Toast.makeText(requireContext(), "Error: " + message, Toast.LENGTH_LONG).show();
                    buttonUpload.setEnabled(true);
                    buttonUpload.setText("Upload");
                });
            }
        });
    }

    private void clearFields() {
        editTextDescription.setText("");
        editTextLocation.setText("");
        editTextQR.setText("");
        editTextLPO.setText("");
        editTextINB.setText("");
        editTextAmount.setText("");
        editTextW_A.setText("");
        editTextPaymentStatus.setText("");
    }
}