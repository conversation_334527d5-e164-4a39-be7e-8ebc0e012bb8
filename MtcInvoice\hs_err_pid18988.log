#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 1321296 bytes for Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   The process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (arena.cpp:168), pid=18988, tid=16552
#
# JRE version: Java(TM) SE Runtime Environment (21.0.2+13) (build 21.0.2+13-LTS-58)
# Java VM: Java HotSpot(TM) 64-Bit Server VM (21.0.2+13-LTS-58, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.xml/javax.xml.namespace=ALL-UNNAMED -Xmx2048m -Dfile.encoding=UTF-8 -Duser.country=US -Duser.language=en -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.14.2-bin\2pb3mgt1p815evrl3weanttgr\gradle-8.14.2\lib\agents\gradle-instrumentation-agent-8.14.2.jar org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.14.2

Host: AMD Ryzen 5 3400G with Radeon Vega Graphics    , 8 cores, 21G,  Windows 10 , 64 bit Build 19041 (10.0.19041.5915)
Time: Tue Jun 17 22:18:27 2025 Bangladesh Standard Time elapsed time: 72.986428 seconds (0d 0h 1m 12s)

---------------  T H R E A D  ---------------

Current thread (0x00000141e1c6a000):  JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=16552, stack(0x00000012bd000000,0x00000012bd100000) (1024K)]


Current CompileTask:
C2:  72986 31718   !   4       com.android.tools.r8.internal.O90::<init> (942 bytes)

Stack: [0x00000012bd000000,0x00000012bd100000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6cade9]
V  [jvm.dll+0x8569c1]
V  [jvm.dll+0x858d2e]
V  [jvm.dll+0x859413]
V  [jvm.dll+0x280e56]
V  [jvm.dll+0xc3f3d]
V  [jvm.dll+0xc4473]
V  [jvm.dll+0x3b5bf2]
V  [jvm.dll+0x382855]
V  [jvm.dll+0x381cca]
V  [jvm.dll+0x249bd0]
V  [jvm.dll+0x2491b1]
V  [jvm.dll+0x1c9634]
V  [jvm.dll+0x258859]
V  [jvm.dll+0x256e3a]
V  [jvm.dll+0x3ef6c6]
V  [jvm.dll+0x7ff568]
V  [jvm.dll+0x6c953d]
C  [ucrtbase.dll+0x21bb2]
C  [KERNEL32.DLL+0x17374]
C  [ntdll.dll+0x4cc91]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x00000141f5d8d630, length=155, elements={
0x00000141c2f97fc0, 0x00000141e1bbedf0, 0x00000141e1bbf720, 0x00000141e1bc1420,
0x00000141e1bc1a90, 0x00000141e1bc2100, 0x00000141e1bc74c0, 0x00000141e1c6a000,
0x00000141e1c6c6c0, 0x00000141e1d58090, 0x00000141e1d5b510, 0x00000141e1d58db0,
0x00000141e1d5ae80, 0x00000141e1d59ad0, 0x00000141e1d5a7f0, 0x00000141e1d59440,
0x00000141e7a66a30, 0x00000141e7a69190, 0x00000141e7a64960, 0x00000141e7a6abd0,
0x00000141e7a63c40, 0x00000141e7a670c0, 0x00000141e7a64ff0, 0x00000141e7a67de0,
0x00000141e7a65680, 0x00000141e7a65d10, 0x00000141e7a6a540, 0x00000141e7a642d0,
0x00000141e7a6b260, 0x00000141e7a68b00, 0x00000141e7a67750, 0x00000141e7a663a0,
0x00000141e1d58720, 0x00000141ea64e570, 0x00000141ea64aa60, 0x00000141ea64d1c0,
0x00000141ea647c70, 0x00000141ea64cb30, 0x00000141ea64b0f0, 0x00000141ea649d40,
0x00000141ea64d850, 0x00000141ea64b780, 0x00000141ea64dee0, 0x00000141ea64be10,
0x00000141ea6475e0, 0x00000141ea64a3d0, 0x00000141ea64ec00, 0x00000141ea64c4a0,
0x00000141e826bc80, 0x00000141e82660a0, 0x00000141e826c9a0, 0x00000141e8267450,
0x00000141e8266730, 0x00000141e8267ae0, 0x00000141e826a240, 0x00000141e8268e90,
0x00000141e8266dc0, 0x00000141e826c310, 0x00000141e8268800, 0x00000141e8269bb0,
0x00000141e957d060, 0x00000141e8265a10, 0x00000141e826af60, 0x00000141e8268170,
0x00000141ebd85cd0, 0x00000141ebd7f3d0, 0x00000141ebd83570, 0x00000141ebd814a0,
0x00000141ebd81b30, 0x00000141ebd83c00, 0x00000141ebd821c0, 0x00000141ebd800f0,
0x00000141ebd84290, 0x00000141ebd82850, 0x00000141ebd7fa60, 0x00000141ebd80780,
0x00000141ebd82ee0, 0x00000141ebd84fb0, 0x00000141ebd80e10, 0x00000141ebd85640,
0x00000141ebd7ed40, 0x00000141ebd86360, 0x00000141e656f140, 0x00000141e656eab0,
0x00000141e6571f30, 0x00000141e656dd90, 0x00000141e65718a0, 0x00000141e6571210,
0x00000141e65725c0, 0x00000141e656e420, 0x00000141ec377d60, 0x00000141ec372810,
0x00000141ec373bc0, 0x00000141ec3769b0, 0x00000141ec375600, 0x00000141ec371460,
0x00000141e66b8650, 0x00000141e66bbad0, 0x00000141e66bc160, 0x00000141e66bc7f0,
0x00000141e66b5ef0, 0x00000141e66bd510, 0x00000141e66badb0, 0x00000141e66b7fc0,
0x00000141e66b72a0, 0x00000141e66bb440, 0x00000141e66bce80, 0x00000141e66b6580,
0x00000141e66bdba0, 0x00000141e66b8ce0, 0x00000141e66b9370, 0x00000141e66b6c10,
0x00000141e66b9a00, 0x00000141e8759000, 0x00000141e66be8c0, 0x00000141e66bef50,
0x00000141e66ba090, 0x00000141e66bf5e0, 0x00000141e66bfc70, 0x00000141e66c16b0,
0x00000141e66c3e10, 0x00000141e66c51c0, 0x00000141e66c1d40, 0x00000141e66c4b30,
0x00000141e66c23d0, 0x00000141e66c2a60, 0x00000141e66c30f0, 0x00000141e66c3780,
0x00000141e66c44a0, 0x00000141ee7265a0, 0x00000141ee72a740, 0x00000141ee727950,
0x00000141ee72add0, 0x00000141ee72b460, 0x00000141ee72dbc0, 0x00000141ee726c30,
0x00000141ee727fe0, 0x00000141ee72baf0, 0x00000141ee72c180, 0x00000141ee72c810,
0x00000141ee72cea0, 0x00000141f60227f0, 0x00000141f6020090, 0x00000141f60276b0,
0x00000141f6023510, 0x00000141f6028a60, 0x00000141f6029e10, 0x00000141f6023ba0,
0x00000141f6026300, 0x00000141f602ab30, 0x00000141f602a4a0, 0x00000141f6026990,
0x00000141f6027d40, 0x00000141f6029780, 0x00000141f4d4cc20
}

Java Threads: ( => current thread )
  0x00000141c2f97fc0 JavaThread "main"                              [_thread_blocked, id=14696, stack(0x00000012bc200000,0x00000012bc300000) (1024K)]
  0x00000141e1bbedf0 JavaThread "Reference Handler"          daemon [_thread_blocked, id=14848, stack(0x00000012bca00000,0x00000012bcb00000) (1024K)]
  0x00000141e1bbf720 JavaThread "Finalizer"                  daemon [_thread_blocked, id=4548, stack(0x00000012bcb00000,0x00000012bcc00000) (1024K)]
  0x00000141e1bc1420 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=2152, stack(0x00000012bcc00000,0x00000012bcd00000) (1024K)]
  0x00000141e1bc1a90 JavaThread "Attach Listener"            daemon [_thread_blocked, id=16672, stack(0x00000012bcd00000,0x00000012bce00000) (1024K)]
  0x00000141e1bc2100 JavaThread "Service Thread"             daemon [_thread_blocked, id=14380, stack(0x00000012bce00000,0x00000012bcf00000) (1024K)]
  0x00000141e1bc74c0 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=15900, stack(0x00000012bcf00000,0x00000012bd000000) (1024K)]
=>0x00000141e1c6a000 JavaThread "C2 CompilerThread0"         daemon [_thread_in_native, id=16552, stack(0x00000012bd000000,0x00000012bd100000) (1024K)]
  0x00000141e1c6c6c0 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=3164, stack(0x00000012bd100000,0x00000012bd200000) (1024K)]
  0x00000141e1d58090 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=14588, stack(0x00000012bd200000,0x00000012bd300000) (1024K)]
  0x00000141e1d5b510 JavaThread "Notification Thread"        daemon [_thread_blocked, id=8744, stack(0x00000012bd300000,0x00000012bd400000) (1024K)]
  0x00000141e1d58db0 JavaThread "Daemon health stats"               [_thread_blocked, id=15460, stack(0x00000012bdd00000,0x00000012bde00000) (1024K)]
  0x00000141e1d5ae80 JavaThread "Incoming local TCP Connector on port 58876"        [_thread_in_native, id=17708, stack(0x00000012bde00000,0x00000012bdf00000) (1024K)]
  0x00000141e1d59ad0 JavaThread "Daemon periodic checks"            [_thread_blocked, id=14508, stack(0x00000012bdf00000,0x00000012be000000) (1024K)]
  0x00000141e1d5a7f0 JavaThread "Daemon"                            [_thread_blocked, id=14176, stack(0x00000012be000000,0x00000012be100000) (1024K)]
  0x00000141e1d59440 JavaThread "Handler for socket connection from /127.0.0.1:58876 to /127.0.0.1:58877"        [_thread_in_native, id=6768, stack(0x00000012be100000,0x00000012be200000) (1024K)]
  0x00000141e7a66a30 JavaThread "Cancel handler"                    [_thread_blocked, id=9872, stack(0x00000012be200000,0x00000012be300000) (1024K)]
  0x00000141e7a69190 JavaThread "Daemon worker"                     [_thread_blocked, id=13680, stack(0x00000012be300000,0x00000012be400000) (1024K)]
  0x00000141e7a64960 JavaThread "Asynchronous log dispatcher for DefaultDaemonConnection: socket connection from /127.0.0.1:58876 to /127.0.0.1:58877"        [_thread_blocked, id=2204, stack(0x00000012be400000,0x00000012be500000) (1024K)]
  0x00000141e7a6abd0 JavaThread "Stdin handler"                     [_thread_blocked, id=4316, stack(0x00000012be500000,0x00000012be600000) (1024K)]
  0x00000141e7a63c40 JavaThread "Daemon client event forwarder"        [_thread_blocked, id=17244, stack(0x00000012be600000,0x00000012be700000) (1024K)]
  0x00000141e7a670c0 JavaThread "Cache worker for journal cache (C:\Users\<USER>\.gradle\caches\journal-1)"        [_thread_blocked, id=11584, stack(0x00000012be700000,0x00000012be800000) (1024K)]
  0x00000141e7a64ff0 JavaThread "File lock request listener"        [_thread_in_native, id=1180, stack(0x00000012be800000,0x00000012be900000) (1024K)]
  0x00000141e7a67de0 JavaThread "Cache worker for file hash cache (C:\Users\<USER>\.gradle\caches\8.14.2\fileHashes)"        [_thread_blocked, id=3028, stack(0x00000012be900000,0x00000012bea00000) (1024K)]
  0x00000141e7a65680 JavaThread "Cache worker for file hash cache (C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\.gradle\8.14.2\fileHashes)"        [_thread_blocked, id=18400, stack(0x00000012beb00000,0x00000012bec00000) (1024K)]
  0x00000141e7a65d10 JavaThread "Cache worker for Build Output Cleanup Cache (C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\.gradle\buildOutputCleanup)"        [_thread_blocked, id=15976, stack(0x00000012bec00000,0x00000012bed00000) (1024K)]
  0x00000141e7a6a540 JavaThread "File watcher server"        daemon [_thread_in_native, id=9928, stack(0x00000012bed00000,0x00000012bee00000) (1024K)]
  0x00000141e7a642d0 JavaThread "File watcher consumer"      daemon [_thread_blocked, id=5376, stack(0x00000012bee00000,0x00000012bef00000) (1024K)]
  0x00000141e7a6b260 JavaThread "jar transforms"                    [_thread_blocked, id=11180, stack(0x00000012bef00000,0x00000012bf000000) (1024K)]
  0x00000141e7a68b00 JavaThread "Cache worker for checksums cache (C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\.gradle\8.14.2\checksums)"        [_thread_blocked, id=12624, stack(0x00000012bf000000,0x00000012bf100000) (1024K)]
  0x00000141e7a67750 JavaThread "Cache worker for file content cache (C:\Users\<USER>\.gradle\caches\8.14.2\fileContent)"        [_thread_blocked, id=18480, stack(0x00000012bf100000,0x00000012bf200000) (1024K)]
  0x00000141e7a663a0 JavaThread "Cache worker for cache directory md-supplier (C:\Users\<USER>\.gradle\caches\8.14.2\md-supplier)"        [_thread_blocked, id=16484, stack(0x00000012bf200000,0x00000012bf300000) (1024K)]
  0x00000141e1d58720 JavaThread "Cache worker for cache directory md-rule (C:\Users\<USER>\.gradle\caches\8.14.2\md-rule)"        [_thread_blocked, id=14292, stack(0x00000012bf300000,0x00000012bf400000) (1024K)]
  0x00000141ea64e570 JavaThread "Problems report writer"            [_thread_blocked, id=8552, stack(0x00000012bf400000,0x00000012bf500000) (1024K)]
  0x00000141ea64aa60 JavaThread "jar transforms Thread 2"           [_thread_blocked, id=15964, stack(0x00000012bf500000,0x00000012bf600000) (1024K)]
  0x00000141ea64d1c0 JavaThread "jar transforms Thread 3"           [_thread_blocked, id=5336, stack(0x00000012bf600000,0x00000012bf700000) (1024K)]
  0x00000141ea647c70 JavaThread "Unconstrained build operations"        [_thread_blocked, id=17560, stack(0x00000012bf700000,0x00000012bf800000) (1024K)]
  0x00000141ea64cb30 JavaThread "Unconstrained build operations Thread 2"        [_thread_blocked, id=14392, stack(0x00000012bf800000,0x00000012bf900000) (1024K)]
  0x00000141ea64b0f0 JavaThread "Unconstrained build operations Thread 3"        [_thread_blocked, id=1816, stack(0x00000012bf900000,0x00000012bfa00000) (1024K)]
  0x00000141ea649d40 JavaThread "Unconstrained build operations Thread 4"        [_thread_blocked, id=7896, stack(0x00000012bfa00000,0x00000012bfb00000) (1024K)]
  0x00000141ea64d850 JavaThread "Unconstrained build operations Thread 5"        [_thread_blocked, id=13220, stack(0x00000012bfb00000,0x00000012bfc00000) (1024K)]
  0x00000141ea64b780 JavaThread "Unconstrained build operations Thread 6"        [_thread_blocked, id=8312, stack(0x00000012bfc00000,0x00000012bfd00000) (1024K)]
  0x00000141ea64dee0 JavaThread "Unconstrained build operations Thread 7"        [_thread_blocked, id=14816, stack(0x00000012bfd00000,0x00000012bfe00000) (1024K)]
  0x00000141ea64be10 JavaThread "Unconstrained build operations Thread 8"        [_thread_blocked, id=19032, stack(0x00000012bfe00000,0x00000012bff00000) (1024K)]
  0x00000141ea6475e0 JavaThread "Unconstrained build operations Thread 9"        [_thread_blocked, id=5296, stack(0x00000012bff00000,0x00000012c0000000) (1024K)]
  0x00000141ea64a3d0 JavaThread "Unconstrained build operations Thread 10"        [_thread_blocked, id=4544, stack(0x00000012c0000000,0x00000012c0100000) (1024K)]
  0x00000141ea64ec00 JavaThread "Unconstrained build operations Thread 11"        [_thread_blocked, id=6732, stack(0x00000012c0100000,0x00000012c0200000) (1024K)]
  0x00000141ea64c4a0 JavaThread "Unconstrained build operations Thread 12"        [_thread_blocked, id=1060, stack(0x00000012c0200000,0x00000012c0300000) (1024K)]
  0x00000141e826bc80 JavaThread "Unconstrained build operations Thread 13"        [_thread_blocked, id=19380, stack(0x00000012c0300000,0x00000012c0400000) (1024K)]
  0x00000141e82660a0 JavaThread "Unconstrained build operations Thread 14"        [_thread_blocked, id=1164, stack(0x00000012c0400000,0x00000012c0500000) (1024K)]
  0x00000141e826c9a0 JavaThread "Unconstrained build operations Thread 15"        [_thread_blocked, id=16060, stack(0x00000012c0500000,0x00000012c0600000) (1024K)]
  0x00000141e8267450 JavaThread "Unconstrained build operations Thread 16"        [_thread_blocked, id=15408, stack(0x00000012c0600000,0x00000012c0700000) (1024K)]
  0x00000141e8266730 JavaThread "Unconstrained build operations Thread 17"        [_thread_blocked, id=7248, stack(0x00000012c0700000,0x00000012c0800000) (1024K)]
  0x00000141e8267ae0 JavaThread "Unconstrained build operations Thread 18"        [_thread_blocked, id=3380, stack(0x00000012c0800000,0x00000012c0900000) (1024K)]
  0x00000141e826a240 JavaThread "Unconstrained build operations Thread 19"        [_thread_blocked, id=16892, stack(0x00000012c0900000,0x00000012c0a00000) (1024K)]
  0x00000141e8268e90 JavaThread "Unconstrained build operations Thread 20"        [_thread_blocked, id=7396, stack(0x00000012c0a00000,0x00000012c0b00000) (1024K)]
  0x00000141e8266dc0 JavaThread "Unconstrained build operations Thread 21"        [_thread_blocked, id=16360, stack(0x00000012c0b00000,0x00000012c0c00000) (1024K)]
  0x00000141e826c310 JavaThread "jar transforms Thread 4"           [_thread_blocked, id=11404, stack(0x00000012c0c00000,0x00000012c0d00000) (1024K)]
  0x00000141e8268800 JavaThread "jar transforms Thread 5"           [_thread_blocked, id=19072, stack(0x00000012c0d00000,0x00000012c0e00000) (1024K)]
  0x00000141e8269bb0 JavaThread "Memory manager"                    [_thread_blocked, id=18380, stack(0x00000012bd400000,0x00000012bd500000) (1024K)]
  0x00000141e957d060 JavaThread "jar transforms Thread 6"           [_thread_blocked, id=18840, stack(0x00000012c0f00000,0x00000012c1000000) (1024K)]
  0x00000141e8265a10 JavaThread "pool-3-thread-1"                   [_thread_blocked, id=15928, stack(0x00000012c1000000,0x00000012c1100000) (1024K)]
  0x00000141e826af60 JavaThread "build event listener"              [_thread_blocked, id=16912, stack(0x00000012c1100000,0x00000012c1200000) (1024K)]
  0x00000141e8268170 JavaThread "included builds"                   [_thread_blocked, id=14008, stack(0x00000012c1200000,0x00000012c1300000) (1024K)]
  0x00000141ebd85cd0 JavaThread "Execution worker"                  [_thread_blocked, id=6540, stack(0x00000012c1300000,0x00000012c1400000) (1024K)]
  0x00000141ebd7f3d0 JavaThread "Execution worker Thread 2"         [_thread_blocked, id=5688, stack(0x00000012c1400000,0x00000012c1500000) (1024K)]
  0x00000141ebd83570 JavaThread "Execution worker Thread 3"         [_thread_blocked, id=17764, stack(0x00000012c1500000,0x00000012c1600000) (1024K)]
  0x00000141ebd814a0 JavaThread "Execution worker Thread 4"         [_thread_blocked, id=10412, stack(0x00000012c1600000,0x00000012c1700000) (1024K)]
  0x00000141ebd81b30 JavaThread "Execution worker Thread 5"         [_thread_blocked, id=2124, stack(0x00000012c1700000,0x00000012c1800000) (1024K)]
  0x00000141ebd83c00 JavaThread "Execution worker Thread 6"         [_thread_blocked, id=17748, stack(0x00000012c1800000,0x00000012c1900000) (1024K)]
  0x00000141ebd821c0 JavaThread "Execution worker Thread 7"         [_thread_blocked, id=2428, stack(0x00000012c1900000,0x00000012c1a00000) (1024K)]
  0x00000141ebd800f0 JavaThread "Cache worker for execution history cache (C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\.gradle\8.14.2\executionHistory)"        [_thread_blocked, id=8104, stack(0x00000012c1a00000,0x00000012c1b00000) (1024K)]
  0x00000141ebd84290 JavaThread "Unconstrained build operations Thread 22"        [_thread_blocked, id=9008, stack(0x00000012bd500000,0x00000012bd600000) (1024K)]
  0x00000141ebd82850 JavaThread "Unconstrained build operations Thread 23"        [_thread_blocked, id=7880, stack(0x00000012c0e00000,0x00000012c0f00000) (1024K)]
  0x00000141ebd7fa60 JavaThread "WorkerExecutor Queue"              [_thread_blocked, id=7300, stack(0x00000012c1b00000,0x00000012c1c00000) (1024K)]
  0x00000141ebd80780 JavaThread "Unconstrained build operations Thread 24"        [_thread_blocked, id=13364, stack(0x00000012c1d00000,0x00000012c1e00000) (1024K)]
  0x00000141ebd82ee0 JavaThread "Unconstrained build operations Thread 25"        [_thread_blocked, id=2436, stack(0x00000012c1e00000,0x00000012c1f00000) (1024K)]
  0x00000141ebd84fb0 JavaThread "Unconstrained build operations Thread 26"        [_thread_in_Java, id=16408, stack(0x00000012c1f00000,0x00000012c2000000) (1024K)]
  0x00000141ebd80e10 JavaThread "Unconstrained build operations Thread 27"        [_thread_blocked, id=18456, stack(0x00000012c2000000,0x00000012c2100000) (1024K)]
  0x00000141ebd85640 JavaThread "Unconstrained build operations Thread 28"        [_thread_blocked, id=14332, stack(0x00000012c2100000,0x00000012c2200000) (1024K)]
  0x00000141ebd7ed40 JavaThread "Unconstrained build operations Thread 29"        [_thread_blocked, id=6764, stack(0x00000012c2200000,0x00000012c2300000) (1024K)]
  0x00000141ebd86360 JavaThread "Unconstrained build operations Thread 30"        [_thread_blocked, id=2508, stack(0x00000012c2300000,0x00000012c2400000) (1024K)]
  0x00000141e656f140 JavaThread "WorkerExecutor Queue Thread 2"        [_thread_blocked, id=11200, stack(0x00000012c2500000,0x00000012c2600000) (1024K)]
  0x00000141e656eab0 JavaThread "Unconstrained build operations Thread 31"        [_thread_blocked, id=15216, stack(0x00000012c2600000,0x00000012c2700000) (1024K)]
  0x00000141e6571f30 JavaThread "Unconstrained build operations Thread 32"        [_thread_blocked, id=18516, stack(0x00000012c2700000,0x00000012c2800000) (1024K)]
  0x00000141e656dd90 JavaThread "Unconstrained build operations Thread 33"        [_thread_blocked, id=8508, stack(0x00000012c2800000,0x00000012c2900000) (1024K)]
  0x00000141e65718a0 JavaThread "Unconstrained build operations Thread 34"        [_thread_blocked, id=3996, stack(0x00000012c2900000,0x00000012c2a00000) (1024K)]
  0x00000141e6571210 JavaThread "Unconstrained build operations Thread 35"        [_thread_blocked, id=11328, stack(0x00000012c2a00000,0x00000012c2b00000) (1024K)]
  0x00000141e65725c0 JavaThread "Unconstrained build operations Thread 36"        [_thread_blocked, id=3012, stack(0x00000012c2b00000,0x00000012c2c00000) (1024K)]
  0x00000141e656e420 JavaThread "Unconstrained build operations Thread 37"        [_thread_blocked, id=18472, stack(0x00000012c2c00000,0x00000012c2d00000) (1024K)]
  0x00000141ec377d60 JavaThread "WorkerExecutor Queue Thread 4"        [_thread_blocked, id=10764, stack(0x00000012c2e00000,0x00000012c2f00000) (1024K)]
  0x00000141ec372810 JavaThread "WorkerExecutor Queue Thread 5"        [_thread_blocked, id=11248, stack(0x00000012c2f00000,0x00000012c3000000) (1024K)]
  0x00000141ec373bc0 JavaThread "WorkerExecutor Queue Thread 6"        [_thread_blocked, id=6912, stack(0x00000012c3000000,0x00000012c3100000) (1024K)]
  0x00000141ec3769b0 JavaThread "WorkerExecutor Queue Thread 7"        [_thread_blocked, id=9520, stack(0x00000012c3100000,0x00000012c3200000) (1024K)]
  0x00000141ec375600 JavaThread "WorkerExecutor Queue Thread 8"        [_thread_blocked, id=11520, stack(0x00000012c3200000,0x00000012c3300000) (1024K)]
  0x00000141ec371460 JavaThread "Java2D Disposer"            daemon [_thread_blocked, id=17112, stack(0x00000012c3300000,0x00000012c3400000) (1024K)]
  0x00000141e66b8650 JavaThread "Unconstrained build operations Thread 38"        [_thread_blocked, id=15784, stack(0x00000012c3400000,0x00000012c3500000) (1024K)]
  0x00000141e66bbad0 JavaThread "Unconstrained build operations Thread 39"        [_thread_blocked, id=14036, stack(0x00000012c3500000,0x00000012c3600000) (1024K)]
  0x00000141e66bc160 JavaThread "Unconstrained build operations Thread 40"        [_thread_blocked, id=10924, stack(0x00000012c3600000,0x00000012c3700000) (1024K)]
  0x00000141e66bc7f0 JavaThread "Unconstrained build operations Thread 41"        [_thread_blocked, id=18488, stack(0x00000012c3700000,0x00000012c3800000) (1024K)]
  0x00000141e66b5ef0 JavaThread "Unconstrained build operations Thread 42"        [_thread_blocked, id=496, stack(0x00000012c3800000,0x00000012c3900000) (1024K)]
  0x00000141e66bd510 JavaThread "Unconstrained build operations Thread 43"        [_thread_blocked, id=17164, stack(0x00000012c3900000,0x00000012c3a00000) (1024K)]
  0x00000141e66badb0 JavaThread "Unconstrained build operations Thread 44"        [_thread_blocked, id=16652, stack(0x00000012c3a00000,0x00000012c3b00000) (1024K)]
  0x00000141e66b7fc0 JavaThread "Unconstrained build operations Thread 45"        [_thread_blocked, id=15304, stack(0x00000012c3b00000,0x00000012c3c00000) (1024K)]
  0x00000141e66b72a0 JavaThread "Unconstrained build operations Thread 46"        [_thread_blocked, id=18832, stack(0x00000012c3c00000,0x00000012c3d00000) (1024K)]
  0x00000141e66bb440 JavaThread "Unconstrained build operations Thread 47"        [_thread_blocked, id=16456, stack(0x00000012c3d00000,0x00000012c3e00000) (1024K)]
  0x00000141e66bce80 JavaThread "Unconstrained build operations Thread 48"        [_thread_blocked, id=17088, stack(0x00000012c3e00000,0x00000012c3f00000) (1024K)]
  0x00000141e66b6580 JavaThread "Unconstrained build operations Thread 49"        [_thread_blocked, id=15740, stack(0x00000012c3f00000,0x00000012c4000000) (1024K)]
  0x00000141e66bdba0 JavaThread "Unconstrained build operations Thread 50"        [_thread_blocked, id=1588, stack(0x00000012c4000000,0x00000012c4100000) (1024K)]
  0x00000141e66b8ce0 JavaThread "Unconstrained build operations Thread 51"        [_thread_blocked, id=9164, stack(0x00000012c4100000,0x00000012c4200000) (1024K)]
  0x00000141e66b9370 JavaThread "Unconstrained build operations Thread 52"        [_thread_blocked, id=19276, stack(0x00000012c4200000,0x00000012c4300000) (1024K)]
  0x00000141e66b6c10 JavaThread "Unconstrained build operations Thread 53"        [_thread_blocked, id=14640, stack(0x00000012c4300000,0x00000012c4400000) (1024K)]
  0x00000141e66b9a00 JavaThread "Unconstrained build operations Thread 54"        [_thread_blocked, id=11168, stack(0x00000012c4400000,0x00000012c4500000) (1024K)]
  0x00000141e8759000 JavaThread "C2 CompilerThread1"         daemon [_thread_in_native, id=10940, stack(0x00000012c1c00000,0x00000012c1d00000) (1024K)]
  0x00000141e66be8c0 JavaThread "Unconstrained build operations Thread 55"        [_thread_blocked, id=15348, stack(0x00000012c2400000,0x00000012c2500000) (1024K)]
  0x00000141e66bef50 JavaThread "Unconstrained build operations Thread 56"        [_thread_blocked, id=11268, stack(0x00000012c4500000,0x00000012c4600000) (1024K)]
  0x00000141e66ba090 JavaThread "Unconstrained build operations Thread 57"        [_thread_blocked, id=16592, stack(0x00000012c4600000,0x00000012c4700000) (1024K)]
  0x00000141e66bf5e0 JavaThread "Unconstrained build operations Thread 58"        [_thread_blocked, id=4980, stack(0x00000012c4700000,0x00000012c4800000) (1024K)]
  0x00000141e66bfc70 JavaThread "Unconstrained build operations Thread 59"        [_thread_blocked, id=15528, stack(0x00000012c4800000,0x00000012c4900000) (1024K)]
  0x00000141e66c16b0 JavaThread "Unconstrained build operations Thread 60"        [_thread_blocked, id=5328, stack(0x00000012c4900000,0x00000012c4a00000) (1024K)]
  0x00000141e66c3e10 JavaThread "Unconstrained build operations Thread 61"        [_thread_blocked, id=10328, stack(0x00000012c4a00000,0x00000012c4b00000) (1024K)]
  0x00000141e66c51c0 JavaThread "WorkerExecutor Queue Thread 9"        [_thread_blocked, id=18844, stack(0x00000012c4b00000,0x00000012c4c00000) (1024K)]
  0x00000141e66c1d40 JavaThread "Unconstrained build operations Thread 62"        [_thread_blocked, id=2816, stack(0x00000012c4c00000,0x00000012c4d00000) (1024K)]
  0x00000141e66c4b30 JavaThread "Unconstrained build operations Thread 63"        [_thread_blocked, id=2608, stack(0x00000012c4d00000,0x00000012c4e00000) (1024K)]
  0x00000141e66c23d0 JavaThread "Unconstrained build operations Thread 64"        [_thread_blocked, id=2476, stack(0x00000012c4e00000,0x00000012c4f00000) (1024K)]
  0x00000141e66c2a60 JavaThread "Unconstrained build operations Thread 65"        [_thread_blocked, id=12648, stack(0x00000012c4f00000,0x00000012c5000000) (1024K)]
  0x00000141e66c30f0 JavaThread "Unconstrained build operations Thread 66"        [_thread_blocked, id=18436, stack(0x00000012c5000000,0x00000012c5100000) (1024K)]
  0x00000141e66c3780 JavaThread "Unconstrained build operations Thread 67"        [_thread_blocked, id=8464, stack(0x00000012c5100000,0x00000012c5200000) (1024K)]
  0x00000141e66c44a0 JavaThread "Unconstrained build operations Thread 68"        [_thread_blocked, id=6520, stack(0x00000012c5200000,0x00000012c5300000) (1024K)]
  0x00000141ee7265a0 JavaThread "Unconstrained build operations Thread 69"        [_thread_blocked, id=4812, stack(0x00000012c5400000,0x00000012c5500000) (1024K)]
  0x00000141ee72a740 JavaThread "Unconstrained build operations Thread 70"        [_thread_blocked, id=12740, stack(0x00000012c5500000,0x00000012c5600000) (1024K)]
  0x00000141ee727950 JavaThread "Unconstrained build operations Thread 71"        [_thread_blocked, id=10776, stack(0x00000012c5600000,0x00000012c5700000) (1024K)]
  0x00000141ee72add0 JavaThread "Unconstrained build operations Thread 72"        [_thread_blocked, id=6584, stack(0x00000012c5700000,0x00000012c5800000) (1024K)]
  0x00000141ee72b460 JavaThread "Unconstrained build operations Thread 73"        [_thread_blocked, id=252, stack(0x00000012c5800000,0x00000012c5900000) (1024K)]
  0x00000141ee72dbc0 JavaThread "Unconstrained build operations Thread 74"        [_thread_blocked, id=17696, stack(0x00000012c5900000,0x00000012c5a00000) (1024K)]
  0x00000141ee726c30 JavaThread "Unconstrained build operations Thread 75"        [_thread_blocked, id=15088, stack(0x00000012c5a00000,0x00000012c5b00000) (1024K)]
  0x00000141ee727fe0 JavaThread "Unconstrained build operations Thread 76"        [_thread_blocked, id=15540, stack(0x00000012c5b00000,0x00000012c5c00000) (1024K)]
  0x00000141ee72baf0 JavaThread "Unconstrained build operations Thread 77"        [_thread_blocked, id=10928, stack(0x00000012c5c00000,0x00000012c5d00000) (1024K)]
  0x00000141ee72c180 JavaThread "Unconstrained build operations Thread 78"        [_thread_blocked, id=12052, stack(0x00000012c5d00000,0x00000012c5e00000) (1024K)]
  0x00000141ee72c810 JavaThread "Unconstrained build operations Thread 79"        [_thread_blocked, id=16076, stack(0x00000012c5e00000,0x00000012c5f00000) (1024K)]
  0x00000141ee72cea0 JavaThread "Unconstrained build operations Thread 80"        [_thread_blocked, id=11664, stack(0x00000012c5f00000,0x00000012c6000000) (1024K)]
  0x00000141f60227f0 JavaThread "pool-4-thread-1"                   [_thread_blocked, id=11204, stack(0x00000012c6700000,0x00000012c6800000) (1024K)]
  0x00000141f6020090 JavaThread "stderr"                            [_thread_in_native, id=17920, stack(0x00000012c6800000,0x00000012c6900000) (1024K)]
  0x00000141f60276b0 JavaThread "stdout"                            [_thread_in_native, id=19140, stack(0x00000012c6900000,0x00000012c6a00000) (1024K)]
  0x00000141f6023510 JavaThread "stderr"                            [_thread_in_native, id=18156, stack(0x00000012c6a00000,0x00000012c6b00000) (1024K)]
  0x00000141f6028a60 JavaThread "stdout"                            [_thread_in_native, id=7924, stack(0x00000012c6b00000,0x00000012c6c00000) (1024K)]
  0x00000141f6029e10 JavaThread "stderr"                            [_thread_in_native, id=2884, stack(0x00000012c6c00000,0x00000012c6d00000) (1024K)]
  0x00000141f6023ba0 JavaThread "stdout"                            [_thread_in_native, id=11540, stack(0x00000012c6d00000,0x00000012c6e00000) (1024K)]
  0x00000141f6026300 JavaThread "stderr"                            [_thread_in_native, id=17636, stack(0x00000012c6e00000,0x00000012c6f00000) (1024K)]
  0x00000141f602ab30 JavaThread "stdout"                            [_thread_in_native, id=1500, stack(0x00000012c6f00000,0x00000012c7000000) (1024K)]
  0x00000141f602a4a0 JavaThread "stderr"                            [_thread_in_native, id=13484, stack(0x00000012c7000000,0x00000012c7100000) (1024K)]
  0x00000141f6026990 JavaThread "stdout"                            [_thread_in_native, id=12328, stack(0x00000012c7100000,0x00000012c7200000) (1024K)]
  0x00000141f6027d40 JavaThread "stderr"                            [_thread_in_native, id=5320, stack(0x00000012c7200000,0x00000012c7300000) (1024K)]
  0x00000141f6029780 JavaThread "stdout"                            [_thread_in_native, id=11184, stack(0x00000012c7300000,0x00000012c7400000) (1024K)]
  0x00000141f4d4cc20 JavaThread "C2 CompilerThread2"         daemon [_thread_in_native, id=15696, stack(0x00000012bbf00000,0x00000012bc000000) (1024K)]
Total: 155

Other Threads:
  0x00000141e1ba42d0 VMThread "VM Thread"                           [id=3700, stack(0x00000012bc900000,0x00000012bca00000) (1024K)]
  0x00000141e1b8d020 WatcherThread "VM Periodic Task Thread"        [id=15852, stack(0x00000012bc800000,0x00000012bc900000) (1024K)]
  0x00000141c5219e70 WorkerThread "GC Thread#0"                     [id=10060, stack(0x00000012bc300000,0x00000012bc400000) (1024K)]
  0x00000141e61ae790 WorkerThread "GC Thread#1"                     [id=19204, stack(0x00000012bd600000,0x00000012bd700000) (1024K)]
  0x00000141e61aeb30 WorkerThread "GC Thread#2"                     [id=17068, stack(0x00000012bd700000,0x00000012bd800000) (1024K)]
  0x00000141e61aeed0 WorkerThread "GC Thread#3"                     [id=15856, stack(0x00000012bd800000,0x00000012bd900000) (1024K)]
  0x00000141e61af270 WorkerThread "GC Thread#4"                     [id=16232, stack(0x00000012bd900000,0x00000012bda00000) (1024K)]
  0x00000141e1f2db90 WorkerThread "GC Thread#5"                     [id=14904, stack(0x00000012bda00000,0x00000012bdb00000) (1024K)]
  0x00000141e1f2df30 WorkerThread "GC Thread#6"                     [id=19236, stack(0x00000012bdb00000,0x00000012bdc00000) (1024K)]
  0x00000141e1f2e2d0 WorkerThread "GC Thread#7"                     [id=18800, stack(0x00000012bdc00000,0x00000012bdd00000) (1024K)]
  0x00000141c522be20 ConcurrentGCThread "G1 Main Marker"            [id=15520, stack(0x00000012bc400000,0x00000012bc500000) (1024K)]
  0x00000141c522d740 WorkerThread "G1 Conc#0"                       [id=732, stack(0x00000012bc500000,0x00000012bc600000) (1024K)]
  0x00000141e7c44280 WorkerThread "G1 Conc#1"                       [id=12892, stack(0x00000012bea00000,0x00000012beb00000) (1024K)]
  0x00000141e1ade260 ConcurrentGCThread "G1 Refine#0"               [id=17488, stack(0x00000012bc600000,0x00000012bc700000) (1024K)]
  0x00000141e1ae06a0 ConcurrentGCThread "G1 Service"                [id=17960, stack(0x00000012bc700000,0x00000012bc800000) (1024K)]
Total: 15

Threads with active compile tasks:
C2 CompilerThread0    73029 31718   !   4       com.android.tools.r8.internal.O90::<init> (942 bytes)
C2 CompilerThread1    73029 31766       4       com.android.tools.r8.internal.wM::a (1004 bytes)
C2 CompilerThread2    73029 31708       4       com.android.tools.r8.internal.Qb0::a (909 bytes)
Total: 3

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x0000000080000000, size: 2048 MB, Compressed Oops mode: 32-bit

CDS archive(s) mapped at: [0x0000014180000000-0x0000014180c90000-0x0000014180c90000), size 13172736, SharedBaseAddress: 0x0000014180000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x0000014181000000-0x00000141c1000000, reserved size: 1073741824
Narrow klass base: 0x0000014180000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CardTable entry size: 512
 Card Set container configuration: InlinePtr #cards 5 size 8 Array Of Cards #cards 12 size 40 Howl #buckets 4 coarsen threshold 1843 Howl Bitmap #cards 512 size 80 coarsen threshold 460 Card regions per heap region 1 cards per card region 2048
 CPUs: 8 total, 8 available
 Memory: 22476M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Heap Region Size: 1M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 352M
 Heap Max Capacity: 2G
 Pre-touch: Disabled
 Parallel Workers: 8
 Concurrent Workers: 2
 Concurrent Refinement Workers: 8
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 699392K, used 428422K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 203 young (207872K), 25 survivors (25600K)
 Metaspace       used 142228K, committed 144640K, reserved 1179648K
  class space    used 18692K, committed 19904K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, TAMS=top-at-mark-start, PB=parsable bottom
|   0|0x0000000080000000, 0x0000000080100000, 0x0000000080100000|100%|HS|  |TAMS 0x0000000080000000| PB 0x0000000080000000| Complete 
|   1|0x0000000080100000, 0x0000000080200000, 0x0000000080200000|100%|HC|  |TAMS 0x0000000080100000| PB 0x0000000080100000| Complete 
|   2|0x0000000080200000, 0x0000000080300000, 0x0000000080300000|100%|HC|  |TAMS 0x0000000080200000| PB 0x0000000080200000| Complete 
|   3|0x0000000080300000, 0x0000000080400000, 0x0000000080400000|100%| O|  |TAMS 0x0000000080300000| PB 0x0000000080300000| Untracked 
|   4|0x0000000080400000, 0x0000000080500000, 0x0000000080500000|100%| O|  |TAMS 0x0000000080400000| PB 0x0000000080400000| Untracked 
|   5|0x0000000080500000, 0x0000000080600000, 0x0000000080600000|100%| O|  |TAMS 0x0000000080500000| PB 0x0000000080500000| Untracked 
|   6|0x0000000080600000, 0x0000000080700000, 0x0000000080700000|100%|HS|  |TAMS 0x0000000080600000| PB 0x0000000080600000| Complete 
|   7|0x0000000080700000, 0x0000000080800000, 0x0000000080800000|100%| O|  |TAMS 0x0000000080700000| PB 0x0000000080700000| Untracked 
|   8|0x0000000080800000, 0x0000000080900000, 0x0000000080900000|100%| O|  |TAMS 0x0000000080800000| PB 0x0000000080800000| Untracked 
|   9|0x0000000080900000, 0x0000000080a00000, 0x0000000080a00000|100%| O|  |TAMS 0x0000000080900000| PB 0x0000000080900000| Untracked 
|  10|0x0000000080a00000, 0x0000000080b00000, 0x0000000080b00000|100%| O|  |TAMS 0x0000000080a00000| PB 0x0000000080a00000| Untracked 
|  11|0x0000000080b00000, 0x0000000080c00000, 0x0000000080c00000|100%| O|  |TAMS 0x0000000080b00000| PB 0x0000000080b00000| Untracked 
|  12|0x0000000080c00000, 0x0000000080d00000, 0x0000000080d00000|100%| O|  |TAMS 0x0000000080c00000| PB 0x0000000080c00000| Untracked 
|  13|0x0000000080d00000, 0x0000000080e00000, 0x0000000080e00000|100%| O|  |TAMS 0x0000000080d00000| PB 0x0000000080d00000| Untracked 
|  14|0x0000000080e00000, 0x0000000080f00000, 0x0000000080f00000|100%| O|  |TAMS 0x0000000080e00000| PB 0x0000000080e00000| Untracked 
|  15|0x0000000080f00000, 0x0000000081000000, 0x0000000081000000|100%| O|  |TAMS 0x0000000080f00000| PB 0x0000000080f00000| Untracked 
|  16|0x0000000081000000, 0x0000000081100000, 0x0000000081100000|100%| O|  |TAMS 0x0000000081000000| PB 0x0000000081000000| Untracked 
|  17|0x0000000081100000, 0x0000000081200000, 0x0000000081200000|100%| O|  |TAMS 0x0000000081100000| PB 0x0000000081100000| Untracked 
|  18|0x0000000081200000, 0x0000000081300000, 0x0000000081300000|100%| O|  |TAMS 0x0000000081200000| PB 0x0000000081200000| Untracked 
|  19|0x0000000081300000, 0x0000000081400000, 0x0000000081400000|100%| O|  |TAMS 0x0000000081300000| PB 0x0000000081300000| Untracked 
|  20|0x0000000081400000, 0x0000000081500000, 0x0000000081500000|100%| O|  |TAMS 0x0000000081400000| PB 0x0000000081400000| Untracked 
|  21|0x0000000081500000, 0x0000000081600000, 0x0000000081600000|100%| O|  |TAMS 0x0000000081500000| PB 0x0000000081500000| Untracked 
|  22|0x0000000081600000, 0x0000000081700000, 0x0000000081700000|100%| O|  |TAMS 0x0000000081600000| PB 0x0000000081600000| Untracked 
|  23|0x0000000081700000, 0x0000000081800000, 0x0000000081800000|100%| O|  |TAMS 0x0000000081700000| PB 0x0000000081700000| Untracked 
|  24|0x0000000081800000, 0x0000000081900000, 0x0000000081900000|100%| O|  |TAMS 0x0000000081800000| PB 0x0000000081800000| Untracked 
|  25|0x0000000081900000, 0x0000000081a00000, 0x0000000081a00000|100%| O|  |TAMS 0x0000000081900000| PB 0x0000000081900000| Untracked 
|  26|0x0000000081a00000, 0x0000000081b00000, 0x0000000081b00000|100%| O|  |TAMS 0x0000000081a00000| PB 0x0000000081a00000| Untracked 
|  27|0x0000000081b00000, 0x0000000081c00000, 0x0000000081c00000|100%| O|  |TAMS 0x0000000081b00000| PB 0x0000000081b00000| Untracked 
|  28|0x0000000081c00000, 0x0000000081d00000, 0x0000000081d00000|100%| O|  |TAMS 0x0000000081c00000| PB 0x0000000081c00000| Untracked 
|  29|0x0000000081d00000, 0x0000000081e00000, 0x0000000081e00000|100%| O|  |TAMS 0x0000000081d00000| PB 0x0000000081d00000| Untracked 
|  30|0x0000000081e00000, 0x0000000081f00000, 0x0000000081f00000|100%| O|  |TAMS 0x0000000081e00000| PB 0x0000000081e00000| Untracked 
|  31|0x0000000081f00000, 0x0000000082000000, 0x0000000082000000|100%| O|  |TAMS 0x0000000081f00000| PB 0x0000000081f00000| Untracked 
|  32|0x0000000082000000, 0x0000000082100000, 0x0000000082100000|100%| O|  |TAMS 0x0000000082000000| PB 0x0000000082000000| Untracked 
|  33|0x0000000082100000, 0x0000000082200000, 0x0000000082200000|100%| O|  |TAMS 0x0000000082100000| PB 0x0000000082100000| Untracked 
|  34|0x0000000082200000, 0x0000000082300000, 0x0000000082300000|100%| O|  |TAMS 0x0000000082200000| PB 0x0000000082200000| Untracked 
|  35|0x0000000082300000, 0x0000000082400000, 0x0000000082400000|100%| O|  |TAMS 0x0000000082300000| PB 0x0000000082300000| Untracked 
|  36|0x0000000082400000, 0x0000000082500000, 0x0000000082500000|100%| O|  |TAMS 0x0000000082400000| PB 0x0000000082400000| Untracked 
|  37|0x0000000082500000, 0x0000000082600000, 0x0000000082600000|100%| O|  |TAMS 0x0000000082500000| PB 0x0000000082500000| Untracked 
|  38|0x0000000082600000, 0x0000000082700000, 0x0000000082700000|100%| O|  |TAMS 0x0000000082600000| PB 0x0000000082600000| Untracked 
|  39|0x0000000082700000, 0x0000000082800000, 0x0000000082800000|100%| O|  |TAMS 0x0000000082700000| PB 0x0000000082700000| Untracked 
|  40|0x0000000082800000, 0x0000000082900000, 0x0000000082900000|100%| O|  |TAMS 0x0000000082800000| PB 0x0000000082800000| Untracked 
|  41|0x0000000082900000, 0x0000000082a00000, 0x0000000082a00000|100%| O|  |TAMS 0x0000000082900000| PB 0x0000000082900000| Untracked 
|  42|0x0000000082a00000, 0x0000000082b00000, 0x0000000082b00000|100%| O|  |TAMS 0x0000000082a00000| PB 0x0000000082a00000| Untracked 
|  43|0x0000000082b00000, 0x0000000082c00000, 0x0000000082c00000|100%|HS|  |TAMS 0x0000000082b00000| PB 0x0000000082b00000| Complete 
|  44|0x0000000082c00000, 0x0000000082d00000, 0x0000000082d00000|100%| O|  |TAMS 0x0000000082c00000| PB 0x0000000082c00000| Untracked 
|  45|0x0000000082d00000, 0x0000000082e00000, 0x0000000082e00000|100%| O|  |TAMS 0x0000000082d00000| PB 0x0000000082d00000| Untracked 
|  46|0x0000000082e00000, 0x0000000082f00000, 0x0000000082f00000|100%| O|  |TAMS 0x0000000082e00000| PB 0x0000000082e00000| Untracked 
|  47|0x0000000082f00000, 0x0000000083000000, 0x0000000083000000|100%| O|  |TAMS 0x0000000082f00000| PB 0x0000000082f00000| Untracked 
|  48|0x0000000083000000, 0x0000000083100000, 0x0000000083100000|100%|HS|  |TAMS 0x0000000083000000| PB 0x0000000083000000| Complete 
|  49|0x0000000083100000, 0x0000000083200000, 0x0000000083200000|100%|HS|  |TAMS 0x0000000083100000| PB 0x0000000083100000| Complete 
|  50|0x0000000083200000, 0x0000000083300000, 0x0000000083300000|100%|HS|  |TAMS 0x0000000083200000| PB 0x0000000083200000| Complete 
|  51|0x0000000083300000, 0x0000000083400000, 0x0000000083400000|100%|HS|  |TAMS 0x0000000083300000| PB 0x0000000083300000| Complete 
|  52|0x0000000083400000, 0x0000000083500000, 0x0000000083500000|100%| O|  |TAMS 0x0000000083400000| PB 0x0000000083400000| Untracked 
|  53|0x0000000083500000, 0x0000000083600000, 0x0000000083600000|100%|HS|  |TAMS 0x0000000083500000| PB 0x0000000083500000| Complete 
|  54|0x0000000083600000, 0x0000000083700000, 0x0000000083700000|100%| O|  |TAMS 0x0000000083600000| PB 0x0000000083600000| Untracked 
|  55|0x0000000083700000, 0x0000000083800000, 0x0000000083800000|100%| O|  |TAMS 0x0000000083700000| PB 0x0000000083700000| Untracked 
|  56|0x0000000083800000, 0x0000000083900000, 0x0000000083900000|100%| O|  |TAMS 0x0000000083800000| PB 0x0000000083800000| Untracked 
|  57|0x0000000083900000, 0x0000000083a00000, 0x0000000083a00000|100%| O|  |TAMS 0x0000000083900000| PB 0x0000000083900000| Untracked 
|  58|0x0000000083a00000, 0x0000000083b00000, 0x0000000083b00000|100%| O|  |TAMS 0x0000000083a00000| PB 0x0000000083a00000| Untracked 
|  59|0x0000000083b00000, 0x0000000083c00000, 0x0000000083c00000|100%| O|  |TAMS 0x0000000083b00000| PB 0x0000000083b00000| Untracked 
|  60|0x0000000083c00000, 0x0000000083d00000, 0x0000000083d00000|100%| O|  |TAMS 0x0000000083c00000| PB 0x0000000083c00000| Untracked 
|  61|0x0000000083d00000, 0x0000000083e00000, 0x0000000083e00000|100%| O|  |TAMS 0x0000000083d00000| PB 0x0000000083d00000| Untracked 
|  62|0x0000000083e00000, 0x0000000083f00000, 0x0000000083f00000|100%| O|  |TAMS 0x0000000083e00000| PB 0x0000000083e00000| Untracked 
|  63|0x0000000083f00000, 0x0000000084000000, 0x0000000084000000|100%| O|  |TAMS 0x0000000083f00000| PB 0x0000000083f00000| Untracked 
|  64|0x0000000084000000, 0x0000000084100000, 0x0000000084100000|100%| O|  |TAMS 0x0000000084000000| PB 0x0000000084000000| Untracked 
|  65|0x0000000084100000, 0x0000000084200000, 0x0000000084200000|100%| O|  |TAMS 0x0000000084100000| PB 0x0000000084100000| Untracked 
|  66|0x0000000084200000, 0x0000000084300000, 0x0000000084300000|100%| O|  |TAMS 0x0000000084200000| PB 0x0000000084200000| Untracked 
|  67|0x0000000084300000, 0x0000000084400000, 0x0000000084400000|100%| O|  |TAMS 0x0000000084300000| PB 0x0000000084300000| Untracked 
|  68|0x0000000084400000, 0x0000000084500000, 0x0000000084500000|100%| O|  |TAMS 0x0000000084400000| PB 0x0000000084400000| Untracked 
|  69|0x0000000084500000, 0x0000000084600000, 0x0000000084600000|100%| O|  |TAMS 0x0000000084500000| PB 0x0000000084500000| Untracked 
|  70|0x0000000084600000, 0x0000000084700000, 0x0000000084700000|100%|HS|  |TAMS 0x0000000084600000| PB 0x0000000084600000| Complete 
|  71|0x0000000084700000, 0x0000000084800000, 0x0000000084800000|100%| O|  |TAMS 0x0000000084700000| PB 0x0000000084700000| Untracked 
|  72|0x0000000084800000, 0x0000000084900000, 0x0000000084900000|100%| O|  |TAMS 0x0000000084800000| PB 0x0000000084800000| Untracked 
|  73|0x0000000084900000, 0x0000000084a00000, 0x0000000084a00000|100%| O|  |TAMS 0x0000000084900000| PB 0x0000000084900000| Untracked 
|  74|0x0000000084a00000, 0x0000000084b00000, 0x0000000084b00000|100%| O|  |TAMS 0x0000000084a00000| PB 0x0000000084a00000| Untracked 
|  75|0x0000000084b00000, 0x0000000084c00000, 0x0000000084c00000|100%| O|  |TAMS 0x0000000084b00000| PB 0x0000000084b00000| Untracked 
|  76|0x0000000084c00000, 0x0000000084d00000, 0x0000000084d00000|100%| O|  |TAMS 0x0000000084c00000| PB 0x0000000084c00000| Untracked 
|  77|0x0000000084d00000, 0x0000000084e00000, 0x0000000084e00000|100%| O|  |TAMS 0x0000000084d00000| PB 0x0000000084d00000| Untracked 
|  78|0x0000000084e00000, 0x0000000084f00000, 0x0000000084f00000|100%| O|  |TAMS 0x0000000084e00000| PB 0x0000000084e00000| Untracked 
|  79|0x0000000084f00000, 0x0000000085000000, 0x0000000085000000|100%| O|  |TAMS 0x0000000084f00000| PB 0x0000000084f00000| Untracked 
|  80|0x0000000085000000, 0x0000000085100000, 0x0000000085100000|100%| O|  |TAMS 0x0000000085000000| PB 0x0000000085000000| Untracked 
|  81|0x0000000085100000, 0x0000000085200000, 0x0000000085200000|100%| O|  |TAMS 0x0000000085100000| PB 0x0000000085100000| Untracked 
|  82|0x0000000085200000, 0x0000000085300000, 0x0000000085300000|100%| O|  |TAMS 0x0000000085200000| PB 0x0000000085200000| Untracked 
|  83|0x0000000085300000, 0x0000000085400000, 0x0000000085400000|100%| O|  |TAMS 0x0000000085300000| PB 0x0000000085300000| Untracked 
|  84|0x0000000085400000, 0x0000000085500000, 0x0000000085500000|100%| O|  |TAMS 0x0000000085400000| PB 0x0000000085400000| Untracked 
|  85|0x0000000085500000, 0x0000000085600000, 0x0000000085600000|100%| O|  |TAMS 0x0000000085500000| PB 0x0000000085500000| Untracked 
|  86|0x0000000085600000, 0x0000000085700000, 0x0000000085700000|100%| O|  |TAMS 0x0000000085600000| PB 0x0000000085600000| Untracked 
|  87|0x0000000085700000, 0x0000000085800000, 0x0000000085800000|100%| O|  |TAMS 0x0000000085700000| PB 0x0000000085700000| Untracked 
|  88|0x0000000085800000, 0x0000000085900000, 0x0000000085900000|100%| O|  |TAMS 0x0000000085800000| PB 0x0000000085800000| Untracked 
|  89|0x0000000085900000, 0x0000000085a00000, 0x0000000085a00000|100%| O|  |TAMS 0x0000000085900000| PB 0x0000000085900000| Untracked 
|  90|0x0000000085a00000, 0x0000000085b00000, 0x0000000085b00000|100%| O|  |TAMS 0x0000000085a00000| PB 0x0000000085a00000| Untracked 
|  91|0x0000000085b00000, 0x0000000085c00000, 0x0000000085c00000|100%| O|  |TAMS 0x0000000085b00000| PB 0x0000000085b00000| Untracked 
|  92|0x0000000085c00000, 0x0000000085d00000, 0x0000000085d00000|100%| O|  |TAMS 0x0000000085c00000| PB 0x0000000085c00000| Untracked 
|  93|0x0000000085d00000, 0x0000000085e00000, 0x0000000085e00000|100%| O|  |TAMS 0x0000000085d00000| PB 0x0000000085d00000| Untracked 
|  94|0x0000000085e00000, 0x0000000085f00000, 0x0000000085f00000|100%| O|  |TAMS 0x0000000085e00000| PB 0x0000000085e00000| Untracked 
|  95|0x0000000085f00000, 0x0000000086000000, 0x0000000086000000|100%| O|  |TAMS 0x0000000085f00000| PB 0x0000000085f00000| Untracked 
|  96|0x0000000086000000, 0x0000000086100000, 0x0000000086100000|100%| O|  |TAMS 0x0000000086000000| PB 0x0000000086000000| Untracked 
|  97|0x0000000086100000, 0x0000000086200000, 0x0000000086200000|100%| O|  |TAMS 0x0000000086100000| PB 0x0000000086100000| Untracked 
|  98|0x0000000086200000, 0x0000000086300000, 0x0000000086300000|100%| O|  |TAMS 0x0000000086200000| PB 0x0000000086200000| Untracked 
|  99|0x0000000086300000, 0x0000000086400000, 0x0000000086400000|100%| O|  |TAMS 0x0000000086300000| PB 0x0000000086300000| Untracked 
| 100|0x0000000086400000, 0x0000000086500000, 0x0000000086500000|100%| O|  |TAMS 0x0000000086400000| PB 0x0000000086400000| Untracked 
| 101|0x0000000086500000, 0x0000000086600000, 0x0000000086600000|100%| O|  |TAMS 0x0000000086500000| PB 0x0000000086500000| Untracked 
| 102|0x0000000086600000, 0x0000000086700000, 0x0000000086700000|100%| O|  |TAMS 0x0000000086600000| PB 0x0000000086600000| Untracked 
| 103|0x0000000086700000, 0x0000000086800000, 0x0000000086800000|100%| O|  |TAMS 0x0000000086700000| PB 0x0000000086700000| Untracked 
| 104|0x0000000086800000, 0x0000000086900000, 0x0000000086900000|100%| O|  |TAMS 0x0000000086800000| PB 0x0000000086800000| Untracked 
| 105|0x0000000086900000, 0x0000000086a00000, 0x0000000086a00000|100%| O|  |TAMS 0x0000000086900000| PB 0x0000000086900000| Untracked 
| 106|0x0000000086a00000, 0x0000000086b00000, 0x0000000086b00000|100%| O|  |TAMS 0x0000000086a00000| PB 0x0000000086a00000| Untracked 
| 107|0x0000000086b00000, 0x0000000086c00000, 0x0000000086c00000|100%| O|  |TAMS 0x0000000086b00000| PB 0x0000000086b00000| Untracked 
| 108|0x0000000086c00000, 0x0000000086d00000, 0x0000000086d00000|100%| O|  |TAMS 0x0000000086c00000| PB 0x0000000086c00000| Untracked 
| 109|0x0000000086d00000, 0x0000000086e00000, 0x0000000086e00000|100%| O|  |TAMS 0x0000000086d00000| PB 0x0000000086d00000| Untracked 
| 110|0x0000000086e00000, 0x0000000086f00000, 0x0000000086f00000|100%| O|  |TAMS 0x0000000086e00000| PB 0x0000000086e00000| Untracked 
| 111|0x0000000086f00000, 0x0000000087000000, 0x0000000087000000|100%| O|  |TAMS 0x0000000086f00000| PB 0x0000000086f00000| Untracked 
| 112|0x0000000087000000, 0x0000000087100000, 0x0000000087100000|100%| O|  |TAMS 0x0000000087000000| PB 0x0000000087000000| Untracked 
| 113|0x0000000087100000, 0x0000000087200000, 0x0000000087200000|100%| O|  |TAMS 0x0000000087100000| PB 0x0000000087100000| Untracked 
| 114|0x0000000087200000, 0x0000000087300000, 0x0000000087300000|100%| O|  |TAMS 0x0000000087200000| PB 0x0000000087200000| Untracked 
| 115|0x0000000087300000, 0x0000000087400000, 0x0000000087400000|100%| O|  |TAMS 0x0000000087300000| PB 0x0000000087300000| Untracked 
| 116|0x0000000087400000, 0x0000000087500000, 0x0000000087500000|100%|HS|  |TAMS 0x0000000087400000| PB 0x0000000087400000| Complete 
| 117|0x0000000087500000, 0x0000000087600000, 0x0000000087600000|100%| O|  |TAMS 0x0000000087500000| PB 0x0000000087500000| Untracked 
| 118|0x0000000087600000, 0x0000000087700000, 0x0000000087700000|100%| O|  |TAMS 0x0000000087600000| PB 0x0000000087600000| Untracked 
| 119|0x0000000087700000, 0x0000000087800000, 0x0000000087800000|100%| O|  |TAMS 0x0000000087700000| PB 0x0000000087700000| Untracked 
| 120|0x0000000087800000, 0x0000000087900000, 0x0000000087900000|100%| O|  |TAMS 0x0000000087800000| PB 0x0000000087800000| Untracked 
| 121|0x0000000087900000, 0x0000000087a00000, 0x0000000087a00000|100%| O|  |TAMS 0x0000000087900000| PB 0x0000000087900000| Untracked 
| 122|0x0000000087a00000, 0x0000000087b00000, 0x0000000087b00000|100%| O|  |TAMS 0x0000000087a00000| PB 0x0000000087a00000| Untracked 
| 123|0x0000000087b00000, 0x0000000087c00000, 0x0000000087c00000|100%| O|  |TAMS 0x0000000087b00000| PB 0x0000000087b00000| Untracked 
| 124|0x0000000087c00000, 0x0000000087d00000, 0x0000000087d00000|100%| O|  |TAMS 0x0000000087c00000| PB 0x0000000087c00000| Untracked 
| 125|0x0000000087d00000, 0x0000000087e00000, 0x0000000087e00000|100%|HS|  |TAMS 0x0000000087d00000| PB 0x0000000087d00000| Complete 
| 126|0x0000000087e00000, 0x0000000087f00000, 0x0000000087f00000|100%| O|  |TAMS 0x0000000087e00000| PB 0x0000000087e00000| Untracked 
| 127|0x0000000087f00000, 0x0000000088000000, 0x0000000088000000|100%| O|  |TAMS 0x0000000087f00000| PB 0x0000000087f00000| Untracked 
| 128|0x0000000088000000, 0x0000000088100000, 0x0000000088100000|100%| O|  |TAMS 0x0000000088000000| PB 0x0000000088000000| Untracked 
| 129|0x0000000088100000, 0x0000000088200000, 0x0000000088200000|100%| O|  |TAMS 0x0000000088100000| PB 0x0000000088100000| Untracked 
| 130|0x0000000088200000, 0x00000000882e1960, 0x0000000088300000| 88%| O|  |TAMS 0x0000000088200000| PB 0x0000000088200000| Untracked 
| 131|0x0000000088300000, 0x0000000088400000, 0x0000000088400000|100%| O|  |TAMS 0x0000000088300000| PB 0x0000000088300000| Untracked 
| 132|0x0000000088400000, 0x0000000088400000, 0x0000000088500000|  0%| F|  |TAMS 0x0000000088400000| PB 0x0000000088400000| Untracked 
| 133|0x0000000088500000, 0x0000000088500000, 0x0000000088600000|  0%| F|  |TAMS 0x0000000088500000| PB 0x0000000088500000| Untracked 
| 134|0x0000000088600000, 0x0000000088600000, 0x0000000088700000|  0%| F|  |TAMS 0x0000000088600000| PB 0x0000000088600000| Untracked 
| 135|0x0000000088700000, 0x0000000088700000, 0x0000000088800000|  0%| F|  |TAMS 0x0000000088700000| PB 0x0000000088700000| Untracked 
| 136|0x0000000088800000, 0x0000000088900000, 0x0000000088900000|100%| O|  |TAMS 0x0000000088800000| PB 0x0000000088800000| Untracked 
| 137|0x0000000088900000, 0x0000000088900000, 0x0000000088a00000|  0%| F|  |TAMS 0x0000000088900000| PB 0x0000000088900000| Untracked 
| 138|0x0000000088a00000, 0x0000000088b00000, 0x0000000088b00000|100%| O|  |TAMS 0x0000000088a00000| PB 0x0000000088a00000| Untracked 
| 139|0x0000000088b00000, 0x0000000088c00000, 0x0000000088c00000|100%| O|  |TAMS 0x0000000088b00000| PB 0x0000000088b00000| Untracked 
| 140|0x0000000088c00000, 0x0000000088d00000, 0x0000000088d00000|100%| O|  |TAMS 0x0000000088c00000| PB 0x0000000088c00000| Untracked 
| 141|0x0000000088d00000, 0x0000000088e00000, 0x0000000088e00000|100%| O|  |TAMS 0x0000000088d00000| PB 0x0000000088d00000| Untracked 
| 142|0x0000000088e00000, 0x0000000088f00000, 0x0000000088f00000|100%| O|  |TAMS 0x0000000088e00000| PB 0x0000000088e00000| Untracked 
| 143|0x0000000088f00000, 0x0000000089000000, 0x0000000089000000|100%| O|  |TAMS 0x0000000088f00000| PB 0x0000000088f00000| Untracked 
| 144|0x0000000089000000, 0x0000000089000000, 0x0000000089100000|  0%| F|  |TAMS 0x0000000089000000| PB 0x0000000089000000| Untracked 
| 145|0x0000000089100000, 0x0000000089200000, 0x0000000089200000|100%| O|  |TAMS 0x0000000089100000| PB 0x0000000089100000| Untracked 
| 146|0x0000000089200000, 0x0000000089300000, 0x0000000089300000|100%| O|  |TAMS 0x0000000089200000| PB 0x0000000089200000| Untracked 
| 147|0x0000000089300000, 0x0000000089400000, 0x0000000089400000|100%| O|  |TAMS 0x0000000089300000| PB 0x0000000089300000| Untracked 
| 148|0x0000000089400000, 0x0000000089500000, 0x0000000089500000|100%| O|  |TAMS 0x0000000089400000| PB 0x0000000089400000| Untracked 
| 149|0x0000000089500000, 0x0000000089600000, 0x0000000089600000|100%| O|  |TAMS 0x0000000089500000| PB 0x0000000089500000| Untracked 
| 150|0x0000000089600000, 0x0000000089700000, 0x0000000089700000|100%| O|  |TAMS 0x0000000089600000| PB 0x0000000089600000| Untracked 
| 151|0x0000000089700000, 0x0000000089700000, 0x0000000089800000|  0%| F|  |TAMS 0x0000000089700000| PB 0x0000000089700000| Untracked 
| 152|0x0000000089800000, 0x0000000089900000, 0x0000000089900000|100%| O|  |TAMS 0x0000000089800000| PB 0x0000000089800000| Untracked 
| 153|0x0000000089900000, 0x0000000089a00000, 0x0000000089a00000|100%| O|  |TAMS 0x0000000089900000| PB 0x0000000089900000| Untracked 
| 154|0x0000000089a00000, 0x0000000089a00000, 0x0000000089b00000|  0%| F|  |TAMS 0x0000000089a00000| PB 0x0000000089a00000| Untracked 
| 155|0x0000000089b00000, 0x0000000089b00000, 0x0000000089c00000|  0%| F|  |TAMS 0x0000000089b00000| PB 0x0000000089b00000| Untracked 
| 156|0x0000000089c00000, 0x0000000089d00000, 0x0000000089d00000|100%| O|  |TAMS 0x0000000089c00000| PB 0x0000000089c00000| Untracked 
| 157|0x0000000089d00000, 0x0000000089e00000, 0x0000000089e00000|100%| O|  |TAMS 0x0000000089d00000| PB 0x0000000089d00000| Untracked 
| 158|0x0000000089e00000, 0x0000000089f00000, 0x0000000089f00000|100%| O|  |TAMS 0x0000000089e00000| PB 0x0000000089e00000| Untracked 
| 159|0x0000000089f00000, 0x0000000089f00000, 0x000000008a000000|  0%| F|  |TAMS 0x0000000089f00000| PB 0x0000000089f00000| Untracked 
| 160|0x000000008a000000, 0x000000008a100000, 0x000000008a100000|100%| O|  |TAMS 0x000000008a000000| PB 0x000000008a000000| Untracked 
| 161|0x000000008a100000, 0x000000008a200000, 0x000000008a200000|100%| O|  |TAMS 0x000000008a100000| PB 0x000000008a100000| Untracked 
| 162|0x000000008a200000, 0x000000008a300000, 0x000000008a300000|100%| O|  |TAMS 0x000000008a200000| PB 0x000000008a200000| Untracked 
| 163|0x000000008a300000, 0x000000008a300000, 0x000000008a400000|  0%| F|  |TAMS 0x000000008a300000| PB 0x000000008a300000| Untracked 
| 164|0x000000008a400000, 0x000000008a500000, 0x000000008a500000|100%| O|  |TAMS 0x000000008a400000| PB 0x000000008a400000| Untracked 
| 165|0x000000008a500000, 0x000000008a600000, 0x000000008a600000|100%| O|  |TAMS 0x000000008a500000| PB 0x000000008a500000| Untracked 
| 166|0x000000008a600000, 0x000000008a700000, 0x000000008a700000|100%|HS|  |TAMS 0x000000008a600000| PB 0x000000008a600000| Complete 
| 167|0x000000008a700000, 0x000000008a800000, 0x000000008a800000|100%|HC|  |TAMS 0x000000008a700000| PB 0x000000008a700000| Complete 
| 168|0x000000008a800000, 0x000000008a900000, 0x000000008a900000|100%| O|  |TAMS 0x000000008a800000| PB 0x000000008a800000| Untracked 
| 169|0x000000008a900000, 0x000000008a900000, 0x000000008aa00000|  0%| F|  |TAMS 0x000000008a900000| PB 0x000000008a900000| Untracked 
| 170|0x000000008aa00000, 0x000000008ab00000, 0x000000008ab00000|100%| O|  |TAMS 0x000000008aa00000| PB 0x000000008aa00000| Untracked 
| 171|0x000000008ab00000, 0x000000008ac00000, 0x000000008ac00000|100%| O|  |TAMS 0x000000008ab00000| PB 0x000000008ab00000| Untracked 
| 172|0x000000008ac00000, 0x000000008ad00000, 0x000000008ad00000|100%| O|  |TAMS 0x000000008ac00000| PB 0x000000008ac00000| Untracked 
| 173|0x000000008ad00000, 0x000000008ad00000, 0x000000008ae00000|  0%| F|  |TAMS 0x000000008ad00000| PB 0x000000008ad00000| Untracked 
| 174|0x000000008ae00000, 0x000000008af00000, 0x000000008af00000|100%| O|  |TAMS 0x000000008ae00000| PB 0x000000008ae00000| Untracked 
| 175|0x000000008af00000, 0x000000008b000000, 0x000000008b000000|100%| O|  |TAMS 0x000000008af00000| PB 0x000000008af00000| Untracked 
| 176|0x000000008b000000, 0x000000008b100000, 0x000000008b100000|100%| O|  |TAMS 0x000000008b000000| PB 0x000000008b000000| Untracked 
| 177|0x000000008b100000, 0x000000008b100000, 0x000000008b200000|  0%| F|  |TAMS 0x000000008b100000| PB 0x000000008b100000| Untracked 
| 178|0x000000008b200000, 0x000000008b200000, 0x000000008b300000|  0%| F|  |TAMS 0x000000008b200000| PB 0x000000008b200000| Untracked 
| 179|0x000000008b300000, 0x000000008b300000, 0x000000008b400000|  0%| F|  |TAMS 0x000000008b300000| PB 0x000000008b300000| Untracked 
| 180|0x000000008b400000, 0x000000008b500000, 0x000000008b500000|100%| O|  |TAMS 0x000000008b400000| PB 0x000000008b400000| Untracked 
| 181|0x000000008b500000, 0x000000008b600000, 0x000000008b600000|100%| O|  |TAMS 0x000000008b500000| PB 0x000000008b500000| Untracked 
| 182|0x000000008b600000, 0x000000008b600000, 0x000000008b700000|  0%| F|  |TAMS 0x000000008b600000| PB 0x000000008b600000| Untracked 
| 183|0x000000008b700000, 0x000000008b700000, 0x000000008b800000|  0%| F|  |TAMS 0x000000008b700000| PB 0x000000008b700000| Untracked 
| 184|0x000000008b800000, 0x000000008b800000, 0x000000008b900000|  0%| F|  |TAMS 0x000000008b800000| PB 0x000000008b800000| Untracked 
| 185|0x000000008b900000, 0x000000008b900000, 0x000000008ba00000|  0%| F|  |TAMS 0x000000008b900000| PB 0x000000008b900000| Untracked 
| 186|0x000000008ba00000, 0x000000008ba00000, 0x000000008bb00000|  0%| F|  |TAMS 0x000000008ba00000| PB 0x000000008ba00000| Untracked 
| 187|0x000000008bb00000, 0x000000008bc00000, 0x000000008bc00000|100%| O|  |TAMS 0x000000008bb00000| PB 0x000000008bb00000| Untracked 
| 188|0x000000008bc00000, 0x000000008bd00000, 0x000000008bd00000|100%| O|  |TAMS 0x000000008bc00000| PB 0x000000008bc00000| Untracked 
| 189|0x000000008bd00000, 0x000000008be00000, 0x000000008be00000|100%| O|  |TAMS 0x000000008bd00000| PB 0x000000008bd00000| Untracked 
| 190|0x000000008be00000, 0x000000008bf00000, 0x000000008bf00000|100%| O|  |TAMS 0x000000008be00000| PB 0x000000008be00000| Untracked 
| 191|0x000000008bf00000, 0x000000008c000000, 0x000000008c000000|100%| O|  |TAMS 0x000000008bf00000| PB 0x000000008bf00000| Untracked 
| 192|0x000000008c000000, 0x000000008c000000, 0x000000008c100000|  0%| F|  |TAMS 0x000000008c000000| PB 0x000000008c000000| Untracked 
| 193|0x000000008c100000, 0x000000008c100000, 0x000000008c200000|  0%| F|  |TAMS 0x000000008c100000| PB 0x000000008c100000| Untracked 
| 194|0x000000008c200000, 0x000000008c300000, 0x000000008c300000|100%| O|  |TAMS 0x000000008c200000| PB 0x000000008c200000| Untracked 
| 195|0x000000008c300000, 0x000000008c400000, 0x000000008c400000|100%| O|  |TAMS 0x000000008c300000| PB 0x000000008c300000| Untracked 
| 196|0x000000008c400000, 0x000000008c400000, 0x000000008c500000|  0%| F|  |TAMS 0x000000008c400000| PB 0x000000008c400000| Untracked 
| 197|0x000000008c500000, 0x000000008c600000, 0x000000008c600000|100%| O|  |TAMS 0x000000008c500000| PB 0x000000008c500000| Untracked 
| 198|0x000000008c600000, 0x000000008c600000, 0x000000008c700000|  0%| F|  |TAMS 0x000000008c600000| PB 0x000000008c600000| Untracked 
| 199|0x000000008c700000, 0x000000008c700000, 0x000000008c800000|  0%| F|  |TAMS 0x000000008c700000| PB 0x000000008c700000| Untracked 
| 200|0x000000008c800000, 0x000000008c800000, 0x000000008c900000|  0%| F|  |TAMS 0x000000008c800000| PB 0x000000008c800000| Untracked 
| 201|0x000000008c900000, 0x000000008c900000, 0x000000008ca00000|  0%| F|  |TAMS 0x000000008c900000| PB 0x000000008c900000| Untracked 
| 202|0x000000008ca00000, 0x000000008ca00000, 0x000000008cb00000|  0%| F|  |TAMS 0x000000008ca00000| PB 0x000000008ca00000| Untracked 
| 203|0x000000008cb00000, 0x000000008cb00000, 0x000000008cc00000|  0%| F|  |TAMS 0x000000008cb00000| PB 0x000000008cb00000| Untracked 
| 204|0x000000008cc00000, 0x000000008cc80000, 0x000000008cd00000| 50%| S|CS|TAMS 0x000000008cc00000| PB 0x000000008cc00000| Complete 
| 205|0x000000008cd00000, 0x000000008ce00000, 0x000000008ce00000|100%| S|CS|TAMS 0x000000008cd00000| PB 0x000000008cd00000| Complete 
| 206|0x000000008ce00000, 0x000000008ce00000, 0x000000008cf00000|  0%| F|  |TAMS 0x000000008ce00000| PB 0x000000008ce00000| Untracked 
| 207|0x000000008cf00000, 0x000000008d000000, 0x000000008d000000|100%| S|CS|TAMS 0x000000008cf00000| PB 0x000000008cf00000| Complete 
| 208|0x000000008d000000, 0x000000008d100000, 0x000000008d100000|100%| S|CS|TAMS 0x000000008d000000| PB 0x000000008d000000| Complete 
| 209|0x000000008d100000, 0x000000008d200000, 0x000000008d200000|100%| S|CS|TAMS 0x000000008d100000| PB 0x000000008d100000| Complete 
| 210|0x000000008d200000, 0x000000008d300000, 0x000000008d300000|100%| O|  |TAMS 0x000000008d200000| PB 0x000000008d200000| Untracked 
| 211|0x000000008d300000, 0x000000008d400000, 0x000000008d400000|100%| O|  |TAMS 0x000000008d300000| PB 0x000000008d300000| Untracked 
| 212|0x000000008d400000, 0x000000008d500000, 0x000000008d500000|100%| O|  |TAMS 0x000000008d400000| PB 0x000000008d400000| Untracked 
| 213|0x000000008d500000, 0x000000008d500000, 0x000000008d600000|  0%| F|  |TAMS 0x000000008d500000| PB 0x000000008d500000| Untracked 
| 214|0x000000008d600000, 0x000000008d600000, 0x000000008d700000|  0%| F|  |TAMS 0x000000008d600000| PB 0x000000008d600000| Untracked 
| 215|0x000000008d700000, 0x000000008d700000, 0x000000008d800000|  0%| F|  |TAMS 0x000000008d700000| PB 0x000000008d700000| Untracked 
| 216|0x000000008d800000, 0x000000008d900000, 0x000000008d900000|100%| S|CS|TAMS 0x000000008d800000| PB 0x000000008d800000| Complete 
| 217|0x000000008d900000, 0x000000008d900000, 0x000000008da00000|  0%| F|  |TAMS 0x000000008d900000| PB 0x000000008d900000| Untracked 
| 218|0x000000008da00000, 0x000000008db00000, 0x000000008db00000|100%| S|CS|TAMS 0x000000008da00000| PB 0x000000008da00000| Complete 
| 219|0x000000008db00000, 0x000000008dc00000, 0x000000008dc00000|100%| O|  |TAMS 0x000000008db00000| PB 0x000000008db00000| Untracked 
| 220|0x000000008dc00000, 0x000000008dd00000, 0x000000008dd00000|100%| S|CS|TAMS 0x000000008dc00000| PB 0x000000008dc00000| Complete 
| 221|0x000000008dd00000, 0x000000008de00000, 0x000000008de00000|100%| S|CS|TAMS 0x000000008dd00000| PB 0x000000008dd00000| Complete 
| 222|0x000000008de00000, 0x000000008df00000, 0x000000008df00000|100%| O|  |TAMS 0x000000008de00000| PB 0x000000008de00000| Untracked 
| 223|0x000000008df00000, 0x000000008e000000, 0x000000008e000000|100%| S|CS|TAMS 0x000000008df00000| PB 0x000000008df00000| Complete 
| 224|0x000000008e000000, 0x000000008e000000, 0x000000008e100000|  0%| F|  |TAMS 0x000000008e000000| PB 0x000000008e000000| Untracked 
| 225|0x000000008e100000, 0x000000008e100000, 0x000000008e200000|  0%| F|  |TAMS 0x000000008e100000| PB 0x000000008e100000| Untracked 
| 226|0x000000008e200000, 0x000000008e200000, 0x000000008e300000|  0%| F|  |TAMS 0x000000008e200000| PB 0x000000008e200000| Untracked 
| 227|0x000000008e300000, 0x000000008e300000, 0x000000008e400000|  0%| F|  |TAMS 0x000000008e300000| PB 0x000000008e300000| Untracked 
| 228|0x000000008e400000, 0x000000008e400000, 0x000000008e500000|  0%| F|  |TAMS 0x000000008e400000| PB 0x000000008e400000| Untracked 
| 229|0x000000008e500000, 0x000000008e500000, 0x000000008e600000|  0%| F|  |TAMS 0x000000008e500000| PB 0x000000008e500000| Untracked 
| 230|0x000000008e600000, 0x000000008e600000, 0x000000008e700000|  0%| F|  |TAMS 0x000000008e600000| PB 0x000000008e600000| Untracked 
| 231|0x000000008e700000, 0x000000008e700000, 0x000000008e800000|  0%| F|  |TAMS 0x000000008e700000| PB 0x000000008e700000| Untracked 
| 232|0x000000008e800000, 0x000000008e800000, 0x000000008e900000|  0%| F|  |TAMS 0x000000008e800000| PB 0x000000008e800000| Untracked 
| 233|0x000000008e900000, 0x000000008e900000, 0x000000008ea00000|  0%| F|  |TAMS 0x000000008e900000| PB 0x000000008e900000| Untracked 
| 234|0x000000008ea00000, 0x000000008ea00000, 0x000000008eb00000|  0%| F|  |TAMS 0x000000008ea00000| PB 0x000000008ea00000| Untracked 
| 235|0x000000008eb00000, 0x000000008eb00000, 0x000000008ec00000|  0%| F|  |TAMS 0x000000008eb00000| PB 0x000000008eb00000| Untracked 
| 236|0x000000008ec00000, 0x000000008ed00000, 0x000000008ed00000|100%| O|  |TAMS 0x000000008ec00000| PB 0x000000008ec00000| Untracked 
| 237|0x000000008ed00000, 0x000000008ed00000, 0x000000008ee00000|  0%| F|  |TAMS 0x000000008ed00000| PB 0x000000008ed00000| Untracked 
| 238|0x000000008ee00000, 0x000000008ee00000, 0x000000008ef00000|  0%| F|  |TAMS 0x000000008ee00000| PB 0x000000008ee00000| Untracked 
| 239|0x000000008ef00000, 0x000000008ef00000, 0x000000008f000000|  0%| F|  |TAMS 0x000000008ef00000| PB 0x000000008ef00000| Untracked 
| 240|0x000000008f000000, 0x000000008f000000, 0x000000008f100000|  0%| F|  |TAMS 0x000000008f000000| PB 0x000000008f000000| Untracked 
| 241|0x000000008f100000, 0x000000008f100000, 0x000000008f200000|  0%| F|  |TAMS 0x000000008f100000| PB 0x000000008f100000| Untracked 
| 242|0x000000008f200000, 0x000000008f200000, 0x000000008f300000|  0%| F|  |TAMS 0x000000008f200000| PB 0x000000008f200000| Untracked 
| 243|0x000000008f300000, 0x000000008f300000, 0x000000008f400000|  0%| F|  |TAMS 0x000000008f300000| PB 0x000000008f300000| Untracked 
| 244|0x000000008f400000, 0x000000008f400000, 0x000000008f500000|  0%| F|  |TAMS 0x000000008f400000| PB 0x000000008f400000| Untracked 
| 245|0x000000008f500000, 0x000000008f500000, 0x000000008f600000|  0%| F|  |TAMS 0x000000008f500000| PB 0x000000008f500000| Untracked 
| 246|0x000000008f600000, 0x000000008f600000, 0x000000008f700000|  0%| F|  |TAMS 0x000000008f600000| PB 0x000000008f600000| Untracked 
| 247|0x000000008f700000, 0x000000008f700000, 0x000000008f800000|  0%| F|  |TAMS 0x000000008f700000| PB 0x000000008f700000| Untracked 
| 248|0x000000008f800000, 0x000000008f800000, 0x000000008f900000|  0%| F|  |TAMS 0x000000008f800000| PB 0x000000008f800000| Untracked 
| 249|0x000000008f900000, 0x000000008fa00000, 0x000000008fa00000|100%| O|  |TAMS 0x000000008f900000| PB 0x000000008f900000| Untracked 
| 250|0x000000008fa00000, 0x000000008fa00000, 0x000000008fb00000|  0%| F|  |TAMS 0x000000008fa00000| PB 0x000000008fa00000| Untracked 
| 251|0x000000008fb00000, 0x000000008fc00000, 0x000000008fc00000|100%| S|CS|TAMS 0x000000008fb00000| PB 0x000000008fb00000| Complete 
| 252|0x000000008fc00000, 0x000000008fd00000, 0x000000008fd00000|100%| S|CS|TAMS 0x000000008fc00000| PB 0x000000008fc00000| Complete 
| 253|0x000000008fd00000, 0x000000008fe00000, 0x000000008fe00000|100%| S|CS|TAMS 0x000000008fd00000| PB 0x000000008fd00000| Complete 
| 254|0x000000008fe00000, 0x000000008ff00000, 0x000000008ff00000|100%| S|CS|TAMS 0x000000008fe00000| PB 0x000000008fe00000| Complete 
| 255|0x000000008ff00000, 0x0000000090000000, 0x0000000090000000|100%| S|CS|TAMS 0x000000008ff00000| PB 0x000000008ff00000| Complete 
| 256|0x0000000090000000, 0x0000000090100000, 0x0000000090100000|100%| S|CS|TAMS 0x0000000090000000| PB 0x0000000090000000| Complete 
| 257|0x0000000090100000, 0x0000000090200000, 0x0000000090200000|100%| S|CS|TAMS 0x0000000090100000| PB 0x0000000090100000| Complete 
| 258|0x0000000090200000, 0x0000000090300000, 0x0000000090300000|100%| S|CS|TAMS 0x0000000090200000| PB 0x0000000090200000| Complete 
| 259|0x0000000090300000, 0x0000000090400000, 0x0000000090400000|100%| S|CS|TAMS 0x0000000090300000| PB 0x0000000090300000| Complete 
| 260|0x0000000090400000, 0x0000000090500000, 0x0000000090500000|100%| S|CS|TAMS 0x0000000090400000| PB 0x0000000090400000| Complete 
| 261|0x0000000090500000, 0x0000000090600000, 0x0000000090600000|100%| S|CS|TAMS 0x0000000090500000| PB 0x0000000090500000| Complete 
| 262|0x0000000090600000, 0x0000000090700000, 0x0000000090700000|100%| S|CS|TAMS 0x0000000090600000| PB 0x0000000090600000| Complete 
| 263|0x0000000090700000, 0x0000000090800000, 0x0000000090800000|100%| S|CS|TAMS 0x0000000090700000| PB 0x0000000090700000| Complete 
| 264|0x0000000090800000, 0x0000000090900000, 0x0000000090900000|100%| S|CS|TAMS 0x0000000090800000| PB 0x0000000090800000| Complete 
| 265|0x0000000090900000, 0x0000000090a00000, 0x0000000090a00000|100%| S|CS|TAMS 0x0000000090900000| PB 0x0000000090900000| Complete 
| 266|0x0000000090a00000, 0x0000000090a00000, 0x0000000090b00000|  0%| F|  |TAMS 0x0000000090a00000| PB 0x0000000090a00000| Untracked 
| 267|0x0000000090b00000, 0x0000000090b00000, 0x0000000090c00000|  0%| F|  |TAMS 0x0000000090b00000| PB 0x0000000090b00000| Untracked 
| 268|0x0000000090c00000, 0x0000000090c00000, 0x0000000090d00000|  0%| F|  |TAMS 0x0000000090c00000| PB 0x0000000090c00000| Untracked 
| 269|0x0000000090d00000, 0x0000000090e00000, 0x0000000090e00000|100%| O|  |TAMS 0x0000000090d00000| PB 0x0000000090d00000| Untracked 
| 270|0x0000000090e00000, 0x0000000090e00000, 0x0000000090f00000|  0%| F|  |TAMS 0x0000000090e00000| PB 0x0000000090e00000| Untracked 
| 271|0x0000000090f00000, 0x0000000090f00000, 0x0000000091000000|  0%| F|  |TAMS 0x0000000090f00000| PB 0x0000000090f00000| Untracked 
| 272|0x0000000091000000, 0x0000000091000000, 0x0000000091100000|  0%| F|  |TAMS 0x0000000091000000| PB 0x0000000091000000| Untracked 
| 273|0x0000000091100000, 0x0000000091100000, 0x0000000091200000|  0%| F|  |TAMS 0x0000000091100000| PB 0x0000000091100000| Untracked 
| 274|0x0000000091200000, 0x0000000091200000, 0x0000000091300000|  0%| F|  |TAMS 0x0000000091200000| PB 0x0000000091200000| Untracked 
| 275|0x0000000091300000, 0x0000000091300000, 0x0000000091400000|  0%| F|  |TAMS 0x0000000091300000| PB 0x0000000091300000| Untracked 
| 276|0x0000000091400000, 0x0000000091400000, 0x0000000091500000|  0%| F|  |TAMS 0x0000000091400000| PB 0x0000000091400000| Untracked 
| 277|0x0000000091500000, 0x0000000091500000, 0x0000000091600000|  0%| F|  |TAMS 0x0000000091500000| PB 0x0000000091500000| Untracked 
| 278|0x0000000091600000, 0x0000000091600000, 0x0000000091700000|  0%| F|  |TAMS 0x0000000091600000| PB 0x0000000091600000| Untracked 
| 279|0x0000000091700000, 0x0000000091700000, 0x0000000091800000|  0%| F|  |TAMS 0x0000000091700000| PB 0x0000000091700000| Untracked 
| 280|0x0000000091800000, 0x0000000091800000, 0x0000000091900000|  0%| F|  |TAMS 0x0000000091800000| PB 0x0000000091800000| Untracked 
| 281|0x0000000091900000, 0x0000000091900000, 0x0000000091a00000|  0%| F|  |TAMS 0x0000000091900000| PB 0x0000000091900000| Untracked 
| 282|0x0000000091a00000, 0x0000000091a00000, 0x0000000091b00000|  0%| F|  |TAMS 0x0000000091a00000| PB 0x0000000091a00000| Untracked 
| 283|0x0000000091b00000, 0x0000000091c00000, 0x0000000091c00000|100%| O|  |TAMS 0x0000000091b00000| PB 0x0000000091b00000| Untracked 
| 284|0x0000000091c00000, 0x0000000091c00000, 0x0000000091d00000|  0%| F|  |TAMS 0x0000000091c00000| PB 0x0000000091c00000| Untracked 
| 285|0x0000000091d00000, 0x0000000091e00000, 0x0000000091e00000|100%| O|  |TAMS 0x0000000091d00000| PB 0x0000000091d00000| Untracked 
| 286|0x0000000091e00000, 0x0000000091e00000, 0x0000000091f00000|  0%| F|  |TAMS 0x0000000091e00000| PB 0x0000000091e00000| Untracked 
| 287|0x0000000091f00000, 0x0000000092000000, 0x0000000092000000|100%| O|  |TAMS 0x0000000091f00000| PB 0x0000000091f00000| Untracked 
| 288|0x0000000092000000, 0x0000000092000000, 0x0000000092100000|  0%| F|  |TAMS 0x0000000092000000| PB 0x0000000092000000| Untracked 
| 289|0x0000000092100000, 0x0000000092100000, 0x0000000092200000|  0%| F|  |TAMS 0x0000000092100000| PB 0x0000000092100000| Untracked 
| 290|0x0000000092200000, 0x0000000092200000, 0x0000000092300000|  0%| F|  |TAMS 0x0000000092200000| PB 0x0000000092200000| Untracked 
| 291|0x0000000092300000, 0x0000000092300000, 0x0000000092400000|  0%| F|  |TAMS 0x0000000092300000| PB 0x0000000092300000| Untracked 
| 292|0x0000000092400000, 0x0000000092400000, 0x0000000092500000|  0%| F|  |TAMS 0x0000000092400000| PB 0x0000000092400000| Untracked 
| 293|0x0000000092500000, 0x0000000092500000, 0x0000000092600000|  0%| F|  |TAMS 0x0000000092500000| PB 0x0000000092500000| Untracked 
| 294|0x0000000092600000, 0x0000000092600000, 0x0000000092700000|  0%| F|  |TAMS 0x0000000092600000| PB 0x0000000092600000| Untracked 
| 295|0x0000000092700000, 0x0000000092700000, 0x0000000092800000|  0%| F|  |TAMS 0x0000000092700000| PB 0x0000000092700000| Untracked 
| 296|0x0000000092800000, 0x0000000092800000, 0x0000000092900000|  0%| F|  |TAMS 0x0000000092800000| PB 0x0000000092800000| Untracked 
| 297|0x0000000092900000, 0x0000000092900000, 0x0000000092a00000|  0%| F|  |TAMS 0x0000000092900000| PB 0x0000000092900000| Untracked 
| 298|0x0000000092a00000, 0x0000000092a00000, 0x0000000092b00000|  0%| F|  |TAMS 0x0000000092a00000| PB 0x0000000092a00000| Untracked 
| 299|0x0000000092b00000, 0x0000000092c00000, 0x0000000092c00000|100%| O|  |TAMS 0x0000000092b00000| PB 0x0000000092b00000| Untracked 
| 300|0x0000000092c00000, 0x0000000092c00000, 0x0000000092d00000|  0%| F|  |TAMS 0x0000000092c00000| PB 0x0000000092c00000| Untracked 
| 301|0x0000000092d00000, 0x0000000092d00000, 0x0000000092e00000|  0%| F|  |TAMS 0x0000000092d00000| PB 0x0000000092d00000| Untracked 
| 302|0x0000000092e00000, 0x0000000092f00000, 0x0000000092f00000|100%| O|  |TAMS 0x0000000092e00000| PB 0x0000000092e00000| Untracked 
| 303|0x0000000092f00000, 0x0000000093000000, 0x0000000093000000|100%| O|  |TAMS 0x0000000092f00000| PB 0x0000000092f00000| Untracked 
| 304|0x0000000093000000, 0x0000000093000000, 0x0000000093100000|  0%| F|  |TAMS 0x0000000093000000| PB 0x0000000093000000| Untracked 
| 305|0x0000000093100000, 0x0000000093200000, 0x0000000093200000|100%| O|  |TAMS 0x0000000093100000| PB 0x0000000093100000| Untracked 
| 306|0x0000000093200000, 0x0000000093300000, 0x0000000093300000|100%| O|  |TAMS 0x0000000093200000| PB 0x0000000093200000| Untracked 
| 307|0x0000000093300000, 0x0000000093400000, 0x0000000093400000|100%| O|  |TAMS 0x0000000093300000| PB 0x0000000093300000| Untracked 
| 308|0x0000000093400000, 0x0000000093500000, 0x0000000093500000|100%| O|  |TAMS 0x0000000093400000| PB 0x0000000093400000| Untracked 
| 309|0x0000000093500000, 0x0000000093600000, 0x0000000093600000|100%| O|  |TAMS 0x0000000093500000| PB 0x0000000093500000| Untracked 
| 310|0x0000000093600000, 0x0000000093600000, 0x0000000093700000|  0%| F|  |TAMS 0x0000000093600000| PB 0x0000000093600000| Untracked 
| 311|0x0000000093700000, 0x0000000093800000, 0x0000000093800000|100%| O|  |TAMS 0x0000000093700000| PB 0x0000000093700000| Untracked 
| 312|0x0000000093800000, 0x0000000093900000, 0x0000000093900000|100%| O|  |TAMS 0x0000000093800000| PB 0x0000000093800000| Untracked 
| 313|0x0000000093900000, 0x0000000093a00000, 0x0000000093a00000|100%| O|  |TAMS 0x0000000093900000| PB 0x0000000093900000| Untracked 
| 314|0x0000000093a00000, 0x0000000093a00000, 0x0000000093b00000|  0%| F|  |TAMS 0x0000000093a00000| PB 0x0000000093a00000| Untracked 
| 315|0x0000000093b00000, 0x0000000093c00000, 0x0000000093c00000|100%| O|  |TAMS 0x0000000093b00000| PB 0x0000000093b00000| Untracked 
| 316|0x0000000093c00000, 0x0000000093c00000, 0x0000000093d00000|  0%| F|  |TAMS 0x0000000093c00000| PB 0x0000000093c00000| Untracked 
| 317|0x0000000093d00000, 0x0000000093d00000, 0x0000000093e00000|  0%| F|  |TAMS 0x0000000093d00000| PB 0x0000000093d00000| Untracked 
| 318|0x0000000093e00000, 0x0000000093e00000, 0x0000000093f00000|  0%| F|  |TAMS 0x0000000093e00000| PB 0x0000000093e00000| Untracked 
| 319|0x0000000093f00000, 0x0000000093f00000, 0x0000000094000000|  0%| F|  |TAMS 0x0000000093f00000| PB 0x0000000093f00000| Untracked 
| 320|0x0000000094000000, 0x0000000094000000, 0x0000000094100000|  0%| F|  |TAMS 0x0000000094000000| PB 0x0000000094000000| Untracked 
| 321|0x0000000094100000, 0x0000000094100000, 0x0000000094200000|  0%| F|  |TAMS 0x0000000094100000| PB 0x0000000094100000| Untracked 
| 322|0x0000000094200000, 0x0000000094300000, 0x0000000094300000|100%| O|  |TAMS 0x0000000094200000| PB 0x0000000094200000| Untracked 
| 323|0x0000000094300000, 0x0000000094300000, 0x0000000094400000|  0%| F|  |TAMS 0x0000000094300000| PB 0x0000000094300000| Untracked 
| 324|0x0000000094400000, 0x0000000094400000, 0x0000000094500000|  0%| F|  |TAMS 0x0000000094400000| PB 0x0000000094400000| Untracked 
| 325|0x0000000094500000, 0x0000000094500000, 0x0000000094600000|  0%| F|  |TAMS 0x0000000094500000| PB 0x0000000094500000| Untracked 
| 326|0x0000000094600000, 0x0000000094600000, 0x0000000094700000|  0%| F|  |TAMS 0x0000000094600000| PB 0x0000000094600000| Untracked 
| 327|0x0000000094700000, 0x0000000094700000, 0x0000000094800000|  0%| F|  |TAMS 0x0000000094700000| PB 0x0000000094700000| Untracked 
| 328|0x0000000094800000, 0x0000000094800000, 0x0000000094900000|  0%| F|  |TAMS 0x0000000094800000| PB 0x0000000094800000| Untracked 
| 329|0x0000000094900000, 0x0000000094900000, 0x0000000094a00000|  0%| F|  |TAMS 0x0000000094900000| PB 0x0000000094900000| Untracked 
| 330|0x0000000094a00000, 0x0000000094a00000, 0x0000000094b00000|  0%| F|  |TAMS 0x0000000094a00000| PB 0x0000000094a00000| Untracked 
| 331|0x0000000094b00000, 0x0000000094c00000, 0x0000000094c00000|100%|HS|  |TAMS 0x0000000094b00000| PB 0x0000000094b00000| Complete 
| 332|0x0000000094c00000, 0x0000000094c00000, 0x0000000094d00000|  0%| F|  |TAMS 0x0000000094c00000| PB 0x0000000094c00000| Untracked 
| 333|0x0000000094d00000, 0x0000000094d00000, 0x0000000094e00000|  0%| F|  |TAMS 0x0000000094d00000| PB 0x0000000094d00000| Untracked 
| 334|0x0000000094e00000, 0x0000000094e00000, 0x0000000094f00000|  0%| F|  |TAMS 0x0000000094e00000| PB 0x0000000094e00000| Untracked 
| 335|0x0000000094f00000, 0x0000000094f00000, 0x0000000095000000|  0%| F|  |TAMS 0x0000000094f00000| PB 0x0000000094f00000| Untracked 
| 336|0x0000000095000000, 0x0000000095000000, 0x0000000095100000|  0%| F|  |TAMS 0x0000000095000000| PB 0x0000000095000000| Untracked 
| 337|0x0000000095100000, 0x0000000095100000, 0x0000000095200000|  0%| F|  |TAMS 0x0000000095100000| PB 0x0000000095100000| Untracked 
| 338|0x0000000095200000, 0x0000000095200000, 0x0000000095300000|  0%| F|  |TAMS 0x0000000095200000| PB 0x0000000095200000| Untracked 
| 339|0x0000000095300000, 0x0000000095300000, 0x0000000095400000|  0%| F|  |TAMS 0x0000000095300000| PB 0x0000000095300000| Untracked 
| 340|0x0000000095400000, 0x0000000095400000, 0x0000000095500000|  0%| F|  |TAMS 0x0000000095400000| PB 0x0000000095400000| Untracked 
| 341|0x0000000095500000, 0x0000000095500000, 0x0000000095600000|  0%| F|  |TAMS 0x0000000095500000| PB 0x0000000095500000| Untracked 
| 342|0x0000000095600000, 0x0000000095600000, 0x0000000095700000|  0%| F|  |TAMS 0x0000000095600000| PB 0x0000000095600000| Untracked 
| 343|0x0000000095700000, 0x0000000095700000, 0x0000000095800000|  0%| F|  |TAMS 0x0000000095700000| PB 0x0000000095700000| Untracked 
| 344|0x0000000095800000, 0x0000000095800000, 0x0000000095900000|  0%| F|  |TAMS 0x0000000095800000| PB 0x0000000095800000| Untracked 
| 345|0x0000000095900000, 0x0000000095900000, 0x0000000095a00000|  0%| F|  |TAMS 0x0000000095900000| PB 0x0000000095900000| Untracked 
| 346|0x0000000095a00000, 0x0000000095a00000, 0x0000000095b00000|  0%| F|  |TAMS 0x0000000095a00000| PB 0x0000000095a00000| Untracked 
| 347|0x0000000095b00000, 0x0000000095b00000, 0x0000000095c00000|  0%| F|  |TAMS 0x0000000095b00000| PB 0x0000000095b00000| Untracked 
| 348|0x0000000095c00000, 0x0000000095c00000, 0x0000000095d00000|  0%| F|  |TAMS 0x0000000095c00000| PB 0x0000000095c00000| Untracked 
| 349|0x0000000095d00000, 0x0000000095d00000, 0x0000000095e00000|  0%| F|  |TAMS 0x0000000095d00000| PB 0x0000000095d00000| Untracked 
| 350|0x0000000095e00000, 0x0000000095e00000, 0x0000000095f00000|  0%| F|  |TAMS 0x0000000095e00000| PB 0x0000000095e00000| Untracked 
| 351|0x0000000095f00000, 0x0000000095f00000, 0x0000000096000000|  0%| F|  |TAMS 0x0000000095f00000| PB 0x0000000095f00000| Untracked 
| 352|0x0000000096000000, 0x0000000096000000, 0x0000000096100000|  0%| F|  |TAMS 0x0000000096000000| PB 0x0000000096000000| Untracked 
| 353|0x0000000096100000, 0x0000000096100000, 0x0000000096200000|  0%| F|  |TAMS 0x0000000096100000| PB 0x0000000096100000| Untracked 
| 354|0x0000000096200000, 0x0000000096300000, 0x0000000096300000|100%|HS|  |TAMS 0x0000000096200000| PB 0x0000000096200000| Complete 
| 355|0x0000000096300000, 0x0000000096400000, 0x0000000096400000|100%|HC|  |TAMS 0x0000000096300000| PB 0x0000000096300000| Complete 
| 356|0x0000000096400000, 0x0000000096500000, 0x0000000096500000|100%|HC|  |TAMS 0x0000000096400000| PB 0x0000000096400000| Complete 
| 357|0x0000000096500000, 0x0000000096600000, 0x0000000096600000|100%|HC|  |TAMS 0x0000000096500000| PB 0x0000000096500000| Complete 
| 358|0x0000000096600000, 0x0000000096700000, 0x0000000096700000|100%|HC|  |TAMS 0x0000000096600000| PB 0x0000000096600000| Complete 
| 359|0x0000000096700000, 0x0000000096800000, 0x0000000096800000|100%|HC|  |TAMS 0x0000000096700000| PB 0x0000000096700000| Complete 
| 360|0x0000000096800000, 0x0000000096900000, 0x0000000096900000|100%|HC|  |TAMS 0x0000000096800000| PB 0x0000000096800000| Complete 
| 361|0x0000000096900000, 0x0000000096900000, 0x0000000096a00000|  0%| F|  |TAMS 0x0000000096900000| PB 0x0000000096900000| Untracked 
| 362|0x0000000096a00000, 0x0000000096a00000, 0x0000000096b00000|  0%| F|  |TAMS 0x0000000096a00000| PB 0x0000000096a00000| Untracked 
| 363|0x0000000096b00000, 0x0000000096b00000, 0x0000000096c00000|  0%| F|  |TAMS 0x0000000096b00000| PB 0x0000000096b00000| Untracked 
| 364|0x0000000096c00000, 0x0000000096d00000, 0x0000000096d00000|100%| O|  |TAMS 0x0000000096c00000| PB 0x0000000096c00000| Untracked 
| 365|0x0000000096d00000, 0x0000000096e00000, 0x0000000096e00000|100%| O|  |TAMS 0x0000000096d00000| PB 0x0000000096d00000| Untracked 
| 366|0x0000000096e00000, 0x0000000096e00000, 0x0000000096f00000|  0%| F|  |TAMS 0x0000000096e00000| PB 0x0000000096e00000| Untracked 
| 367|0x0000000096f00000, 0x0000000096f00000, 0x0000000097000000|  0%| F|  |TAMS 0x0000000096f00000| PB 0x0000000096f00000| Untracked 
| 368|0x0000000097000000, 0x0000000097100000, 0x0000000097100000|100%| O|  |TAMS 0x0000000097000000| PB 0x0000000097000000| Untracked 
| 369|0x0000000097100000, 0x0000000097200000, 0x0000000097200000|100%| O|  |TAMS 0x0000000097100000| PB 0x0000000097100000| Untracked 
| 370|0x0000000097200000, 0x0000000097300000, 0x0000000097300000|100%| O|  |TAMS 0x0000000097200000| PB 0x0000000097200000| Untracked 
| 371|0x0000000097300000, 0x0000000097300000, 0x0000000097400000|  0%| F|  |TAMS 0x0000000097300000| PB 0x0000000097300000| Untracked 
| 372|0x0000000097400000, 0x0000000097500000, 0x0000000097500000|100%| O|  |TAMS 0x0000000097400000| PB 0x0000000097400000| Untracked 
| 373|0x0000000097500000, 0x0000000097500000, 0x0000000097600000|  0%| F|  |TAMS 0x0000000097500000| PB 0x0000000097500000| Untracked 
| 374|0x0000000097600000, 0x0000000097600000, 0x0000000097700000|  0%| F|  |TAMS 0x0000000097600000| PB 0x0000000097600000| Untracked 
| 375|0x0000000097700000, 0x0000000097800000, 0x0000000097800000|100%| O|  |TAMS 0x0000000097700000| PB 0x0000000097700000| Untracked 
| 376|0x0000000097800000, 0x0000000097800000, 0x0000000097900000|  0%| F|  |TAMS 0x0000000097800000| PB 0x0000000097800000| Untracked 
| 377|0x0000000097900000, 0x0000000097a00000, 0x0000000097a00000|100%| O|  |TAMS 0x0000000097900000| PB 0x0000000097900000| Untracked 
| 378|0x0000000097a00000, 0x0000000097b00000, 0x0000000097b00000|100%| O|  |TAMS 0x0000000097a00000| PB 0x0000000097a00000| Untracked 
| 379|0x0000000097b00000, 0x0000000097c00000, 0x0000000097c00000|100%| O|  |TAMS 0x0000000097b00000| PB 0x0000000097b00000| Untracked 
| 380|0x0000000097c00000, 0x0000000097c00000, 0x0000000097d00000|  0%| F|  |TAMS 0x0000000097c00000| PB 0x0000000097c00000| Untracked 
| 381|0x0000000097d00000, 0x0000000097d00000, 0x0000000097e00000|  0%| F|  |TAMS 0x0000000097d00000| PB 0x0000000097d00000| Untracked 
| 382|0x0000000097e00000, 0x0000000097e00000, 0x0000000097f00000|  0%| F|  |TAMS 0x0000000097e00000| PB 0x0000000097e00000| Untracked 
| 383|0x0000000097f00000, 0x0000000097f00000, 0x0000000098000000|  0%| F|  |TAMS 0x0000000097f00000| PB 0x0000000097f00000| Untracked 
| 384|0x0000000098000000, 0x0000000098000000, 0x0000000098100000|  0%| F|  |TAMS 0x0000000098000000| PB 0x0000000098000000| Untracked 
| 385|0x0000000098100000, 0x0000000098100000, 0x0000000098200000|  0%| F|  |TAMS 0x0000000098100000| PB 0x0000000098100000| Untracked 
| 386|0x0000000098200000, 0x0000000098200000, 0x0000000098300000|  0%| F|  |TAMS 0x0000000098200000| PB 0x0000000098200000| Untracked 
| 387|0x0000000098300000, 0x0000000098300000, 0x0000000098400000|  0%| F|  |TAMS 0x0000000098300000| PB 0x0000000098300000| Untracked 
| 388|0x0000000098400000, 0x0000000098400000, 0x0000000098500000|  0%| F|  |TAMS 0x0000000098400000| PB 0x0000000098400000| Untracked 
| 389|0x0000000098500000, 0x0000000098500000, 0x0000000098600000|  0%| F|  |TAMS 0x0000000098500000| PB 0x0000000098500000| Untracked 
| 390|0x0000000098600000, 0x0000000098600000, 0x0000000098700000|  0%| F|  |TAMS 0x0000000098600000| PB 0x0000000098600000| Untracked 
| 391|0x0000000098700000, 0x0000000098700000, 0x0000000098800000|  0%| F|  |TAMS 0x0000000098700000| PB 0x0000000098700000| Untracked 
| 392|0x0000000098800000, 0x0000000098800000, 0x0000000098900000|  0%| F|  |TAMS 0x0000000098800000| PB 0x0000000098800000| Untracked 
| 393|0x0000000098900000, 0x0000000098900000, 0x0000000098a00000|  0%| F|  |TAMS 0x0000000098900000| PB 0x0000000098900000| Untracked 
| 394|0x0000000098a00000, 0x0000000098a00000, 0x0000000098b00000|  0%| F|  |TAMS 0x0000000098a00000| PB 0x0000000098a00000| Untracked 
| 395|0x0000000098b00000, 0x0000000098b00000, 0x0000000098c00000|  0%| F|  |TAMS 0x0000000098b00000| PB 0x0000000098b00000| Untracked 
| 396|0x0000000098c00000, 0x0000000098c00000, 0x0000000098d00000|  0%| F|  |TAMS 0x0000000098c00000| PB 0x0000000098c00000| Untracked 
| 397|0x0000000098d00000, 0x0000000098d00000, 0x0000000098e00000|  0%| F|  |TAMS 0x0000000098d00000| PB 0x0000000098d00000| Untracked 
| 398|0x0000000098e00000, 0x0000000098e00000, 0x0000000098f00000|  0%| F|  |TAMS 0x0000000098e00000| PB 0x0000000098e00000| Untracked 
| 399|0x0000000098f00000, 0x0000000098f00000, 0x0000000099000000|  0%| F|  |TAMS 0x0000000098f00000| PB 0x0000000098f00000| Untracked 
| 400|0x0000000099000000, 0x0000000099000000, 0x0000000099100000|  0%| F|  |TAMS 0x0000000099000000| PB 0x0000000099000000| Untracked 
| 401|0x0000000099100000, 0x0000000099100000, 0x0000000099200000|  0%| F|  |TAMS 0x0000000099100000| PB 0x0000000099100000| Untracked 
| 402|0x0000000099200000, 0x0000000099200000, 0x0000000099300000|  0%| F|  |TAMS 0x0000000099200000| PB 0x0000000099200000| Untracked 
| 403|0x0000000099300000, 0x0000000099300000, 0x0000000099400000|  0%| F|  |TAMS 0x0000000099300000| PB 0x0000000099300000| Untracked 
| 404|0x0000000099400000, 0x0000000099400000, 0x0000000099500000|  0%| F|  |TAMS 0x0000000099400000| PB 0x0000000099400000| Untracked 
| 405|0x0000000099500000, 0x0000000099500000, 0x0000000099600000|  0%| F|  |TAMS 0x0000000099500000| PB 0x0000000099500000| Untracked 
| 406|0x0000000099600000, 0x0000000099600000, 0x0000000099700000|  0%| F|  |TAMS 0x0000000099600000| PB 0x0000000099600000| Untracked 
| 407|0x0000000099700000, 0x0000000099700000, 0x0000000099800000|  0%| F|  |TAMS 0x0000000099700000| PB 0x0000000099700000| Untracked 
| 408|0x0000000099800000, 0x0000000099800000, 0x0000000099900000|  0%| F|  |TAMS 0x0000000099800000| PB 0x0000000099800000| Untracked 
| 409|0x0000000099900000, 0x0000000099900000, 0x0000000099a00000|  0%| F|  |TAMS 0x0000000099900000| PB 0x0000000099900000| Untracked 
| 410|0x0000000099a00000, 0x0000000099a00000, 0x0000000099b00000|  0%| F|  |TAMS 0x0000000099a00000| PB 0x0000000099a00000| Untracked 
| 411|0x0000000099b00000, 0x0000000099b00000, 0x0000000099c00000|  0%| F|  |TAMS 0x0000000099b00000| PB 0x0000000099b00000| Untracked 
| 412|0x0000000099c00000, 0x0000000099c00000, 0x0000000099d00000|  0%| F|  |TAMS 0x0000000099c00000| PB 0x0000000099c00000| Untracked 
| 413|0x0000000099d00000, 0x0000000099d00000, 0x0000000099e00000|  0%| F|  |TAMS 0x0000000099d00000| PB 0x0000000099d00000| Untracked 
| 414|0x0000000099e00000, 0x0000000099e00000, 0x0000000099f00000|  0%| F|  |TAMS 0x0000000099e00000| PB 0x0000000099e00000| Untracked 
| 415|0x0000000099f00000, 0x0000000099f00000, 0x000000009a000000|  0%| F|  |TAMS 0x0000000099f00000| PB 0x0000000099f00000| Untracked 
| 416|0x000000009a000000, 0x000000009a000000, 0x000000009a100000|  0%| F|  |TAMS 0x000000009a000000| PB 0x000000009a000000| Untracked 
| 417|0x000000009a100000, 0x000000009a100000, 0x000000009a200000|  0%| F|  |TAMS 0x000000009a100000| PB 0x000000009a100000| Untracked 
| 418|0x000000009a200000, 0x000000009a200000, 0x000000009a300000|  0%| F|  |TAMS 0x000000009a200000| PB 0x000000009a200000| Untracked 
| 419|0x000000009a300000, 0x000000009a300000, 0x000000009a400000|  0%| F|  |TAMS 0x000000009a300000| PB 0x000000009a300000| Untracked 
| 420|0x000000009a400000, 0x000000009a400000, 0x000000009a500000|  0%| F|  |TAMS 0x000000009a400000| PB 0x000000009a400000| Untracked 
| 421|0x000000009a500000, 0x000000009a500000, 0x000000009a600000|  0%| F|  |TAMS 0x000000009a500000| PB 0x000000009a500000| Untracked 
| 422|0x000000009a600000, 0x000000009a600000, 0x000000009a700000|  0%| F|  |TAMS 0x000000009a600000| PB 0x000000009a600000| Untracked 
| 423|0x000000009a700000, 0x000000009a700000, 0x000000009a800000|  0%| F|  |TAMS 0x000000009a700000| PB 0x000000009a700000| Untracked 
| 424|0x000000009a800000, 0x000000009a800000, 0x000000009a900000|  0%| F|  |TAMS 0x000000009a800000| PB 0x000000009a800000| Untracked 
| 425|0x000000009a900000, 0x000000009a900000, 0x000000009aa00000|  0%| F|  |TAMS 0x000000009a900000| PB 0x000000009a900000| Untracked 
| 426|0x000000009aa00000, 0x000000009aa00000, 0x000000009ab00000|  0%| F|  |TAMS 0x000000009aa00000| PB 0x000000009aa00000| Untracked 
| 427|0x000000009ab00000, 0x000000009ab00000, 0x000000009ac00000|  0%| F|  |TAMS 0x000000009ab00000| PB 0x000000009ab00000| Untracked 
| 428|0x000000009ac00000, 0x000000009ac00000, 0x000000009ad00000|  0%| F|  |TAMS 0x000000009ac00000| PB 0x000000009ac00000| Untracked 
| 429|0x000000009ad00000, 0x000000009ad00000, 0x000000009ae00000|  0%| F|  |TAMS 0x000000009ad00000| PB 0x000000009ad00000| Untracked 
| 430|0x000000009ae00000, 0x000000009ae00000, 0x000000009af00000|  0%| F|  |TAMS 0x000000009ae00000| PB 0x000000009ae00000| Untracked 
| 431|0x000000009af00000, 0x000000009af00000, 0x000000009b000000|  0%| F|  |TAMS 0x000000009af00000| PB 0x000000009af00000| Untracked 
| 432|0x000000009b000000, 0x000000009b000000, 0x000000009b100000|  0%| F|  |TAMS 0x000000009b000000| PB 0x000000009b000000| Untracked 
| 433|0x000000009b100000, 0x000000009b100000, 0x000000009b200000|  0%| F|  |TAMS 0x000000009b100000| PB 0x000000009b100000| Untracked 
| 434|0x000000009b200000, 0x000000009b200000, 0x000000009b300000|  0%| F|  |TAMS 0x000000009b200000| PB 0x000000009b200000| Untracked 
| 435|0x000000009b300000, 0x000000009b300000, 0x000000009b400000|  0%| F|  |TAMS 0x000000009b300000| PB 0x000000009b300000| Untracked 
| 436|0x000000009b400000, 0x000000009b400000, 0x000000009b500000|  0%| F|  |TAMS 0x000000009b400000| PB 0x000000009b400000| Untracked 
| 437|0x000000009b500000, 0x000000009b500000, 0x000000009b600000|  0%| F|  |TAMS 0x000000009b500000| PB 0x000000009b500000| Untracked 
| 438|0x000000009b600000, 0x000000009b600000, 0x000000009b700000|  0%| F|  |TAMS 0x000000009b600000| PB 0x000000009b600000| Untracked 
| 439|0x000000009b700000, 0x000000009b700000, 0x000000009b800000|  0%| F|  |TAMS 0x000000009b700000| PB 0x000000009b700000| Untracked 
| 440|0x000000009b800000, 0x000000009b800000, 0x000000009b900000|  0%| F|  |TAMS 0x000000009b800000| PB 0x000000009b800000| Untracked 
| 441|0x000000009b900000, 0x000000009b900000, 0x000000009ba00000|  0%| F|  |TAMS 0x000000009b900000| PB 0x000000009b900000| Untracked 
| 442|0x000000009ba00000, 0x000000009ba00000, 0x000000009bb00000|  0%| F|  |TAMS 0x000000009ba00000| PB 0x000000009ba00000| Untracked 
| 443|0x000000009bb00000, 0x000000009bb00000, 0x000000009bc00000|  0%| F|  |TAMS 0x000000009bb00000| PB 0x000000009bb00000| Untracked 
| 444|0x000000009bc00000, 0x000000009bc00000, 0x000000009bd00000|  0%| F|  |TAMS 0x000000009bc00000| PB 0x000000009bc00000| Untracked 
| 445|0x000000009bd00000, 0x000000009bd00000, 0x000000009be00000|  0%| F|  |TAMS 0x000000009bd00000| PB 0x000000009bd00000| Untracked 
| 446|0x000000009be00000, 0x000000009be00000, 0x000000009bf00000|  0%| F|  |TAMS 0x000000009be00000| PB 0x000000009be00000| Untracked 
| 447|0x000000009bf00000, 0x000000009bf00000, 0x000000009c000000|  0%| F|  |TAMS 0x000000009bf00000| PB 0x000000009bf00000| Untracked 
| 448|0x000000009c000000, 0x000000009c000000, 0x000000009c100000|  0%| F|  |TAMS 0x000000009c000000| PB 0x000000009c000000| Untracked 
| 449|0x000000009c100000, 0x000000009c100000, 0x000000009c200000|  0%| F|  |TAMS 0x000000009c100000| PB 0x000000009c100000| Untracked 
| 450|0x000000009c200000, 0x000000009c200000, 0x000000009c300000|  0%| F|  |TAMS 0x000000009c200000| PB 0x000000009c200000| Untracked 
| 451|0x000000009c300000, 0x000000009c300000, 0x000000009c400000|  0%| F|  |TAMS 0x000000009c300000| PB 0x000000009c300000| Untracked 
| 452|0x000000009c400000, 0x000000009c400000, 0x000000009c500000|  0%| F|  |TAMS 0x000000009c400000| PB 0x000000009c400000| Untracked 
| 453|0x000000009c500000, 0x000000009c500000, 0x000000009c600000|  0%| F|  |TAMS 0x000000009c500000| PB 0x000000009c500000| Untracked 
| 454|0x000000009c600000, 0x000000009c600000, 0x000000009c700000|  0%| F|  |TAMS 0x000000009c600000| PB 0x000000009c600000| Untracked 
| 455|0x000000009c700000, 0x000000009c700000, 0x000000009c800000|  0%| F|  |TAMS 0x000000009c700000| PB 0x000000009c700000| Untracked 
| 456|0x000000009c800000, 0x000000009c800000, 0x000000009c900000|  0%| F|  |TAMS 0x000000009c800000| PB 0x000000009c800000| Untracked 
| 457|0x000000009c900000, 0x000000009c900000, 0x000000009ca00000|  0%| F|  |TAMS 0x000000009c900000| PB 0x000000009c900000| Untracked 
| 458|0x000000009ca00000, 0x000000009ca00000, 0x000000009cb00000|  0%| F|  |TAMS 0x000000009ca00000| PB 0x000000009ca00000| Untracked 
| 459|0x000000009cb00000, 0x000000009cb00000, 0x000000009cc00000|  0%| F|  |TAMS 0x000000009cb00000| PB 0x000000009cb00000| Untracked 
| 460|0x000000009cc00000, 0x000000009cc00000, 0x000000009cd00000|  0%| F|  |TAMS 0x000000009cc00000| PB 0x000000009cc00000| Untracked 
| 461|0x000000009cd00000, 0x000000009cd00000, 0x000000009ce00000|  0%| F|  |TAMS 0x000000009cd00000| PB 0x000000009cd00000| Untracked 
| 462|0x000000009ce00000, 0x000000009ce00000, 0x000000009cf00000|  0%| F|  |TAMS 0x000000009ce00000| PB 0x000000009ce00000| Untracked 
| 463|0x000000009cf00000, 0x000000009cf00000, 0x000000009d000000|  0%| F|  |TAMS 0x000000009cf00000| PB 0x000000009cf00000| Untracked 
| 464|0x000000009d000000, 0x000000009d000000, 0x000000009d100000|  0%| F|  |TAMS 0x000000009d000000| PB 0x000000009d000000| Untracked 
| 465|0x000000009d100000, 0x000000009d100000, 0x000000009d200000|  0%| F|  |TAMS 0x000000009d100000| PB 0x000000009d100000| Untracked 
| 466|0x000000009d200000, 0x000000009d200000, 0x000000009d300000|  0%| F|  |TAMS 0x000000009d200000| PB 0x000000009d200000| Untracked 
| 467|0x000000009d300000, 0x000000009d300000, 0x000000009d400000|  0%| F|  |TAMS 0x000000009d300000| PB 0x000000009d300000| Untracked 
| 468|0x000000009d400000, 0x000000009d400000, 0x000000009d500000|  0%| F|  |TAMS 0x000000009d400000| PB 0x000000009d400000| Untracked 
| 469|0x000000009d500000, 0x000000009d500000, 0x000000009d600000|  0%| F|  |TAMS 0x000000009d500000| PB 0x000000009d500000| Untracked 
| 470|0x000000009d600000, 0x000000009d600000, 0x000000009d700000|  0%| F|  |TAMS 0x000000009d600000| PB 0x000000009d600000| Untracked 
| 471|0x000000009d700000, 0x000000009d700000, 0x000000009d800000|  0%| F|  |TAMS 0x000000009d700000| PB 0x000000009d700000| Untracked 
| 472|0x000000009d800000, 0x000000009d800000, 0x000000009d900000|  0%| F|  |TAMS 0x000000009d800000| PB 0x000000009d800000| Untracked 
| 473|0x000000009d900000, 0x000000009d900000, 0x000000009da00000|  0%| F|  |TAMS 0x000000009d900000| PB 0x000000009d900000| Untracked 
| 474|0x000000009da00000, 0x000000009da00000, 0x000000009db00000|  0%| F|  |TAMS 0x000000009da00000| PB 0x000000009da00000| Untracked 
| 475|0x000000009db00000, 0x000000009db00000, 0x000000009dc00000|  0%| F|  |TAMS 0x000000009db00000| PB 0x000000009db00000| Untracked 
| 476|0x000000009dc00000, 0x000000009dc00000, 0x000000009dd00000|  0%| F|  |TAMS 0x000000009dc00000| PB 0x000000009dc00000| Untracked 
| 477|0x000000009dd00000, 0x000000009dd00000, 0x000000009de00000|  0%| F|  |TAMS 0x000000009dd00000| PB 0x000000009dd00000| Untracked 
| 478|0x000000009de00000, 0x000000009de00000, 0x000000009df00000|  0%| F|  |TAMS 0x000000009de00000| PB 0x000000009de00000| Untracked 
| 479|0x000000009df00000, 0x000000009df00000, 0x000000009e000000|  0%| F|  |TAMS 0x000000009df00000| PB 0x000000009df00000| Untracked 
| 480|0x000000009e000000, 0x000000009e000000, 0x000000009e100000|  0%| F|  |TAMS 0x000000009e000000| PB 0x000000009e000000| Untracked 
| 481|0x000000009e100000, 0x000000009e100000, 0x000000009e200000|  0%| F|  |TAMS 0x000000009e100000| PB 0x000000009e100000| Untracked 
| 482|0x000000009e200000, 0x000000009e200000, 0x000000009e300000|  0%| F|  |TAMS 0x000000009e200000| PB 0x000000009e200000| Untracked 
| 483|0x000000009e300000, 0x000000009e300000, 0x000000009e400000|  0%| F|  |TAMS 0x000000009e300000| PB 0x000000009e300000| Untracked 
| 484|0x000000009e400000, 0x000000009e400000, 0x000000009e500000|  0%| F|  |TAMS 0x000000009e400000| PB 0x000000009e400000| Untracked 
| 485|0x000000009e500000, 0x000000009e500000, 0x000000009e600000|  0%| F|  |TAMS 0x000000009e500000| PB 0x000000009e500000| Untracked 
| 486|0x000000009e600000, 0x000000009e600000, 0x000000009e700000|  0%| F|  |TAMS 0x000000009e600000| PB 0x000000009e600000| Untracked 
| 487|0x000000009e700000, 0x000000009e700000, 0x000000009e800000|  0%| F|  |TAMS 0x000000009e700000| PB 0x000000009e700000| Untracked 
| 488|0x000000009e800000, 0x000000009e800000, 0x000000009e900000|  0%| F|  |TAMS 0x000000009e800000| PB 0x000000009e800000| Untracked 
| 489|0x000000009e900000, 0x000000009e900000, 0x000000009ea00000|  0%| F|  |TAMS 0x000000009e900000| PB 0x000000009e900000| Untracked 
| 490|0x000000009ea00000, 0x000000009ea00000, 0x000000009eb00000|  0%| F|  |TAMS 0x000000009ea00000| PB 0x000000009ea00000| Untracked 
| 491|0x000000009eb00000, 0x000000009eb00000, 0x000000009ec00000|  0%| F|  |TAMS 0x000000009eb00000| PB 0x000000009eb00000| Untracked 
| 492|0x000000009ec00000, 0x000000009ec00000, 0x000000009ed00000|  0%| F|  |TAMS 0x000000009ec00000| PB 0x000000009ec00000| Untracked 
| 493|0x000000009ed00000, 0x000000009ed00000, 0x000000009ee00000|  0%| F|  |TAMS 0x000000009ed00000| PB 0x000000009ed00000| Untracked 
| 494|0x000000009ee00000, 0x000000009ee00000, 0x000000009ef00000|  0%| F|  |TAMS 0x000000009ee00000| PB 0x000000009ee00000| Untracked 
| 495|0x000000009ef00000, 0x000000009ef00000, 0x000000009f000000|  0%| F|  |TAMS 0x000000009ef00000| PB 0x000000009ef00000| Untracked 
| 496|0x000000009f000000, 0x000000009f000000, 0x000000009f100000|  0%| F|  |TAMS 0x000000009f000000| PB 0x000000009f000000| Untracked 
| 497|0x000000009f100000, 0x000000009f100000, 0x000000009f200000|  0%| F|  |TAMS 0x000000009f100000| PB 0x000000009f100000| Untracked 
| 498|0x000000009f200000, 0x000000009f200000, 0x000000009f300000|  0%| F|  |TAMS 0x000000009f200000| PB 0x000000009f200000| Untracked 
| 499|0x000000009f300000, 0x000000009f300000, 0x000000009f400000|  0%| F|  |TAMS 0x000000009f300000| PB 0x000000009f300000| Untracked 
| 500|0x000000009f400000, 0x000000009f4853a0, 0x000000009f500000| 52%| E|  |TAMS 0x000000009f400000| PB 0x000000009f400000| Complete 
| 501|0x000000009f500000, 0x000000009f600000, 0x000000009f600000|100%| E|CS|TAMS 0x000000009f500000| PB 0x000000009f500000| Complete 
| 502|0x000000009f600000, 0x000000009f700000, 0x000000009f700000|100%| E|CS|TAMS 0x000000009f600000| PB 0x000000009f600000| Complete 
| 503|0x000000009f700000, 0x000000009f800000, 0x000000009f800000|100%| E|CS|TAMS 0x000000009f700000| PB 0x000000009f700000| Complete 
| 504|0x000000009f800000, 0x000000009f900000, 0x000000009f900000|100%| E|CS|TAMS 0x000000009f800000| PB 0x000000009f800000| Complete 
| 505|0x000000009f900000, 0x000000009fa00000, 0x000000009fa00000|100%| E|CS|TAMS 0x000000009f900000| PB 0x000000009f900000| Complete 
| 506|0x000000009fa00000, 0x000000009fb00000, 0x000000009fb00000|100%| E|CS|TAMS 0x000000009fa00000| PB 0x000000009fa00000| Complete 
| 507|0x000000009fb00000, 0x000000009fc00000, 0x000000009fc00000|100%| E|CS|TAMS 0x000000009fb00000| PB 0x000000009fb00000| Complete 
| 508|0x000000009fc00000, 0x000000009fd00000, 0x000000009fd00000|100%| E|CS|TAMS 0x000000009fc00000| PB 0x000000009fc00000| Complete 
| 509|0x000000009fd00000, 0x000000009fe00000, 0x000000009fe00000|100%| E|CS|TAMS 0x000000009fd00000| PB 0x000000009fd00000| Complete 
| 510|0x000000009fe00000, 0x000000009ff00000, 0x000000009ff00000|100%| E|CS|TAMS 0x000000009fe00000| PB 0x000000009fe00000| Complete 
| 511|0x000000009ff00000, 0x00000000a0000000, 0x00000000a0000000|100%| E|CS|TAMS 0x000000009ff00000| PB 0x000000009ff00000| Complete 
| 512|0x00000000a0000000, 0x00000000a0100000, 0x00000000a0100000|100%| E|CS|TAMS 0x00000000a0000000| PB 0x00000000a0000000| Complete 
| 513|0x00000000a0100000, 0x00000000a0200000, 0x00000000a0200000|100%| E|CS|TAMS 0x00000000a0100000| PB 0x00000000a0100000| Complete 
| 514|0x00000000a0200000, 0x00000000a0300000, 0x00000000a0300000|100%| E|CS|TAMS 0x00000000a0200000| PB 0x00000000a0200000| Complete 
| 515|0x00000000a0300000, 0x00000000a0400000, 0x00000000a0400000|100%| E|CS|TAMS 0x00000000a0300000| PB 0x00000000a0300000| Complete 
| 516|0x00000000a0400000, 0x00000000a0500000, 0x00000000a0500000|100%| E|CS|TAMS 0x00000000a0400000| PB 0x00000000a0400000| Complete 
| 517|0x00000000a0500000, 0x00000000a0600000, 0x00000000a0600000|100%| E|CS|TAMS 0x00000000a0500000| PB 0x00000000a0500000| Complete 
| 518|0x00000000a0600000, 0x00000000a0700000, 0x00000000a0700000|100%| E|CS|TAMS 0x00000000a0600000| PB 0x00000000a0600000| Complete 
| 519|0x00000000a0700000, 0x00000000a0800000, 0x00000000a0800000|100%| E|CS|TAMS 0x00000000a0700000| PB 0x00000000a0700000| Complete 
| 520|0x00000000a0800000, 0x00000000a0900000, 0x00000000a0900000|100%| E|CS|TAMS 0x00000000a0800000| PB 0x00000000a0800000| Complete 
| 521|0x00000000a0900000, 0x00000000a0a00000, 0x00000000a0a00000|100%| E|CS|TAMS 0x00000000a0900000| PB 0x00000000a0900000| Complete 
| 522|0x00000000a0a00000, 0x00000000a0b00000, 0x00000000a0b00000|100%| E|CS|TAMS 0x00000000a0a00000| PB 0x00000000a0a00000| Complete 
| 523|0x00000000a0b00000, 0x00000000a0c00000, 0x00000000a0c00000|100%| E|CS|TAMS 0x00000000a0b00000| PB 0x00000000a0b00000| Complete 
| 524|0x00000000a0c00000, 0x00000000a0d00000, 0x00000000a0d00000|100%| E|CS|TAMS 0x00000000a0c00000| PB 0x00000000a0c00000| Complete 
| 525|0x00000000a0d00000, 0x00000000a0e00000, 0x00000000a0e00000|100%| E|CS|TAMS 0x00000000a0d00000| PB 0x00000000a0d00000| Complete 
| 526|0x00000000a0e00000, 0x00000000a0f00000, 0x00000000a0f00000|100%| E|CS|TAMS 0x00000000a0e00000| PB 0x00000000a0e00000| Complete 
| 527|0x00000000a0f00000, 0x00000000a1000000, 0x00000000a1000000|100%| E|CS|TAMS 0x00000000a0f00000| PB 0x00000000a0f00000| Complete 
| 528|0x00000000a1000000, 0x00000000a1100000, 0x00000000a1100000|100%| E|CS|TAMS 0x00000000a1000000| PB 0x00000000a1000000| Complete 
| 529|0x00000000a1100000, 0x00000000a1200000, 0x00000000a1200000|100%| E|CS|TAMS 0x00000000a1100000| PB 0x00000000a1100000| Complete 
| 530|0x00000000a1200000, 0x00000000a1300000, 0x00000000a1300000|100%| E|CS|TAMS 0x00000000a1200000| PB 0x00000000a1200000| Complete 
| 531|0x00000000a1300000, 0x00000000a1400000, 0x00000000a1400000|100%| E|CS|TAMS 0x00000000a1300000| PB 0x00000000a1300000| Complete 
| 532|0x00000000a1400000, 0x00000000a1500000, 0x00000000a1500000|100%| E|CS|TAMS 0x00000000a1400000| PB 0x00000000a1400000| Complete 
| 533|0x00000000a1500000, 0x00000000a1600000, 0x00000000a1600000|100%| E|CS|TAMS 0x00000000a1500000| PB 0x00000000a1500000| Complete 
| 534|0x00000000a1600000, 0x00000000a1700000, 0x00000000a1700000|100%| E|CS|TAMS 0x00000000a1600000| PB 0x00000000a1600000| Complete 
| 535|0x00000000a1700000, 0x00000000a1800000, 0x00000000a1800000|100%| E|CS|TAMS 0x00000000a1700000| PB 0x00000000a1700000| Complete 
| 536|0x00000000a1800000, 0x00000000a1900000, 0x00000000a1900000|100%| E|CS|TAMS 0x00000000a1800000| PB 0x00000000a1800000| Complete 
| 537|0x00000000a1900000, 0x00000000a1a00000, 0x00000000a1a00000|100%| E|CS|TAMS 0x00000000a1900000| PB 0x00000000a1900000| Complete 
| 538|0x00000000a1a00000, 0x00000000a1b00000, 0x00000000a1b00000|100%| E|CS|TAMS 0x00000000a1a00000| PB 0x00000000a1a00000| Complete 
| 539|0x00000000a1b00000, 0x00000000a1c00000, 0x00000000a1c00000|100%| E|CS|TAMS 0x00000000a1b00000| PB 0x00000000a1b00000| Complete 
| 540|0x00000000a1c00000, 0x00000000a1d00000, 0x00000000a1d00000|100%| E|CS|TAMS 0x00000000a1c00000| PB 0x00000000a1c00000| Complete 
| 541|0x00000000a1d00000, 0x00000000a1e00000, 0x00000000a1e00000|100%| E|CS|TAMS 0x00000000a1d00000| PB 0x00000000a1d00000| Complete 
| 542|0x00000000a1e00000, 0x00000000a1f00000, 0x00000000a1f00000|100%| E|CS|TAMS 0x00000000a1e00000| PB 0x00000000a1e00000| Complete 
| 543|0x00000000a1f00000, 0x00000000a2000000, 0x00000000a2000000|100%| E|CS|TAMS 0x00000000a1f00000| PB 0x00000000a1f00000| Complete 
| 544|0x00000000a2000000, 0x00000000a2100000, 0x00000000a2100000|100%| E|CS|TAMS 0x00000000a2000000| PB 0x00000000a2000000| Complete 
| 545|0x00000000a2100000, 0x00000000a2200000, 0x00000000a2200000|100%| E|CS|TAMS 0x00000000a2100000| PB 0x00000000a2100000| Complete 
| 546|0x00000000a2200000, 0x00000000a2300000, 0x00000000a2300000|100%| E|CS|TAMS 0x00000000a2200000| PB 0x00000000a2200000| Complete 
| 547|0x00000000a2300000, 0x00000000a2400000, 0x00000000a2400000|100%| E|CS|TAMS 0x00000000a2300000| PB 0x00000000a2300000| Complete 
| 548|0x00000000a2400000, 0x00000000a2500000, 0x00000000a2500000|100%| E|CS|TAMS 0x00000000a2400000| PB 0x00000000a2400000| Complete 
| 549|0x00000000a2500000, 0x00000000a2600000, 0x00000000a2600000|100%| E|CS|TAMS 0x00000000a2500000| PB 0x00000000a2500000| Complete 
| 550|0x00000000a2600000, 0x00000000a2700000, 0x00000000a2700000|100%| E|CS|TAMS 0x00000000a2600000| PB 0x00000000a2600000| Complete 
| 551|0x00000000a2700000, 0x00000000a2800000, 0x00000000a2800000|100%| E|CS|TAMS 0x00000000a2700000| PB 0x00000000a2700000| Complete 
| 552|0x00000000a2800000, 0x00000000a2900000, 0x00000000a2900000|100%| E|CS|TAMS 0x00000000a2800000| PB 0x00000000a2800000| Complete 
| 553|0x00000000a2900000, 0x00000000a2a00000, 0x00000000a2a00000|100%| E|CS|TAMS 0x00000000a2900000| PB 0x00000000a2900000| Complete 
| 554|0x00000000a2a00000, 0x00000000a2b00000, 0x00000000a2b00000|100%| E|CS|TAMS 0x00000000a2a00000| PB 0x00000000a2a00000| Complete 
| 555|0x00000000a2b00000, 0x00000000a2c00000, 0x00000000a2c00000|100%| E|CS|TAMS 0x00000000a2b00000| PB 0x00000000a2b00000| Complete 
| 556|0x00000000a2c00000, 0x00000000a2d00000, 0x00000000a2d00000|100%| E|CS|TAMS 0x00000000a2c00000| PB 0x00000000a2c00000| Complete 
| 557|0x00000000a2d00000, 0x00000000a2e00000, 0x00000000a2e00000|100%| E|CS|TAMS 0x00000000a2d00000| PB 0x00000000a2d00000| Complete 
| 558|0x00000000a2e00000, 0x00000000a2f00000, 0x00000000a2f00000|100%| E|CS|TAMS 0x00000000a2e00000| PB 0x00000000a2e00000| Complete 
| 559|0x00000000a2f00000, 0x00000000a3000000, 0x00000000a3000000|100%| E|CS|TAMS 0x00000000a2f00000| PB 0x00000000a2f00000| Complete 
| 560|0x00000000a3000000, 0x00000000a3100000, 0x00000000a3100000|100%| E|CS|TAMS 0x00000000a3000000| PB 0x00000000a3000000| Complete 
| 561|0x00000000a3100000, 0x00000000a3200000, 0x00000000a3200000|100%| E|CS|TAMS 0x00000000a3100000| PB 0x00000000a3100000| Complete 
| 562|0x00000000a3200000, 0x00000000a3300000, 0x00000000a3300000|100%| E|CS|TAMS 0x00000000a3200000| PB 0x00000000a3200000| Complete 
| 563|0x00000000a3300000, 0x00000000a3400000, 0x00000000a3400000|100%| E|CS|TAMS 0x00000000a3300000| PB 0x00000000a3300000| Complete 
| 564|0x00000000a3400000, 0x00000000a3500000, 0x00000000a3500000|100%| E|CS|TAMS 0x00000000a3400000| PB 0x00000000a3400000| Complete 
| 565|0x00000000a3500000, 0x00000000a3600000, 0x00000000a3600000|100%| E|CS|TAMS 0x00000000a3500000| PB 0x00000000a3500000| Complete 
| 566|0x00000000a3600000, 0x00000000a3700000, 0x00000000a3700000|100%| E|CS|TAMS 0x00000000a3600000| PB 0x00000000a3600000| Complete 
| 567|0x00000000a3700000, 0x00000000a3800000, 0x00000000a3800000|100%| E|CS|TAMS 0x00000000a3700000| PB 0x00000000a3700000| Complete 
| 568|0x00000000a3800000, 0x00000000a3900000, 0x00000000a3900000|100%| E|CS|TAMS 0x00000000a3800000| PB 0x00000000a3800000| Complete 
| 569|0x00000000a3900000, 0x00000000a3a00000, 0x00000000a3a00000|100%| E|CS|TAMS 0x00000000a3900000| PB 0x00000000a3900000| Complete 
| 570|0x00000000a3a00000, 0x00000000a3b00000, 0x00000000a3b00000|100%| E|CS|TAMS 0x00000000a3a00000| PB 0x00000000a3a00000| Complete 
| 571|0x00000000a3b00000, 0x00000000a3c00000, 0x00000000a3c00000|100%| E|CS|TAMS 0x00000000a3b00000| PB 0x00000000a3b00000| Complete 
| 572|0x00000000a3c00000, 0x00000000a3d00000, 0x00000000a3d00000|100%| E|CS|TAMS 0x00000000a3c00000| PB 0x00000000a3c00000| Complete 
| 573|0x00000000a3d00000, 0x00000000a3e00000, 0x00000000a3e00000|100%| E|CS|TAMS 0x00000000a3d00000| PB 0x00000000a3d00000| Complete 
| 574|0x00000000a3e00000, 0x00000000a3f00000, 0x00000000a3f00000|100%| E|CS|TAMS 0x00000000a3e00000| PB 0x00000000a3e00000| Complete 
| 575|0x00000000a3f00000, 0x00000000a4000000, 0x00000000a4000000|100%| E|CS|TAMS 0x00000000a3f00000| PB 0x00000000a3f00000| Complete 
| 576|0x00000000a4000000, 0x00000000a4100000, 0x00000000a4100000|100%| E|CS|TAMS 0x00000000a4000000| PB 0x00000000a4000000| Complete 
| 577|0x00000000a4100000, 0x00000000a4200000, 0x00000000a4200000|100%| E|CS|TAMS 0x00000000a4100000| PB 0x00000000a4100000| Complete 
| 578|0x00000000a4200000, 0x00000000a4300000, 0x00000000a4300000|100%| E|CS|TAMS 0x00000000a4200000| PB 0x00000000a4200000| Complete 
| 579|0x00000000a4300000, 0x00000000a4400000, 0x00000000a4400000|100%| E|CS|TAMS 0x00000000a4300000| PB 0x00000000a4300000| Complete 
| 580|0x00000000a4400000, 0x00000000a4500000, 0x00000000a4500000|100%| E|CS|TAMS 0x00000000a4400000| PB 0x00000000a4400000| Complete 
| 581|0x00000000a4500000, 0x00000000a4600000, 0x00000000a4600000|100%| E|CS|TAMS 0x00000000a4500000| PB 0x00000000a4500000| Complete 
| 582|0x00000000a4600000, 0x00000000a4700000, 0x00000000a4700000|100%| E|CS|TAMS 0x00000000a4600000| PB 0x00000000a4600000| Complete 
| 583|0x00000000a4700000, 0x00000000a4800000, 0x00000000a4800000|100%| E|CS|TAMS 0x00000000a4700000| PB 0x00000000a4700000| Complete 
| 584|0x00000000a4800000, 0x00000000a4900000, 0x00000000a4900000|100%| E|CS|TAMS 0x00000000a4800000| PB 0x00000000a4800000| Complete 
| 585|0x00000000a4900000, 0x00000000a4a00000, 0x00000000a4a00000|100%| E|CS|TAMS 0x00000000a4900000| PB 0x00000000a4900000| Complete 
| 586|0x00000000a4a00000, 0x00000000a4b00000, 0x00000000a4b00000|100%| E|CS|TAMS 0x00000000a4a00000| PB 0x00000000a4a00000| Complete 
| 587|0x00000000a4b00000, 0x00000000a4c00000, 0x00000000a4c00000|100%| E|CS|TAMS 0x00000000a4b00000| PB 0x00000000a4b00000| Complete 
| 588|0x00000000a4c00000, 0x00000000a4d00000, 0x00000000a4d00000|100%| E|CS|TAMS 0x00000000a4c00000| PB 0x00000000a4c00000| Complete 
| 589|0x00000000a4d00000, 0x00000000a4e00000, 0x00000000a4e00000|100%| E|CS|TAMS 0x00000000a4d00000| PB 0x00000000a4d00000| Complete 
| 590|0x00000000a4e00000, 0x00000000a4f00000, 0x00000000a4f00000|100%| E|CS|TAMS 0x00000000a4e00000| PB 0x00000000a4e00000| Complete 
| 591|0x00000000a4f00000, 0x00000000a5000000, 0x00000000a5000000|100%| E|CS|TAMS 0x00000000a4f00000| PB 0x00000000a4f00000| Complete 
| 592|0x00000000a5000000, 0x00000000a5100000, 0x00000000a5100000|100%| E|CS|TAMS 0x00000000a5000000| PB 0x00000000a5000000| Complete 
| 593|0x00000000a5100000, 0x00000000a5200000, 0x00000000a5200000|100%| E|CS|TAMS 0x00000000a5100000| PB 0x00000000a5100000| Complete 
| 594|0x00000000a5200000, 0x00000000a5300000, 0x00000000a5300000|100%| E|CS|TAMS 0x00000000a5200000| PB 0x00000000a5200000| Complete 
| 595|0x00000000a5300000, 0x00000000a5400000, 0x00000000a5400000|100%| E|CS|TAMS 0x00000000a5300000| PB 0x00000000a5300000| Complete 
| 596|0x00000000a5400000, 0x00000000a5500000, 0x00000000a5500000|100%| E|CS|TAMS 0x00000000a5400000| PB 0x00000000a5400000| Complete 
| 597|0x00000000a5500000, 0x00000000a5600000, 0x00000000a5600000|100%| E|CS|TAMS 0x00000000a5500000| PB 0x00000000a5500000| Complete 
| 598|0x00000000a5600000, 0x00000000a5700000, 0x00000000a5700000|100%| E|CS|TAMS 0x00000000a5600000| PB 0x00000000a5600000| Complete 
| 599|0x00000000a5700000, 0x00000000a5800000, 0x00000000a5800000|100%| E|CS|TAMS 0x00000000a5700000| PB 0x00000000a5700000| Complete 
| 600|0x00000000a5800000, 0x00000000a5900000, 0x00000000a5900000|100%| E|CS|TAMS 0x00000000a5800000| PB 0x00000000a5800000| Complete 
| 601|0x00000000a5900000, 0x00000000a5a00000, 0x00000000a5a00000|100%| E|CS|TAMS 0x00000000a5900000| PB 0x00000000a5900000| Complete 
| 602|0x00000000a5a00000, 0x00000000a5b00000, 0x00000000a5b00000|100%| E|CS|TAMS 0x00000000a5a00000| PB 0x00000000a5a00000| Complete 
| 603|0x00000000a5b00000, 0x00000000a5c00000, 0x00000000a5c00000|100%| E|CS|TAMS 0x00000000a5b00000| PB 0x00000000a5b00000| Complete 
| 604|0x00000000a5c00000, 0x00000000a5d00000, 0x00000000a5d00000|100%| E|CS|TAMS 0x00000000a5c00000| PB 0x00000000a5c00000| Complete 
| 605|0x00000000a5d00000, 0x00000000a5e00000, 0x00000000a5e00000|100%| E|CS|TAMS 0x00000000a5d00000| PB 0x00000000a5d00000| Complete 
| 606|0x00000000a5e00000, 0x00000000a5f00000, 0x00000000a5f00000|100%| E|CS|TAMS 0x00000000a5e00000| PB 0x00000000a5e00000| Complete 
| 607|0x00000000a5f00000, 0x00000000a6000000, 0x00000000a6000000|100%| E|CS|TAMS 0x00000000a5f00000| PB 0x00000000a5f00000| Complete 
| 608|0x00000000a6000000, 0x00000000a6100000, 0x00000000a6100000|100%| E|CS|TAMS 0x00000000a6000000| PB 0x00000000a6000000| Complete 
| 609|0x00000000a6100000, 0x00000000a6200000, 0x00000000a6200000|100%| E|CS|TAMS 0x00000000a6100000| PB 0x00000000a6100000| Complete 
| 610|0x00000000a6200000, 0x00000000a6300000, 0x00000000a6300000|100%| E|CS|TAMS 0x00000000a6200000| PB 0x00000000a6200000| Complete 
| 611|0x00000000a6300000, 0x00000000a6400000, 0x00000000a6400000|100%| E|CS|TAMS 0x00000000a6300000| PB 0x00000000a6300000| Complete 
| 612|0x00000000a6400000, 0x00000000a6500000, 0x00000000a6500000|100%| E|CS|TAMS 0x00000000a6400000| PB 0x00000000a6400000| Complete 
| 613|0x00000000a6500000, 0x00000000a6600000, 0x00000000a6600000|100%| E|CS|TAMS 0x00000000a6500000| PB 0x00000000a6500000| Complete 
| 614|0x00000000a6600000, 0x00000000a6700000, 0x00000000a6700000|100%| E|CS|TAMS 0x00000000a6600000| PB 0x00000000a6600000| Complete 
| 615|0x00000000a6700000, 0x00000000a6800000, 0x00000000a6800000|100%| E|CS|TAMS 0x00000000a6700000| PB 0x00000000a6700000| Complete 
| 616|0x00000000a6800000, 0x00000000a6900000, 0x00000000a6900000|100%| E|CS|TAMS 0x00000000a6800000| PB 0x00000000a6800000| Complete 
| 617|0x00000000a6900000, 0x00000000a6a00000, 0x00000000a6a00000|100%| E|CS|TAMS 0x00000000a6900000| PB 0x00000000a6900000| Complete 
| 618|0x00000000a6a00000, 0x00000000a6b00000, 0x00000000a6b00000|100%| E|CS|TAMS 0x00000000a6a00000| PB 0x00000000a6a00000| Complete 
| 619|0x00000000a6b00000, 0x00000000a6c00000, 0x00000000a6c00000|100%| E|CS|TAMS 0x00000000a6b00000| PB 0x00000000a6b00000| Complete 
| 620|0x00000000a6c00000, 0x00000000a6d00000, 0x00000000a6d00000|100%| E|CS|TAMS 0x00000000a6c00000| PB 0x00000000a6c00000| Complete 
| 621|0x00000000a6d00000, 0x00000000a6e00000, 0x00000000a6e00000|100%| E|CS|TAMS 0x00000000a6d00000| PB 0x00000000a6d00000| Complete 
| 622|0x00000000a6e00000, 0x00000000a6f00000, 0x00000000a6f00000|100%| E|CS|TAMS 0x00000000a6e00000| PB 0x00000000a6e00000| Complete 
| 623|0x00000000a6f00000, 0x00000000a7000000, 0x00000000a7000000|100%| E|CS|TAMS 0x00000000a6f00000| PB 0x00000000a6f00000| Complete 
| 624|0x00000000a7000000, 0x00000000a7100000, 0x00000000a7100000|100%| E|CS|TAMS 0x00000000a7000000| PB 0x00000000a7000000| Complete 
| 625|0x00000000a7100000, 0x00000000a7200000, 0x00000000a7200000|100%| E|CS|TAMS 0x00000000a7100000| PB 0x00000000a7100000| Complete 
| 626|0x00000000a7200000, 0x00000000a7300000, 0x00000000a7300000|100%| E|CS|TAMS 0x00000000a7200000| PB 0x00000000a7200000| Complete 
| 627|0x00000000a7300000, 0x00000000a7400000, 0x00000000a7400000|100%| E|CS|TAMS 0x00000000a7300000| PB 0x00000000a7300000| Complete 
| 628|0x00000000a7400000, 0x00000000a7500000, 0x00000000a7500000|100%| E|CS|TAMS 0x00000000a7400000| PB 0x00000000a7400000| Complete 
|1994|0x00000000fca00000, 0x00000000fcb00000, 0x00000000fcb00000|100%| O|  |TAMS 0x00000000fca00000| PB 0x00000000fca00000| Untracked 
|1995|0x00000000fcb00000, 0x00000000fcc00000, 0x00000000fcc00000|100%| E|CS|TAMS 0x00000000fcb00000| PB 0x00000000fcb00000| Complete 
|1996|0x00000000fcc00000, 0x00000000fcd00000, 0x00000000fcd00000|100%| E|CS|TAMS 0x00000000fcc00000| PB 0x00000000fcc00000| Complete 
|1997|0x00000000fcd00000, 0x00000000fce00000, 0x00000000fce00000|100%| E|CS|TAMS 0x00000000fcd00000| PB 0x00000000fcd00000| Complete 
|1998|0x00000000fce00000, 0x00000000fcf00000, 0x00000000fcf00000|100%| E|CS|TAMS 0x00000000fce00000| PB 0x00000000fce00000| Complete 
|1999|0x00000000fcf00000, 0x00000000fd000000, 0x00000000fd000000|100%| E|CS|TAMS 0x00000000fcf00000| PB 0x00000000fcf00000| Complete 
|2000|0x00000000fd000000, 0x00000000fd100000, 0x00000000fd100000|100%| E|CS|TAMS 0x00000000fd000000| PB 0x00000000fd000000| Complete 
|2001|0x00000000fd100000, 0x00000000fd200000, 0x00000000fd200000|100%| E|CS|TAMS 0x00000000fd100000| PB 0x00000000fd100000| Complete 
|2002|0x00000000fd200000, 0x00000000fd300000, 0x00000000fd300000|100%| E|CS|TAMS 0x00000000fd200000| PB 0x00000000fd200000| Complete 
|2003|0x00000000fd300000, 0x00000000fd400000, 0x00000000fd400000|100%| E|CS|TAMS 0x00000000fd300000| PB 0x00000000fd300000| Complete 
|2004|0x00000000fd400000, 0x00000000fd500000, 0x00000000fd500000|100%| E|CS|TAMS 0x00000000fd400000| PB 0x00000000fd400000| Complete 
|2005|0x00000000fd500000, 0x00000000fd600000, 0x00000000fd600000|100%| E|CS|TAMS 0x00000000fd500000| PB 0x00000000fd500000| Complete 
|2006|0x00000000fd600000, 0x00000000fd700000, 0x00000000fd700000|100%| E|CS|TAMS 0x00000000fd600000| PB 0x00000000fd600000| Complete 
|2007|0x00000000fd700000, 0x00000000fd800000, 0x00000000fd800000|100%| E|CS|TAMS 0x00000000fd700000| PB 0x00000000fd700000| Complete 
|2008|0x00000000fd800000, 0x00000000fd900000, 0x00000000fd900000|100%| E|CS|TAMS 0x00000000fd800000| PB 0x00000000fd800000| Complete 
|2009|0x00000000fd900000, 0x00000000fda00000, 0x00000000fda00000|100%| E|CS|TAMS 0x00000000fd900000| PB 0x00000000fd900000| Complete 
|2010|0x00000000fda00000, 0x00000000fdb00000, 0x00000000fdb00000|100%| E|CS|TAMS 0x00000000fda00000| PB 0x00000000fda00000| Complete 
|2011|0x00000000fdb00000, 0x00000000fdc00000, 0x00000000fdc00000|100%| E|CS|TAMS 0x00000000fdb00000| PB 0x00000000fdb00000| Complete 
|2012|0x00000000fdc00000, 0x00000000fdd00000, 0x00000000fdd00000|100%| E|CS|TAMS 0x00000000fdc00000| PB 0x00000000fdc00000| Complete 
|2013|0x00000000fdd00000, 0x00000000fde00000, 0x00000000fde00000|100%| E|CS|TAMS 0x00000000fdd00000| PB 0x00000000fdd00000| Complete 
|2014|0x00000000fde00000, 0x00000000fdf00000, 0x00000000fdf00000|100%| E|CS|TAMS 0x00000000fde00000| PB 0x00000000fde00000| Complete 
|2015|0x00000000fdf00000, 0x00000000fe000000, 0x00000000fe000000|100%| E|CS|TAMS 0x00000000fdf00000| PB 0x00000000fdf00000| Complete 
|2016|0x00000000fe000000, 0x00000000fe100000, 0x00000000fe100000|100%| E|CS|TAMS 0x00000000fe000000| PB 0x00000000fe000000| Complete 
|2017|0x00000000fe100000, 0x00000000fe200000, 0x00000000fe200000|100%| E|CS|TAMS 0x00000000fe100000| PB 0x00000000fe100000| Complete 
|2018|0x00000000fe200000, 0x00000000fe300000, 0x00000000fe300000|100%| E|CS|TAMS 0x00000000fe200000| PB 0x00000000fe200000| Complete 
|2019|0x00000000fe300000, 0x00000000fe400000, 0x00000000fe400000|100%| E|CS|TAMS 0x00000000fe300000| PB 0x00000000fe300000| Complete 
|2020|0x00000000fe400000, 0x00000000fe500000, 0x00000000fe500000|100%| E|CS|TAMS 0x00000000fe400000| PB 0x00000000fe400000| Complete 
|2021|0x00000000fe500000, 0x00000000fe600000, 0x00000000fe600000|100%| E|CS|TAMS 0x00000000fe500000| PB 0x00000000fe500000| Complete 
|2022|0x00000000fe600000, 0x00000000fe700000, 0x00000000fe700000|100%| E|CS|TAMS 0x00000000fe600000| PB 0x00000000fe600000| Complete 
|2023|0x00000000fe700000, 0x00000000fe800000, 0x00000000fe800000|100%| E|CS|TAMS 0x00000000fe700000| PB 0x00000000fe700000| Complete 
|2024|0x00000000fe800000, 0x00000000fe900000, 0x00000000fe900000|100%| E|CS|TAMS 0x00000000fe800000| PB 0x00000000fe800000| Complete 
|2025|0x00000000fe900000, 0x00000000fea00000, 0x00000000fea00000|100%| E|CS|TAMS 0x00000000fe900000| PB 0x00000000fe900000| Complete 
|2026|0x00000000fea00000, 0x00000000feb00000, 0x00000000feb00000|100%| E|CS|TAMS 0x00000000fea00000| PB 0x00000000fea00000| Complete 
|2027|0x00000000feb00000, 0x00000000fec00000, 0x00000000fec00000|100%| E|CS|TAMS 0x00000000feb00000| PB 0x00000000feb00000| Complete 
|2028|0x00000000fec00000, 0x00000000fed00000, 0x00000000fed00000|100%| E|CS|TAMS 0x00000000fec00000| PB 0x00000000fec00000| Complete 
|2029|0x00000000fed00000, 0x00000000fee00000, 0x00000000fee00000|100%| E|CS|TAMS 0x00000000fed00000| PB 0x00000000fed00000| Complete 
|2030|0x00000000fee00000, 0x00000000fef00000, 0x00000000fef00000|100%| E|CS|TAMS 0x00000000fee00000| PB 0x00000000fee00000| Complete 
|2031|0x00000000fef00000, 0x00000000ff000000, 0x00000000ff000000|100%| E|CS|TAMS 0x00000000fef00000| PB 0x00000000fef00000| Complete 
|2032|0x00000000ff000000, 0x00000000ff100000, 0x00000000ff100000|100%| E|CS|TAMS 0x00000000ff000000| PB 0x00000000ff000000| Complete 
|2033|0x00000000ff100000, 0x00000000ff200000, 0x00000000ff200000|100%| E|CS|TAMS 0x00000000ff100000| PB 0x00000000ff100000| Complete 
|2034|0x00000000ff200000, 0x00000000ff300000, 0x00000000ff300000|100%| E|CS|TAMS 0x00000000ff200000| PB 0x00000000ff200000| Complete 
|2035|0x00000000ff300000, 0x00000000ff400000, 0x00000000ff400000|100%| E|CS|TAMS 0x00000000ff300000| PB 0x00000000ff300000| Complete 
|2036|0x00000000ff400000, 0x00000000ff500000, 0x00000000ff500000|100%| E|CS|TAMS 0x00000000ff400000| PB 0x00000000ff400000| Complete 
|2037|0x00000000ff500000, 0x00000000ff600000, 0x00000000ff600000|100%| E|CS|TAMS 0x00000000ff500000| PB 0x00000000ff500000| Complete 
|2038|0x00000000ff600000, 0x00000000ff700000, 0x00000000ff700000|100%| E|CS|TAMS 0x00000000ff600000| PB 0x00000000ff600000| Complete 
|2039|0x00000000ff700000, 0x00000000ff800000, 0x00000000ff800000|100%| E|CS|TAMS 0x00000000ff700000| PB 0x00000000ff700000| Complete 
|2040|0x00000000ff800000, 0x00000000ff900000, 0x00000000ff900000|100%| E|CS|TAMS 0x00000000ff800000| PB 0x00000000ff800000| Complete 
|2041|0x00000000ff900000, 0x00000000ffa00000, 0x00000000ffa00000|100%| E|CS|TAMS 0x00000000ff900000| PB 0x00000000ff900000| Complete 
|2042|0x00000000ffa00000, 0x00000000ffb00000, 0x00000000ffb00000|100%| E|CS|TAMS 0x00000000ffa00000| PB 0x00000000ffa00000| Complete 
|2043|0x00000000ffb00000, 0x00000000ffc00000, 0x00000000ffc00000|100%| E|CS|TAMS 0x00000000ffb00000| PB 0x00000000ffb00000| Complete 
|2044|0x00000000ffc00000, 0x00000000ffd00000, 0x00000000ffd00000|100%| E|CS|TAMS 0x00000000ffc00000| PB 0x00000000ffc00000| Complete 
|2045|0x00000000ffd00000, 0x00000000ffe00000, 0x00000000ffe00000|100%| E|CS|TAMS 0x00000000ffd00000| PB 0x00000000ffd00000| Complete 
|2046|0x00000000ffe00000, 0x00000000fff00000, 0x00000000fff00000|100%| E|CS|TAMS 0x00000000ffe00000| PB 0x00000000ffe00000| Complete 
|2047|0x00000000fff00000, 0x0000000100000000, 0x0000000100000000|100%| E|CS|TAMS 0x00000000fff00000| PB 0x00000000fff00000| Complete 

Card table byte_map: [0x00000141dcd60000,0x00000141dd160000] _byte_map_base: 0x00000141dc960000

Marking Bits: (CMBitMap*) 0x00000141c521b390
 Bits: [0x00000141dd160000, 0x00000141df160000)

Polling page: 0x00000141c31b0000

Metaspace:

Usage:
  Non-class:    120.65 MB used.
      Class:     18.25 MB used.
       Both:    138.90 MB used.

Virtual space:
  Non-class space:      128.00 MB reserved,     121.88 MB ( 95%) committed,  2 nodes.
      Class space:        1.00 GB reserved,      19.44 MB (  2%) committed,  1 nodes.
             Both:        1.12 GB reserved,     141.31 MB ( 12%) committed. 

Chunk freelists:
   Non-Class:  5.36 MB
       Class:  12.44 MB
        Both:  17.80 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 235.25 MB
CDS: on
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 6.
num_arena_births: 4436.
num_arena_deaths: 2.
num_vsnodes_births: 3.
num_vsnodes_deaths: 0.
num_space_committed: 2254.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 8.
num_chunks_taken_from_freelist: 10628.
num_chunk_merges: 3.
num_chunk_splits: 7025.
num_chunks_enlarged: 4598.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=23693Kb max_used=23693Kb free=96306Kb
 bounds [0x00000141d5100000, 0x00000141d6830000, 0x00000141dc630000]
CodeHeap 'profiled nmethods': size=120000Kb used=41898Kb max_used=44857Kb free=78101Kb
 bounds [0x00000141cd630000, 0x00000141d02b0000, 0x00000141d4b60000]
CodeHeap 'non-nmethods': size=5760Kb used=3145Kb max_used=3237Kb free=2614Kb
 bounds [0x00000141d4b60000, 0x00000141d4ea0000, 0x00000141d5100000]
 total_blobs=21306 nmethods=20128 adapters=1078
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 72.941 Thread 0x00000141e1c6c6c0 31752       3       com.android.tools.r8.kotlin.r::b (76 bytes)
Event: 72.945 Thread 0x00000141e1c6c6c0 nmethod 31752 0x00000141cf7cad90 code [0x00000141cf7cb220, 0x00000141cf7cd588]
Event: 72.945 Thread 0x00000141e1c6c6c0 31753       3       com.android.tools.r8.internal.Mb0::a (38 bytes)
Event: 72.946 Thread 0x00000141e1c6c6c0 nmethod 31753 0x00000141cf596810 code [0x00000141cf596a40, 0x00000141cf597258]
Event: 72.949 Thread 0x00000141e1c6c6c0 31754       3       com.android.tools.r8.internal.Ie::b (20 bytes)
Event: 72.949 Thread 0x00000141e1c6c6c0 nmethod 31754 0x00000141cf755110 code [0x00000141cf755320, 0x00000141cf755810]
Event: 72.954 Thread 0x00000141e1c6c6c0 31756       3       com.android.tools.r8.internal.L0::read (45 bytes)
Event: 72.954 Thread 0x00000141e1c6c6c0 nmethod 31756 0x00000141cf80a910 code [0x00000141cf80aae0, 0x00000141cf80ae90]
Event: 72.955 Thread 0x00000141e1c6c6c0 31757   !   3       com.android.tools.r8.internal.j1::a (48 bytes)
Event: 72.956 Thread 0x00000141e1c6c6c0 nmethod 31757 0x00000141cfbaeb90 code [0x00000141cfbaed80, 0x00000141cfbaf2d8]
Event: 72.956 Thread 0x00000141e1c6c6c0 31758       3       com.android.tools.r8.internal.j1::a (44 bytes)
Event: 72.956 Thread 0x00000141e1c6c6c0 nmethod 31758 0x00000141cdeaf110 code [0x00000141cdeaf300, 0x00000141cdeaf810]
Event: 72.957 Thread 0x00000141e1c6c6c0 31759       3       com.android.tools.r8.internal.tM::<init> (56 bytes)
Event: 72.957 Thread 0x00000141e1c6c6c0 nmethod 31759 0x00000141ce2c1e90 code [0x00000141ce2c2100, 0x00000141ce2c2b10]
Event: 72.957 Thread 0x00000141e1c6c6c0 31760       3       com.android.tools.r8.internal.dy0::<init> (47 bytes)
Event: 72.958 Thread 0x00000141e1c6c6c0 nmethod 31760 0x00000141ce032310 code [0x00000141ce032540, 0x00000141ce032e20]
Event: 72.973 Thread 0x00000141e1c6c6c0 31761       2       com.android.tools.r8.kotlin.g0::a (239 bytes)
Event: 72.975 Thread 0x00000141e1c6c6c0 nmethod 31761 0x00000141ce3cf990 code [0x00000141ce3cfdc0, 0x00000141ce3d10a0]
Event: 72.975 Thread 0x00000141e1c6c6c0 31762 % !   3       java.lang.StringLatin1::replace @ 190 (291 bytes)
Event: 72.977 Thread 0x00000141e1c6c6c0 nmethod 31762% 0x00000141cfba3d90 code [0x00000141cfba40c0, 0x00000141cfba5580]

GC Heap History (20 events):
Event: 48.912 GC heap before
{Heap before GC invocations=61 (full 0):
 garbage-first heap   total 699392K, used 574976K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 235 young (240640K), 7 survivors (7168K)
 Metaspace       used 140108K, committed 142592K, reserved 1179648K
  class space    used 18543K, committed 19776K, reserved 1048576K
}
Event: 48.935 GC heap after
{Heap after GC invocations=62 (full 0):
 garbage-first heap   total 699392K, used 367616K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 30 young (30720K), 30 survivors (30720K)
 Metaspace       used 140108K, committed 142592K, reserved 1179648K
  class space    used 18543K, committed 19776K, reserved 1048576K
}
Event: 50.378 GC heap before
{Heap before GC invocations=62 (full 0):
 garbage-first heap   total 699392K, used 615424K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 271 young (277504K), 30 survivors (30720K)
 Metaspace       used 140473K, committed 142976K, reserved 1179648K
  class space    used 18585K, committed 19840K, reserved 1048576K
}
Event: 50.422 GC heap after
{Heap after GC invocations=63 (full 0):
 garbage-first heap   total 699392K, used 326447K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 20 young (20480K), 20 survivors (20480K)
 Metaspace       used 140473K, committed 142976K, reserved 1179648K
  class space    used 18585K, committed 19840K, reserved 1048576K
}
Event: 53.715 GC heap before
{Heap before GC invocations=63 (full 0):
 garbage-first heap   total 699392K, used 602927K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 290 young (296960K), 20 survivors (20480K)
 Metaspace       used 140670K, committed 143168K, reserved 1179648K
  class space    used 18603K, committed 19840K, reserved 1048576K
}
Event: 53.746 GC heap after
{Heap after GC invocations=64 (full 0):
 garbage-first heap   total 699392K, used 310784K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 24 young (24576K), 24 survivors (24576K)
 Metaspace       used 140670K, committed 143168K, reserved 1179648K
  class space    used 18603K, committed 19840K, reserved 1048576K
}
Event: 55.600 GC heap before
{Heap before GC invocations=64 (full 0):
 garbage-first heap   total 699392K, used 632320K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 338 young (346112K), 24 survivors (24576K)
 Metaspace       used 140790K, committed 143296K, reserved 1179648K
  class space    used 18612K, committed 19840K, reserved 1048576K
}
Event: 55.619 GC heap after
{Heap after GC invocations=65 (full 0):
 garbage-first heap   total 699392K, used 314288K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 8 young (8192K), 8 survivors (8192K)
 Metaspace       used 140790K, committed 143296K, reserved 1179648K
  class space    used 18612K, committed 19840K, reserved 1048576K
}
Event: 58.507 GC heap before
{Heap before GC invocations=66 (full 0):
 garbage-first heap   total 699392K, used 623536K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 332 young (339968K), 8 survivors (8192K)
 Metaspace       used 141381K, committed 143872K, reserved 1179648K
  class space    used 18655K, committed 19904K, reserved 1048576K
}
Event: 58.518 GC heap after
{Heap after GC invocations=67 (full 0):
 garbage-first heap   total 699392K, used 293033K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 11 young (11264K), 11 survivors (11264K)
 Metaspace       used 141381K, committed 143872K, reserved 1179648K
  class space    used 18655K, committed 19904K, reserved 1048576K
}
Event: 60.863 GC heap before
{Heap before GC invocations=67 (full 0):
 garbage-first heap   total 699392K, used 650409K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 361 young (369664K), 11 survivors (11264K)
 Metaspace       used 141518K, committed 144000K, reserved 1179648K
  class space    used 18670K, committed 19904K, reserved 1048576K
}
Event: 60.875 GC heap after
{Heap after GC invocations=68 (full 0):
 garbage-first heap   total 699392K, used 289905K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 14 young (14336K), 14 survivors (14336K)
 Metaspace       used 141518K, committed 144000K, reserved 1179648K
  class space    used 18670K, committed 19904K, reserved 1048576K
}
Event: 65.435 GC heap before
{Heap before GC invocations=68 (full 0):
 garbage-first heap   total 699392K, used 648305K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 364 young (372736K), 14 survivors (14336K)
 Metaspace       used 142074K, committed 144512K, reserved 1179648K
  class space    used 18689K, committed 19904K, reserved 1048576K
}
Event: 65.470 GC heap after
{Heap after GC invocations=69 (full 0):
 garbage-first heap   total 699392K, used 257821K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 22 young (22528K), 22 survivors (22528K)
 Metaspace       used 142074K, committed 144512K, reserved 1179648K
  class space    used 18689K, committed 19904K, reserved 1048576K
}
Event: 65.680 GC heap before
{Heap before GC invocations=69 (full 0):
 garbage-first heap   total 699392K, used 305949K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 70 young (71680K), 22 survivors (22528K)
 Metaspace       used 142081K, committed 144512K, reserved 1179648K
  class space    used 18689K, committed 19904K, reserved 1048576K
}
Event: 65.698 GC heap after
{Heap after GC invocations=70 (full 0):
 garbage-first heap   total 699392K, used 261405K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 25 young (25600K), 25 survivors (25600K)
 Metaspace       used 142081K, committed 144512K, reserved 1179648K
  class space    used 18689K, committed 19904K, reserved 1048576K
}
Event: 69.727 GC heap before
{Heap before GC invocations=71 (full 0):
 garbage-first heap   total 699392K, used 636189K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 386 young (395264K), 25 survivors (25600K)
 Metaspace       used 142140K, committed 144576K, reserved 1179648K
  class space    used 18691K, committed 19904K, reserved 1048576K
}
Event: 69.747 GC heap after
{Heap after GC invocations=72 (full 0):
 garbage-first heap   total 699392K, used 259357K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 23 young (23552K), 23 survivors (23552K)
 Metaspace       used 142140K, committed 144576K, reserved 1179648K
  class space    used 18691K, committed 19904K, reserved 1048576K
}
Event: 71.952 GC heap before
{Heap before GC invocations=72 (full 0):
 garbage-first heap   total 699392K, used 639261K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 394 young (403456K), 23 survivors (23552K)
 Metaspace       used 142168K, committed 144640K, reserved 1179648K
  class space    used 18692K, committed 19904K, reserved 1048576K
}
Event: 71.971 GC heap after
{Heap after GC invocations=73 (full 0):
 garbage-first heap   total 699392K, used 247174K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 25 young (25600K), 25 survivors (25600K)
 Metaspace       used 142168K, committed 144640K, reserved 1179648K
  class space    used 18692K, committed 19904K, reserved 1048576K
}

Dll operation events (16 events):
Event: 0.008 Loaded shared library C:\Program Files\Java\jdk-21\bin\java.dll
Event: 0.039 Loaded shared library C:\Program Files\Java\jdk-21\bin\jsvml.dll
Event: 0.090 Loaded shared library C:\Program Files\Java\jdk-21\bin\zip.dll
Event: 0.094 Loaded shared library C:\Program Files\Java\jdk-21\bin\instrument.dll
Event: 0.099 Loaded shared library C:\Program Files\Java\jdk-21\bin\net.dll
Event: 0.100 Loaded shared library C:\Program Files\Java\jdk-21\bin\nio.dll
Event: 0.104 Loaded shared library C:\Program Files\Java\jdk-21\bin\zip.dll
Event: 0.365 Loaded shared library C:\Program Files\Java\jdk-21\bin\jimage.dll
Event: 0.496 Loaded shared library C:\Program Files\Java\jdk-21\bin\verify.dll
Event: 0.588 Loaded shared library C:\Users\<USER>\.gradle\native\1def1411415f61bf3af743bc5b6707747c0891f09f0c88961ee8f79bc544acac\windows-amd64\native-platform.dll
Event: 0.600 Loaded shared library C:\Users\<USER>\.gradle\native\0.2.7\x86_64-windows-gnu\gradle-fileevents.dll
Event: 1.497 Loaded shared library C:\Program Files\Java\jdk-21\bin\management.dll
Event: 1.499 Loaded shared library C:\Program Files\Java\jdk-21\bin\management_ext.dll
Event: 1.592 Loaded shared library C:\Program Files\Java\jdk-21\bin\extnet.dll
Event: 1.754 Loaded shared library C:\Program Files\Java\jdk-21\bin\sunmscapi.dll
Event: 17.695 Loaded shared library C:\Program Files\Java\jdk-21\bin\awt.dll

Deoptimization events (20 events):
Event: 72.846 Thread 0x00000141ebd84fb0 DEOPT PACKING pc=0x00000141ce66bbef sp=0x00000012c1ffa860
Event: 72.846 Thread 0x00000141ebd84fb0 DEOPT UNPACKING pc=0x00000141d4bb4e42 sp=0x00000012c1ff9e48 mode 0
Event: 72.858 Thread 0x00000141ebd84fb0 DEOPT PACKING pc=0x00000141ce66bbef sp=0x00000012c1ffa5f0
Event: 72.858 Thread 0x00000141ebd84fb0 DEOPT UNPACKING pc=0x00000141d4bb4e42 sp=0x00000012c1ff9bc8 mode 0
Event: 72.867 Thread 0x00000141ebd84fb0 DEOPT PACKING pc=0x00000141ce66bbef sp=0x00000012c1ffa6a0
Event: 72.867 Thread 0x00000141ebd84fb0 DEOPT UNPACKING pc=0x00000141d4bb4e42 sp=0x00000012c1ff9c88 mode 0
Event: 72.884 Thread 0x00000141ebd84fb0 DEOPT PACKING pc=0x00000141ce66bbef sp=0x00000012c1ffa870
Event: 72.884 Thread 0x00000141ebd84fb0 DEOPT UNPACKING pc=0x00000141d4bb4e42 sp=0x00000012c1ff9e58 mode 0
Event: 72.906 Thread 0x00000141ebd84fb0 DEOPT PACKING pc=0x00000141ce66bbef sp=0x00000012c1ffa860
Event: 72.906 Thread 0x00000141ebd84fb0 DEOPT UNPACKING pc=0x00000141d4bb4e42 sp=0x00000012c1ff9e48 mode 0
Event: 72.933 Thread 0x00000141ebd84fb0 DEOPT PACKING pc=0x00000141ce66bbef sp=0x00000012c1ffa870
Event: 72.933 Thread 0x00000141ebd84fb0 DEOPT UNPACKING pc=0x00000141d4bb4e42 sp=0x00000012c1ff9e58 mode 0
Event: 72.949 Thread 0x00000141ebd84fb0 Uncommon trap: trap_request=0xffffff45 fr.pc=0x00000141d52e9b8c relative=0x000000000000022c
Event: 72.949 Thread 0x00000141ebd84fb0 Uncommon trap: reason=unstable_if action=reinterpret pc=0x00000141d52e9b8c method=com.android.tools.r8.internal.Ie.e(I)V @ 11 c2
Event: 72.949 Thread 0x00000141ebd84fb0 DEOPT PACKING pc=0x00000141d52e9b8c sp=0x00000012c1ffa3c0
Event: 72.949 Thread 0x00000141ebd84fb0 DEOPT UNPACKING pc=0x00000141d4bb46a2 sp=0x00000012c1ffa200 mode 2
Event: 72.949 Thread 0x00000141ebd84fb0 DEOPT PACKING pc=0x00000141ce66bbef sp=0x00000012c1ffa690
Event: 72.950 Thread 0x00000141ebd84fb0 DEOPT UNPACKING pc=0x00000141d4bb4e42 sp=0x00000012c1ff9c78 mode 0
Event: 72.974 Thread 0x00000141ebd84fb0 DEOPT PACKING pc=0x00000141ce66bbef sp=0x00000012c1ffa860
Event: 72.974 Thread 0x00000141ebd84fb0 DEOPT UNPACKING pc=0x00000141d4bb4e42 sp=0x00000012c1ff9e48 mode 0

Classes loaded (20 events):
Event: 44.058 Loading class java/util/SortedSet$1
Event: 44.058 Loading class java/util/SortedSet$1 done
Event: 44.096 Loading class java/util/stream/SliceOps
Event: 44.096 Loading class java/util/stream/SliceOps done
Event: 44.096 Loading class java/util/stream/SliceOps$1
Event: 44.096 Loading class java/util/stream/SliceOps$1 done
Event: 44.178 Loading class java/util/stream/SliceOps$1$1
Event: 44.178 Loading class java/util/stream/SliceOps$1$1 done
Event: 46.531 Loading class java/util/concurrent/ConcurrentHashMap$TreeNode
Event: 46.531 Loading class java/util/concurrent/ConcurrentHashMap$TreeNode done
Event: 46.532 Loading class java/util/concurrent/ConcurrentHashMap$TreeBin
Event: 46.532 Loading class java/util/concurrent/ConcurrentHashMap$TreeBin done
Event: 48.045 Loading class java/util/LinkedList$LLSpliterator
Event: 48.045 Loading class java/util/LinkedList$LLSpliterator done
Event: 48.083 Loading class java/lang/Throwable$WrappedPrintWriter
Event: 48.084 Loading class java/lang/Throwable$PrintStreamOrWriter
Event: 48.084 Loading class java/lang/Throwable$PrintStreamOrWriter done
Event: 48.084 Loading class java/lang/Throwable$WrappedPrintWriter done
Event: 50.096 Loading class java/util/zip/ZipOutputStream$XEntry
Event: 50.096 Loading class java/util/zip/ZipOutputStream$XEntry done

Classes unloaded (1 events):
Event: 55.890 Thread 0x00000141e1ba42d0 Unloading class 0x00000141822e8c00 'java/lang/invoke/LambdaForm$DMH+0x00000141822e8c00'

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 44.475 Thread 0x00000141ebd80e10 Exception <a 'java/lang/NoSuchMethodError'{0x00000000a0dfdee0}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000a0dfdee0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 44.556 Thread 0x00000141ebd80e10 Exception <a 'java/lang/NoSuchMethodError'{0x00000000a0bb9510}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, int)'> (0x00000000a0bb9510) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 44.558 Thread 0x00000141ebd83570 Exception <a 'java/lang/NoSuchMethodError'{0x00000000a0a243a0}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, int)'> (0x00000000a0a243a0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 44.696 Thread 0x00000141ebd80e10 Exception <a 'java/lang/NoSuchMethodError'{0x00000000a04598d8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeInterface(java.lang.Object, java.lang.Object, int)'> (0x00000000a04598d8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 44.823 Thread 0x00000141e656dd90 Exception <a 'java/lang/IncompatibleClassChangeError'{0x00000000a0277f98}: Found class java.lang.Object, but interface was expected> (0x00000000a0277f98) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 840]
Event: 45.531 Thread 0x00000141e656dd90 Implicit null exception at 0x00000141d5dc89da to 0x00000141d5dc92e6
Event: 45.924 Thread 0x00000141ebd80e10 Exception <a 'java/lang/NoSuchMethodError'{0x000000009c2b2008}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecialIFC(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000009c2b2008) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 46.281 Thread 0x00000141e656dd90 Exception <a 'java/lang/ClassCastException'{0x000000009aafbbe0}: class kotlin.reflect.jvm.internal.ReflectionFactoryImpl cannot be cast to class com.android.tools.r8.internal.we0 (kotlin.reflect.jvm.internal.ReflectionFactoryImpl is in unnamed module of loader org.gradle.internal.classloader.VisitableURLClassLoader @3ac42916; com.android.tools.r8.internal.we0 is in unnamed module of loader org.gradle.internal.classloader.VisitableURLClassLoader$InstrumentingVisitableURLClassLoader @6849fcbd)> (0x000000009a
Event: 47.371 Thread 0x00000141e656dd90 Exception <a 'sun/nio/fs/WindowsException'{0x00000000fff08d70}> (0x00000000fff08d70) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 520]
Event: 48.088 Thread 0x00000141ebd83570 Exception <a 'java/lang/NoSuchMethodError'{0x00000000a5c5f740}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, int, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000a5c5f740) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 49.974 Thread 0x00000141ebd86360 Exception <a 'sun/nio/fs/WindowsException'{0x000000009dca5760}> (0x000000009dca5760) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 520]
Event: 50.097 Thread 0x00000141ebd86360 Exception <a 'sun/nio/fs/WindowsException'{0x000000009dcb0d48}> (0x000000009dcb0d48) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 520]
Event: 56.573 Thread 0x00000141ebd80e10 Exception <a 'java/lang/NoSuchMethodError'{0x00000000a40e8e20}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x00000000a40e8e20) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 57.013 Thread 0x00000141ebd83570 Exception <a 'sun/nio/fs/WindowsException'{0x00000000a0117570}> (0x00000000a0117570) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 520]
Event: 57.553 Thread 0x00000141ebd84fb0 Exception <a 'java/lang/NoSuchMethodError'{0x000000009c056698}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000009c056698) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 57.976 Thread 0x00000141ebd84fb0 Implicit null exception at 0x00000141d62763d3 to 0x00000141d6276b8c
Event: 58.402 Thread 0x00000141ebd80e10 Implicit null exception at 0x00000141d5776774 to 0x00000141d577a000
Event: 60.487 Thread 0x00000141ebd84fb0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000987fa390}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, int, int)'> (0x00000000987fa390) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 65.804 Thread 0x00000141ebd80e10 Exception <a 'sun/nio/fs/WindowsException'{0x00000000ffa59e10}> (0x00000000ffa59e10) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 520]
Event: 69.034 Thread 0x00000141ebd84fb0 Implicit null exception at 0x00000141d632c979 to 0x00000141d632ff19

ZGC Phase Switch (0 events):
No events

VM Operations (20 events):
Event: 65.942 Executing VM operation: G1PauseRemark
Event: 65.979 Executing VM operation: G1PauseRemark done
Event: 66.100 Executing VM operation: G1PauseCleanup
Event: 66.101 Executing VM operation: G1PauseCleanup done
Event: 67.101 Executing VM operation: Cleanup
Event: 67.102 Executing VM operation: Cleanup done
Event: 68.107 Executing VM operation: Cleanup
Event: 68.123 Executing VM operation: Cleanup done
Event: 68.841 Executing VM operation: ICBufferFull
Event: 68.841 Executing VM operation: ICBufferFull done
Event: 69.727 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause)
Event: 69.747 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause) done
Event: 70.753 Executing VM operation: Cleanup
Event: 70.753 Executing VM operation: Cleanup done
Event: 71.762 Executing VM operation: Cleanup
Event: 71.762 Executing VM operation: Cleanup done
Event: 71.952 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause)
Event: 71.971 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause) done
Event: 72.680 Executing VM operation: ICBufferFull
Event: 72.681 Executing VM operation: ICBufferFull done

Events (20 events):
Event: 65.975 Thread 0x00000141e1ba42d0 flushing nmethod 0x00000141d5a4d810
Event: 65.975 Thread 0x00000141e1ba42d0 flushing nmethod 0x00000141d543ca10
Event: 65.975 Thread 0x00000141e1ba42d0 flushing nmethod 0x00000141d513e290
Event: 65.975 Thread 0x00000141e1ba42d0 flushing nmethod 0x00000141d5585d90
Event: 65.975 Thread 0x00000141e1ba42d0 flushing nmethod 0x00000141d53e1e90
Event: 65.975 Thread 0x00000141e1ba42d0 flushing nmethod 0x00000141d5337590
Event: 65.975 Thread 0x00000141e1ba42d0 flushing nmethod 0x00000141d5335490
Event: 65.975 Thread 0x00000141e1ba42d0 flushing nmethod 0x00000141d531f810
Event: 65.975 Thread 0x00000141e1ba42d0 flushing nmethod 0x00000141d52e2d10
Event: 65.975 Thread 0x00000141e1ba42d0 flushing nmethod 0x00000141d5316a10
Event: 65.975 Thread 0x00000141e1ba42d0 flushing nmethod 0x00000141d5242a90
Event: 65.975 Thread 0x00000141e1ba42d0 flushing nmethod 0x00000141d527d990
Event: 65.975 Thread 0x00000141e1ba42d0 flushing nmethod 0x00000141d5270210
Event: 65.975 Thread 0x00000141e1ba42d0 flushing nmethod 0x00000141d5230490
Event: 65.975 Thread 0x00000141e1ba42d0 flushing nmethod 0x00000141d518dd10
Event: 65.975 Thread 0x00000141e1ba42d0 flushing nmethod 0x00000141d514b310
Event: 71.865 Thread 0x00000141f4b10f50 Thread exited: 0x00000141f4b10f50
Event: 71.943 Thread 0x00000141f48525c0 Thread added: 0x00000141f48525c0
Event: 72.553 Thread 0x00000141f48525c0 Thread exited: 0x00000141f48525c0
Event: 72.615 Thread 0x00000141f4d4cc20 Thread added: 0x00000141f4d4cc20


Dynamic libraries:
0x00007ff68dbe0000 - 0x00007ff68dbf0000 	C:\Program Files\Java\jdk-21\bin\java.exe
0x00007ffe0c390000 - 0x00007ffe0c588000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ffe0aa90000 - 0x00007ffe0ab52000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ffe09a70000 - 0x00007ffe09d66000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ffe0a280000 - 0x00007ffe0a380000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ffe03080000 - 0x00007ffe0309b000 	C:\Program Files\Java\jdk-21\bin\VCRUNTIME140.dll
0x00007ffe00280000 - 0x00007ffe00299000 	C:\Program Files\Java\jdk-21\bin\jli.dll
0x00007ffe0ba10000 - 0x00007ffe0bac1000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ffe0b840000 - 0x00007ffe0b8de000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ffe0ab60000 - 0x00007ffe0abff000 	C:\WINDOWS\System32\sechost.dll
0x00007ffe0a5f0000 - 0x00007ffe0a713000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ffe0a380000 - 0x00007ffe0a3a7000 	C:\WINDOWS\System32\bcrypt.dll
0x00007ffe0b0e0000 - 0x00007ffe0b27d000 	C:\WINDOWS\System32\USER32.dll
0x00007ffe09ec0000 - 0x00007ffe09ee2000 	C:\WINDOWS\System32\win32u.dll
0x00007ffdf9360000 - 0x00007ffdf95fa000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5915_none_60b4b9d171f9c7c7\COMCTL32.dll
0x00007ffe0af90000 - 0x00007ffe0afbb000 	C:\WINDOWS\System32\GDI32.dll
0x00007ffe09ef0000 - 0x00007ffe0a009000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ffe09d70000 - 0x00007ffe09e0d000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ffe012c0000 - 0x00007ffe012ca000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ffe0a410000 - 0x00007ffe0a43f000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ffe06d90000 - 0x00007ffe06d9c000 	C:\Program Files\Java\jdk-21\bin\vcruntime140_1.dll
0x00007ffddee40000 - 0x00007ffddeece000 	C:\Program Files\Java\jdk-21\bin\msvcp140.dll
0x00007ffd981f0000 - 0x00007ffd98f07000 	C:\Program Files\Java\jdk-21\bin\server\jvm.dll
0x00007ffe0ac90000 - 0x00007ffe0acfb000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ffe09870000 - 0x00007ffe098bb000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007ffdfe730000 - 0x00007ffdfe757000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ffe09850000 - 0x00007ffe09862000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007ffe08290000 - 0x00007ffe082a2000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ffe06940000 - 0x00007ffe0694a000 	C:\Program Files\Java\jdk-21\bin\jimage.dll
0x00007ffe07870000 - 0x00007ffe07a71000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ffdf88c0000 - 0x00007ffdf88f4000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ffe0a1f0000 - 0x00007ffe0a272000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ffdf87b0000 - 0x00007ffdf87bf000 	C:\Program Files\Java\jdk-21\bin\instrument.dll
0x00007ffdfa4a0000 - 0x00007ffdfa4bf000 	C:\Program Files\Java\jdk-21\bin\java.dll
0x00007ffe0bad0000 - 0x00007ffe0c23e000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ffe07a80000 - 0x00007ffe08224000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ffe0a730000 - 0x00007ffe0aa83000 	C:\WINDOWS\System32\combase.dll
0x00007ffe09380000 - 0x00007ffe093ab000 	C:\WINDOWS\SYSTEM32\Wldp.dll
0x00007ffe0b700000 - 0x00007ffe0b7cd000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ffe0ae00000 - 0x00007ffe0aead000 	C:\WINDOWS\System32\SHCORE.dll
0x00007ffe0c240000 - 0x00007ffe0c29b000 	C:\WINDOWS\System32\shlwapi.dll
0x00007ffe09950000 - 0x00007ffe09975000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ffdc9a10000 - 0x00007ffdc9ae7000 	C:\Program Files\Java\jdk-21\bin\jsvml.dll
0x00007ffdf08d0000 - 0x00007ffdf08e8000 	C:\Program Files\Java\jdk-21\bin\zip.dll
0x00007ffe06560000 - 0x00007ffe06570000 	C:\Program Files\Java\jdk-21\bin\net.dll
0x00007ffe05cc0000 - 0x00007ffe05dca000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007ffe090e0000 - 0x00007ffe0914a000 	C:\WINDOWS\system32\mswsock.dll
0x00007ffdf2ea0000 - 0x00007ffdf2eb6000 	C:\Program Files\Java\jdk-21\bin\nio.dll
0x00007ffdfa400000 - 0x00007ffdfa410000 	C:\Program Files\Java\jdk-21\bin\verify.dll
0x00007ffde7b10000 - 0x00007ffde7b37000 	C:\Users\<USER>\.gradle\native\1def1411415f61bf3af743bc5b6707747c0891f09f0c88961ee8f79bc544acac\windows-amd64\native-platform.dll
0x00007ffdc31b0000 - 0x00007ffdc3228000 	C:\Users\<USER>\.gradle\native\0.2.7\x86_64-windows-gnu\gradle-fileevents.dll
0x00007ffdfa6b0000 - 0x00007ffdfa6ba000 	C:\Program Files\Java\jdk-21\bin\management.dll
0x00007ffdfa490000 - 0x00007ffdfa49b000 	C:\Program Files\Java\jdk-21\bin\management_ext.dll
0x00007ffe0b830000 - 0x00007ffe0b838000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007ffe08dc0000 - 0x00007ffe08dfb000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007ffe0a720000 - 0x00007ffe0a728000 	C:\WINDOWS\System32\NSI.dll
0x00007ffdf86a0000 - 0x00007ffdf86a9000 	C:\Program Files\Java\jdk-21\bin\extnet.dll
0x00007ffe092d0000 - 0x00007ffe092e8000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007ffe08a00000 - 0x00007ffe08a38000 	C:\WINDOWS\system32\rsaenh.dll
0x00007ffe098d0000 - 0x00007ffe098fe000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007ffe092f0000 - 0x00007ffe092fc000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007ffdf8810000 - 0x00007ffdf881e000 	C:\Program Files\Java\jdk-21\bin\sunmscapi.dll
0x00007ffe0a090000 - 0x00007ffe0a1ed000 	C:\WINDOWS\System32\CRYPT32.dll
0x00007ffe093f0000 - 0x00007ffe09417000 	C:\WINDOWS\SYSTEM32\ncrypt.dll
0x00007ffe093b0000 - 0x00007ffe093eb000 	C:\WINDOWS\SYSTEM32\NTASN1.dll
0x00007ffdbabf0000 - 0x00007ffdbabf7000 	C:\WINDOWS\system32\wshunix.dll
0x00007ffdb7970000 - 0x00007ffdb7aff000 	C:\Program Files\Java\jdk-21\bin\awt.dll
0x00007ffe05c20000 - 0x00007ffe05cb4000 	C:\WINDOWS\SYSTEM32\apphelp.dll
0x00007ffe074c0000 - 0x00007ffe074ef000 	C:\WINDOWS\system32\DWMAPI.DLL
0x00007ffe070e0000 - 0x00007ffe0717e000 	C:\WINDOWS\system32\uxtheme.dll
0x00007ffe08c50000 - 0x00007ffe08c83000 	C:\WINDOWS\SYSTEM32\ntmarta.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Program Files\Java\jdk-21\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5915_none_60b4b9d171f9c7c7;C:\Program Files\Java\jdk-21\bin\server;C:\Users\<USER>\.gradle\native\1def1411415f61bf3af743bc5b6707747c0891f09f0c88961ee8f79bc544acac\windows-amd64;C:\Users\<USER>\.gradle\native\0.2.7\x86_64-windows-gnu

VM Arguments:
jvm_args: --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.xml/javax.xml.namespace=ALL-UNNAMED -Xmx2048m -Dfile.encoding=UTF-8 -Duser.country=US -Duser.language=en -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.14.2-bin\2pb3mgt1p815evrl3weanttgr\gradle-8.14.2\lib\agents\gradle-instrumentation-agent-8.14.2.jar 
java_command: org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.14.2
java_class_path (initial): C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.14.2-bin\2pb3mgt1p815evrl3weanttgr\gradle-8.14.2\lib\gradle-daemon-main-8.14.2.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
     uint ConcGCThreads                            = 2                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 8                                         {product} {ergonomic}
   size_t G1HeapRegionSize                         = 1048576                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 369098752                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 2147483648                                {product} {command line}
   size_t MaxNewSize                               = 1287651328                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 1048576                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 2147483648                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
CLASSPATH=C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\\gradle\wrapper\gradle-wrapper.jar
PATH=C:\Program Files\Python313\Scripts\;C:\Program Files\Python313\;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\xampp\php;C:\flutter sdk\flutter\bin;C:\Program Files\Java\jdk-21\bin;C:\OpenSSH-Win64\OpenSSH-Win64;C:\Windows\System32;C:\Program Files\Git\cmd;C:\Program Files\Git\bin;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;C:\xampp\php\php.exe;C:\scrcpy;C:\ProgramData\ComposerSetup\bin;C:\Program Files\nodejs\;C:\Program Files\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\xampp\php;C:\Program Files\Java\jdk1.8.0_111\bin;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\JetBrains\PyCharm Community Edition 2022.1.3\bin;C:\Program Files\Java\jdk1.8.0_291\bin;C:;C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Python 3.13;C:\Program Files\Void\bin;C:\Users\<USER>\platform-tools;C:\Program Files\Python313\Scripts\;C:\Program Files\Python313\;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\xampp\php;C:\flutter sdk\flutter\bin;C:\Program Files\Java\jdk-21\bin;C:\OpenSSH-Win64\OpenSSH-Win64;C:\Windows\System32;C:\Program Files\Git\cmd;C:\Program Files\Git\bin;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;C:\xampp\php\php.exe;C:\scrcpy;C:\ProgramData\ComposerSetup\bin;C:\Program Files\nodejs\;C:\Program Files\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\xampp\php;C:\Program Files\Java\jdk1.8.0_111\bin;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\JetBrains\PyCharm Community Edition 2022.1.3\bin;C:\Program Files\
USERNAME=ntc
OS=Windows_NT
PROCESSOR_IDENTIFIER=AMD64 Family 23 Model 24 Stepping 1, AuthenticAMD
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

---------------  S Y S T E M  ---------------

OS:
 Windows 10 , 64 bit Build 19041 (10.0.19041.5915)
OS uptime: 0 days 15:23 hours

CPU: total 8 (initial active 8) (8 cores per cpu, 2 threads per core) family 23 model 24 stepping 1 microcode 0x0, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4a, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, clmul, bmi1, bmi2, adx, sha, fma, vzeroupper, clflush, clflushopt, rdtscp, f16c
Processor Information for all 8 processors :
  Max Mhz: 3700, Current Mhz: 3700, Mhz Limit: 3700

Memory: 4k page, system-wide physical 22476M (4516M free)
TotalPageFile size 22476M (AvailPageFile size 5M)
current process WorkingSet (physical memory assigned to process): 1165M, peak: 1190M
current process commit charge ("private bytes"): 1217M, peak: 1249M

vm_info: Java HotSpot(TM) 64-Bit Server VM (21.0.2+13-LTS-58) for windows-amd64 JRE (21.0.2+13-LTS-58), built on 2024-01-05T18:32:24Z by "mach5one" with MS VC++ 17.1 (VS2022)

END.
