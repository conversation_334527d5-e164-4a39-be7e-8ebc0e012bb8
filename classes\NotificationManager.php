<?php
/**
 * Notification Manager Class
 * 
 * Handles push notification subscriptions, sending notifications,
 * and managing notification history for the attendance system.
 * 
 * @package MtcInvoice Attendance
 * @version 1.0
 */

class NotificationManager {
    private $db;
    private $vapidPublicKey;
    private $vapidPrivateKey;
    private $vapidSubject;
    
    public function __construct($database) {
        $this->db = $database;
        
        // VAPID keys for push notifications (replace with your actual keys)
        $this->vapidPublicKey = 'YOUR_VAPID_PUBLIC_KEY';
        $this->vapidPrivateKey = 'YOUR_VAPID_PRIVATE_KEY';
        $this->vapidSubject = 'mailto:<EMAIL>';
        
        $this->createTables();
    }
    
    /**
     * Create notification tables if they don't exist
     */
    private function createTables() {
        $sql = "
        CREATE TABLE IF NOT EXISTS push_subscriptions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT,
            endpoint TEXT NOT NULL,
            p256dh_key TEXT NOT NULL,
            auth_key TEXT NOT NULL,
            user_agent TEXT,
            ip_address VARCHAR(45),
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_user_id (user_id),
            INDEX idx_is_active (is_active)
        );
        
        CREATE TABLE IF NOT EXISTS notification_history (
            id INT AUTO_INCREMENT PRIMARY KEY,
            subscription_id INT,
            title VARCHAR(255) NOT NULL,
            body TEXT,
            data JSON,
            status ENUM('sent', 'failed', 'pending') DEFAULT 'pending',
            error_message TEXT,
            sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_subscription_id (subscription_id),
            INDEX idx_status (status),
            INDEX idx_sent_at (sent_at),
            FOREIGN KEY (subscription_id) REFERENCES push_subscriptions(id) ON DELETE CASCADE
        );
        ";
        
        $this->db->exec($sql);
    }
    
    /**
     * Save push notification subscription
     */
    public function saveSubscription($subscription, $userId = null) {
        try {
            $endpoint = $subscription['endpoint'];
            $keys = $subscription['keys'];
            $p256dhKey = $keys['p256dh'];
            $authKey = $keys['auth'];
            
            // Check if subscription already exists
            $stmt = $this->db->prepare("
                SELECT id FROM push_subscriptions 
                WHERE endpoint = ? AND is_active = TRUE
            ");
            $stmt->execute([$endpoint]);
            
            if ($stmt->fetch()) {
                // Update existing subscription
                $stmt = $this->db->prepare("
                    UPDATE push_subscriptions 
                    SET p256dh_key = ?, auth_key = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE endpoint = ?
                ");
                $stmt->execute([$p256dhKey, $authKey, $endpoint]);
                
                return ['message' => 'Subscription updated'];
            } else {
                // Create new subscription
                $stmt = $this->db->prepare("
                    INSERT INTO push_subscriptions (user_id, endpoint, p256dh_key, auth_key, user_agent, ip_address)
                    VALUES (?, ?, ?, ?, ?, ?)
                ");
                
                $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
                $ipAddress = $_SERVER['REMOTE_ADDR'] ?? '';
                
                $stmt->execute([
                    $userId,
                    $endpoint,
                    $p256dhKey,
                    $authKey,
                    $userAgent,
                    $ipAddress
                ]);
                
                return [
                    'id' => $this->db->lastInsertId(),
                    'message' => 'Subscription created'
                ];
            }
            
        } catch (Exception $e) {
            throw new Exception('Failed to save subscription: ' . $e->getMessage());
        }
    }
    
    /**
     * Get all active subscriptions
     */
    public function getSubscriptions($userId = null) {
        try {
            $sql = "
                SELECT id, user_id, endpoint, created_at, updated_at, user_agent
                FROM push_subscriptions 
                WHERE is_active = TRUE
            ";
            
            $params = [];
            
            if ($userId) {
                $sql .= " AND user_id = ?";
                $params[] = $userId;
            }
            
            $sql .= " ORDER BY created_at DESC";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            
            return $stmt->fetchAll();
            
        } catch (Exception $e) {
            throw new Exception('Failed to get subscriptions: ' . $e->getMessage());
        }
    }
    
    /**
     * Send push notification
     */
    public function sendNotification($notification, $recipients = []) {
        try {
            $title = $notification['title'] ?? 'Attendance Alert';
            $body = $notification['body'] ?? '';
            $data = $notification['data'] ?? [];
            $icon = $notification['icon'] ?? '/MtcInvoiceNewProject/assets/icons/icon-192x192.png';
            $badge = $notification['badge'] ?? '/MtcInvoiceNewProject/assets/icons/badge-72x72.png';
            $tag = $notification['tag'] ?? 'attendance-notification';
            
            // Get subscriptions to send to
            $subscriptions = $this->getTargetSubscriptions($recipients);
            
            $results = [];
            $successCount = 0;
            $failureCount = 0;
            
            foreach ($subscriptions as $subscription) {
                try {
                    $payload = json_encode([
                        'title' => $title,
                        'body' => $body,
                        'icon' => $icon,
                        'badge' => $badge,
                        'tag' => $tag,
                        'data' => $data,
                        'timestamp' => time()
                    ]);
                    
                    $result = $this->sendPushNotification(
                        $subscription['endpoint'],
                        $subscription['p256dh_key'],
                        $subscription['auth_key'],
                        $payload
                    );
                    
                    if ($result['success']) {
                        $successCount++;
                        $this->logNotification($subscription['id'], $title, $body, $data, 'sent');
                    } else {
                        $failureCount++;
                        $this->logNotification($subscription['id'], $title, $body, $data, 'failed', $result['error']);
                        
                        // Deactivate subscription if endpoint is invalid
                        if (strpos($result['error'], '410') !== false) {
                            $this->deactivateSubscription($subscription['id']);
                        }
                    }
                    
                    $results[] = [
                        'subscription_id' => $subscription['id'],
                        'success' => $result['success'],
                        'error' => $result['error'] ?? null
                    ];
                    
                } catch (Exception $e) {
                    $failureCount++;
                    $this->logNotification($subscription['id'], $title, $body, $data, 'failed', $e->getMessage());
                    
                    $results[] = [
                        'subscription_id' => $subscription['id'],
                        'success' => false,
                        'error' => $e->getMessage()
                    ];
                }
            }
            
            return [
                'total_sent' => count($subscriptions),
                'success_count' => $successCount,
                'failure_count' => $failureCount,
                'results' => $results
            ];
            
        } catch (Exception $e) {
            throw new Exception('Failed to send notification: ' . $e->getMessage());
        }
    }
    
    /**
     * Send notification for attendance alert
     */
    public function sendAlertNotification($alertId, $customMessage = null) {
        try {
            // Get alert details
            $stmt = $this->db->prepare("
                SELECT aa.*, w.name as worker_name, w.employee_id
                FROM attendance_alerts aa
                JOIN workers w ON aa.worker_id = w.id
                WHERE aa.id = ?
            ");
            $stmt->execute([$alertId]);
            $alert = $stmt->fetch();
            
            if (!$alert) {
                throw new Exception('Alert not found');
            }
            
            $title = 'Attendance Alert';
            $body = $customMessage ?: $alert['alert_message'];
            
            $notification = [
                'title' => $title,
                'body' => $body,
                'data' => [
                    'alert_id' => $alertId,
                    'worker_id' => $alert['worker_id'],
                    'worker_name' => $alert['worker_name'],
                    'employee_id' => $alert['employee_id'],
                    'severity' => $alert['severity'],
                    'url' => '/MtcInvoiceNewProject/admin/attendance/?alert=' . $alertId
                ],
                'tag' => 'attendance-alert-' . $alertId,
                'priority' => $alert['severity']
            ];
            
            return $this->sendNotification($notification);
            
        } catch (Exception $e) {
            throw new Exception('Failed to send alert notification: ' . $e->getMessage());
        }
    }
    
    /**
     * Get target subscriptions based on recipients
     */
    private function getTargetSubscriptions($recipients = []) {
        if (empty($recipients)) {
            // Send to all active subscriptions
            return $this->getSubscriptions();
        }
        
        $placeholders = str_repeat('?,', count($recipients) - 1) . '?';
        
        $stmt = $this->db->prepare("
            SELECT ps.id, ps.endpoint, ps.p256dh_key, ps.auth_key
            FROM push_subscriptions ps
            WHERE ps.is_active = TRUE AND ps.user_id IN ($placeholders)
        ");
        $stmt->execute($recipients);
        
        return $stmt->fetchAll();
    }
    
    /**
     * Send push notification using Web Push Protocol
     */
    private function sendPushNotification($endpoint, $p256dhKey, $authKey, $payload) {
        // This is a simplified implementation
        // In production, use a proper Web Push library like web-push-php
        
        try {
            $headers = [
                'Content-Type: application/json',
                'TTL: 86400' // 24 hours
            ];
            
            $ch = curl_init();
            curl_setopt_array($ch, [
                CURLOPT_URL => $endpoint,
                CURLOPT_POST => true,
                CURLOPT_POSTFIELDS => $payload,
                CURLOPT_HTTPHEADER => $headers,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_SSL_VERIFYPEER => false,
                CURLOPT_TIMEOUT => 30
            ]);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);
            
            if ($error) {
                return ['success' => false, 'error' => $error];
            }
            
            if ($httpCode >= 200 && $httpCode < 300) {
                return ['success' => true];
            } else {
                return ['success' => false, 'error' => "HTTP $httpCode: $response"];
            }
            
        } catch (Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
    
    /**
     * Log notification to history
     */
    private function logNotification($subscriptionId, $title, $body, $data, $status, $error = null) {
        try {
            $stmt = $this->db->prepare("
                INSERT INTO notification_history (subscription_id, title, body, data, status, error_message)
                VALUES (?, ?, ?, ?, ?, ?)
            ");
            
            $stmt->execute([
                $subscriptionId,
                $title,
                $body,
                json_encode($data),
                $status,
                $error
            ]);
            
        } catch (Exception $e) {
            error_log('Failed to log notification: ' . $e->getMessage());
        }
    }
    
    /**
     * Deactivate subscription
     */
    private function deactivateSubscription($subscriptionId) {
        try {
            $stmt = $this->db->prepare("
                UPDATE push_subscriptions 
                SET is_active = FALSE, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            ");
            $stmt->execute([$subscriptionId]);
            
        } catch (Exception $e) {
            error_log('Failed to deactivate subscription: ' . $e->getMessage());
        }
    }
    
    /**
     * Get notification history
     */
    public function getNotificationHistory($limit = 50, $offset = 0) {
        try {
            $stmt = $this->db->prepare("
                SELECT nh.*, ps.user_id, ps.endpoint
                FROM notification_history nh
                LEFT JOIN push_subscriptions ps ON nh.subscription_id = ps.id
                ORDER BY nh.sent_at DESC
                LIMIT ? OFFSET ?
            ");
            $stmt->execute([$limit, $offset]);
            
            return $stmt->fetchAll();
            
        } catch (Exception $e) {
            throw new Exception('Failed to get notification history: ' . $e->getMessage());
        }
    }
    
    /**
     * Get notification statistics
     */
    public function getNotificationStats() {
        try {
            $stmt = $this->db->prepare("
                SELECT 
                    COUNT(*) as total_subscriptions,
                    SUM(CASE WHEN is_active = TRUE THEN 1 ELSE 0 END) as active_subscriptions,
                    (SELECT COUNT(*) FROM notification_history WHERE sent_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)) as notifications_24h,
                    (SELECT COUNT(*) FROM notification_history WHERE status = 'sent' AND sent_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)) as successful_24h,
                    (SELECT COUNT(*) FROM notification_history WHERE status = 'failed' AND sent_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)) as failed_24h
                FROM push_subscriptions
            ");
            $stmt->execute();
            
            return $stmt->fetch();
            
        } catch (Exception $e) {
            throw new Exception('Failed to get notification stats: ' . $e->getMessage());
        }
    }
    
    /**
     * Update subscription
     */
    public function updateSubscription($subscriptionId, $updates) {
        try {
            $allowedFields = ['is_active', 'user_id'];
            $setClause = [];
            $params = [];
            
            foreach ($updates as $field => $value) {
                if (in_array($field, $allowedFields)) {
                    $setClause[] = "$field = ?";
                    $params[] = $value;
                }
            }
            
            if (empty($setClause)) {
                throw new Exception('No valid fields to update');
            }
            
            $params[] = $subscriptionId;
            
            $stmt = $this->db->prepare("
                UPDATE push_subscriptions 
                SET " . implode(', ', $setClause) . ", updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            ");
            $stmt->execute($params);
            
            return ['message' => 'Subscription updated successfully'];
            
        } catch (Exception $e) {
            throw new Exception('Failed to update subscription: ' . $e->getMessage());
        }
    }
    
    /**
     * Delete subscription
     */
    public function deleteSubscription($subscriptionId) {
        try {
            $stmt = $this->db->prepare("DELETE FROM push_subscriptions WHERE id = ?");
            $stmt->execute([$subscriptionId]);
            
            return ['message' => 'Subscription deleted successfully'];
            
        } catch (Exception $e) {
            throw new Exception('Failed to delete subscription: ' . $e->getMessage());
        }
    }
}
?>
