package com.official.invoicegenarator;

public class Config {
    // Server configuration
    public static final String SERVER_IP = "*************";
    public static final String PROJECT_NAME = "MtcInvoiceNewProject";
    public static final String API_VERSION = "v1";
    
    // Base URLs
    public static final String BASE_URL = "http://" + SERVER_IP + "/" + PROJECT_NAME + "/";
    public static final String API_BASE_URL = BASE_URL + "api/";
    public static final String UPLOADS_BASE_URL = BASE_URL + "api/uploads/";
    
    // API Endpoints
    public static final String UPLOAD_URL = API_BASE_URL + "files/upload.php";
    public static final String DOWNLOAD_URL = API_BASE_URL + "files/download.php";
    public static final String DELETE_URL = API_BASE_URL + "files/delete.php";
    public static final String LIST_URL = API_BASE_URL + "files/list.php";
    
    // File paths
    public static final String PDF_UPLOAD_PATH = "pdfs/";
    public static final String IMAGE_UPLOAD_PATH = "images/";
    
    // Timeouts (in seconds)
    public static final int CONNECTION_TIMEOUT = 30;
    public static final int READ_TIMEOUT = 30;
    
    // File size limits (in bytes)
    public static final long MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
    
    // Date formats
    public static final String DATE_FORMAT_SERVER = "yyyy-MM-dd HH:mm:ss";
    public static final String DATE_FORMAT_DISPLAY = "MMM dd, yyyy hh:mm a";
    
    // Shared Preferences
    public static final String PREF_NAME = "invoice_app_pref";
    public static final String KEY_IS_LOGGED_IN = "is_logged_in";
    public static final String KEY_USER_ID = "user_id";
    public static final String KEY_USER_EMAIL = "user_email";
    
    // Default values
    public static final int DEFAULT_PAGE_SIZE = 20;
}
