package com.official.invoicegenarator;

import android.Manifest;
import android.app.DownloadManager;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.PackageManager;
import android.database.Cursor;
import android.net.Uri;
import android.os.Build;
import android.os.Environment;
import android.webkit.URLUtil;
import android.widget.Toast;
import android.os.Bundle;

import java.io.File;
import java.net.URLConnection;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import android.os.Environment;
import android.text.Editable;
import android.text.TextWatcher;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.DividerItemDecoration;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.google.android.material.chip.ChipGroup;
import com.official.invoicegenarator.adapter.FileListAdapter;
import com.official.invoicegenarator.api.ApiClient;
import com.official.invoicegenarator.api.ApiService;
import com.official.invoicegenarator.model.FileInfo;
import com.official.invoicegenarator.model.FileListResponse;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;


public class DownloadListActivityFixed extends AppCompatActivity implements SwipeRefreshLayout.OnRefreshListener {

    private static final int PAGE_SIZE = 20; // Number of items per page
    private static final String TAG = "DownloadListActivity";
    private static final int PER_PAGE = 10; // Number of items per page
    private static final int PERMISSION_REQUEST_INTERNET = 1001;
    private static final int PERMISSION_REQUEST_WRITE_EXTERNAL_STORAGE = 1002;

    private RecyclerView recyclerView;
    private ProgressBar loadingProgressBar;
    private EditText searchEditText;
    private ChipGroup sortOptions;
    private SwipeRefreshLayout swipeRefreshLayout;
    private LinearLayout emptyStateView;
    private LinearLayout errorStateView;
    private TextView errorMessageTextView;
    private FileListAdapter fileListAdapter;

    private List<FileInfo> fileList;
    private List<FileInfo> allFiles;
    private int currentPage = 1;
    private boolean isLoading = false;
    private boolean isLastPage = false;
    private String currentSearchQuery = "";
    private String currentFilter = "";
    private String currentSortBy = "date";
    private boolean isAscending = false;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_download_list);
        
        // Initialize views
        initializeViews();
        setupSwipeRefresh();
        setupRecyclerView();
        
        // Load initial data
        loadFiles();
    }

    private void initializeViews() {
        recyclerView = findViewById(R.id.recyclerView);
        swipeRefreshLayout = findViewById(R.id.swipeRefreshLayout);
        emptyStateView = findViewById(R.id.emptyStateView);
        
        // Initialize error state views
        errorStateView = findViewById(R.id.errorStateView);
        errorMessageTextView = errorStateView.findViewById(R.id.errorMessageTextView);
        Button retryButton = errorStateView.findViewById(R.id.retryButton);
        retryButton.setOnClickListener(v -> retryLoading());
        
        loadingProgressBar = findViewById(R.id.loadingProgressBar);
        searchEditText = findViewById(R.id.searchEditText);
        sortOptions = findViewById(R.id.sortOptions);
        
        // Set up empty and error states
        if (emptyStateView != null) {
            emptyStateView.setVisibility(View.GONE);
        }
        if (errorStateView != null) {
            errorStateView.setVisibility(View.GONE);
        }
        
        // Initialize file list and adapter
        if (fileList == null) {
            fileList = new ArrayList<>();
        }
        if (allFiles == null) {
            allFiles = new ArrayList<>();
        }
        
        // Set up search
        searchEditText.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {}

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {}

            @Override
            public void afterTextChanged(Editable s) {
                currentSearchQuery = s.toString().trim();
                refreshFileList();
            }
        });
        
        // Set up sort options
        sortOptions.setOnCheckedChangeListener((group, checkedId) -> {
            if (checkedId == R.id.sortByDate) {
                currentSortBy = "date";
            } else if (checkedId == R.id.sortByName) {
                currentSortBy = "name";
            } else if (checkedId == R.id.sortBySize) {
                currentSortBy = "size";
            }
            refreshFileList();
        });
    }

    private void refreshFileList() {
        currentPage = 1;
        isLastPage = false;
        if (fileList != null) {
            fileList.clear();
        }
        if (allFiles != null) {
            allFiles.clear();
        }
        if (fileListAdapter != null) {
            fileListAdapter.notifyDataSetChanged();
        }
        loadFiles();
    }

    private void setupSwipeRefresh() {
        swipeRefreshLayout.setOnRefreshListener(this);
        swipeRefreshLayout.setColorSchemeResources(
                android.R.color.holo_blue_bright,
                android.R.color.holo_green_light,
                android.R.color.holo_orange_light,
                android.R.color.holo_red_light
        );
    }
    
    private void setupRecyclerView() {
        Log.d(TAG, "Setting up RecyclerView");
        LinearLayoutManager layoutManager = new LinearLayoutManager(this);
        recyclerView.setLayoutManager(layoutManager);
        
        // Add divider between items
        DividerItemDecoration dividerItemDecoration = new DividerItemDecoration(recyclerView.getContext(),
                layoutManager.getOrientation());
        recyclerView.addItemDecoration(dividerItemDecoration);
        
        // Initialize lists if null
        if (fileList == null) {
            fileList = new ArrayList<>();
        }
        if (allFiles == null) {
            allFiles = new ArrayList<>();
        }
        
        // Set up adapter
        fileListAdapter = new FileListAdapter(fileList);
        
        // Set click listener with logging
        fileListAdapter.setOnItemClickListener(new FileListAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(FileInfo file) {
                Log.d(TAG, "File item clicked: " + file.getName());
                if (file != null) {
                    openFile(file);
                } else {
                    Log.e(TAG, "Received null file in onItemClick");
                    Toast.makeText(DownloadListActivityFixed.this, 
                            "Error: Could not open file", Toast.LENGTH_SHORT).show();
                }
            }

            @Override
            public void onDeleteClick(FileInfo file) {
                Log.d(TAG, "Delete button clicked for file: " + (file != null ? file.getName() : "null"));
                if (file != null) {
                    deleteFile(file);
                } else {
                    Log.e(TAG, "Received null file in onDeleteClick");
                    Toast.makeText(DownloadListActivityFixed.this, 
                            "Error: Could not delete file", Toast.LENGTH_SHORT).show();
                }
            }

            @Override
            public void onDownloadClick(FileInfo file) {
                Log.d(TAG, "Download button clicked for file: " + (file != null ? file.getName() : "null"));
                if (file != null) {
                   // downloadFile(file);
                    openFile(file);
                } else {
                    Log.e(TAG, "Received null file in onDownloadClick");
                    Toast.makeText(DownloadListActivityFixed.this,
                            "Error: Could not download file", Toast.LENGTH_SHORT).show();
                }
            }
        });
        
        recyclerView.setAdapter(fileListAdapter);
        Log.d(TAG, "RecyclerView setup completed with " + fileList.size() + " items");
        
        // Add scroll listener for pagination
        recyclerView.addOnScrollListener(new RecyclerView.OnScrollListener() {
            @Override
            public void onScrolled(@NonNull RecyclerView recyclerView, int dx, int dy) {
                super.onScrolled(recyclerView, dx, dy);
                
                int visibleItemCount = layoutManager.getChildCount();
                int totalItemCount = layoutManager.getItemCount();
                int firstVisibleItemPosition = layoutManager.findFirstVisibleItemPosition();
                
                if (!isLoading && !isLastPage) {
                    if ((visibleItemCount + firstVisibleItemPosition) >= totalItemCount
                            && firstVisibleItemPosition >= 0
                            && totalItemCount >= PAGE_SIZE) {
                        loadNextPage();
                    }
                }
            }
        });
    }
    
//    private void downloadFile(FileInfo file) {
//        if (file == null) {
//            Log.e(TAG, "Cannot download file: File is null");
//            Toast.makeText(this, "Error: Could not download file", Toast.LENGTH_SHORT).show();
//            return;
//        }
//
//        // Get the download URL from the file object
//        String downloadUrl = file.getDownloadUrl();
//        if (downloadUrl == null || downloadUrl.isEmpty()) {
//            Log.e(TAG, "Download URL is null or empty");
//            Toast.makeText(this, "Error: Invalid download URL", Toast.LENGTH_SHORT).show();
//            return;
//        }
//
//        Log.d(TAG, "Downloading file from URL: " + downloadUrl);
//
//        try {
//            // Check if we have internet permission
//            if (ContextCompat.checkSelfPermission(this, Manifest.permission.INTERNET)
//                    != PackageManager.PERMISSION_GRANTED) {
//                // Request permission
//                ActivityCompat.requestPermissions(this,
//                        new String[]{Manifest.permission.INTERNET},
//                        PERMISSION_REQUEST_INTERNET);
//                return;
//            }
//
//            // Check if we have write external storage permission for downloads
//            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
//                if (checkSelfPermission(Manifest.permission.WRITE_EXTERNAL_STORAGE)
//                        != PackageManager.PERMISSION_GRANTED) {
//                    requestPermissions(new String[]{Manifest.permission.WRITE_EXTERNAL_STORAGE},
//                            PERMISSION_REQUEST_WRITE_EXTERNAL_STORAGE);
//                    return;
//                }
//            }
//
//            // Log the initial download attempt
//            Log.d(TAG, "Preparing to download file: " + file.getName());
//            Log.d(TAG, "Source URL: " + downloadUrl);
//
//            // URL encode the filename to handle special characters
//            String fileName = file.getName();
//            String encodedFileName = URLEncoder.encode(fileName, "UTF-8").replace("+", "%20");
//            Log.d(TAG, "Encoded filename: " + encodedFileName);
//
//            // Create a download request with the properly encoded URL
//            DownloadManager downloadManager = (DownloadManager) getSystemService(Context.DOWNLOAD_SERVICE);
//            if (downloadManager == null) {
//                throw new IllegalStateException("Cannot get the download service");
//            }
//
//            Uri downloadUri = Uri.parse(downloadUrl);
//            Log.d(TAG, "Parsed download URI: " + downloadUri);
//
//            DownloadManager.Request request = new DownloadManager.Request(downloadUri);
//
//            // Set the title and description for the download
//            request.setTitle(fileName);
//            request.setDescription("Downloading file...");
//
//            // Set the destination directory for the downloaded file
//            File downloadsDir = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS);
//            if (!downloadsDir.exists()) {
//                boolean dirCreated = downloadsDir.mkdirs();
//                Log.d(TAG, "Downloads directory created: " + dirCreated);
//            }
//
//            File destinationFile = new File(downloadsDir, fileName);
//            Log.d(TAG, "Initial destination file: " + destinationFile.getAbsolutePath());
//
//            // If file exists, add a timestamp to the filename
//            int counter = 1;
//            String baseName = fileName.contains(".") ?
//                fileName.substring(0, fileName.lastIndexOf('.')) : fileName;
//            String extension = fileName.contains(".") ?
//                fileName.substring(fileName.lastIndexOf('.')) : "";
//
//            while (destinationFile.exists()) {
//                fileName = baseName + "_" + counter + extension;
//                destinationFile = new File(downloadsDir, fileName);
//                Log.d(TAG, "File exists, trying new name: " + fileName);
//                counter++;
//            }
//
//            Log.d(TAG, "Final destination file: " + destinationFile.getAbsolutePath());
//
//            try {
//                request.setDestinationUri(Uri.fromFile(destinationFile));
//            } catch (Exception e) {
//                Log.e(TAG, "Error setting destination URI: " + e.getMessage(), e);
//                throw e;
//            }
//
//            // Set notification visibility
//            request.setNotificationVisibility(DownloadManager.Request.VISIBILITY_VISIBLE_NOTIFY_COMPLETED);
//
//            // Set MIME type based on file extension
//            String mimeType = URLConnection.guessContentTypeFromName(fileName);
//            if (mimeType == null) {
//                mimeType = "*/*";
//            }
//            Log.d(TAG, "Detected MIME type: " + mimeType);
//            request.setMimeType(mimeType);
//
//            // Allow all network types and roaming
//            request.setAllowedNetworkTypes(DownloadManager.Request.NETWORK_MOBILE | DownloadManager.Request.NETWORK_WIFI);
//            request.setAllowedOverRoaming(true);
//
//            // Add headers if needed
//            request.addRequestHeader("User-Agent", "Mozilla/5.0");
//
//            // Start the download
//            try {
//                Log.d(TAG, "Attempting to enqueue download...");
//                long downloadId = downloadManager.enqueue(request);
//                Log.d(TAG, "Download started successfully with ID: " + downloadId);
//                Log.d(TAG, "Download URL: " + downloadUrl);
//                Log.d(TAG, "Destination: " + destinationFile.getAbsolutePath());
//
//                // Register a broadcast receiver to track download completion
//                registerReceiver(new BroadcastReceiver() {
//                    @Override
//                    public void onReceive(Context context, Intent intent) {
//                        long id = intent.getLongExtra(DownloadManager.EXTRA_DOWNLOAD_ID, -1);
//                        if (id == downloadId) {
//                            Log.d(TAG, "Download completed for ID: " + id);
//                            // Check if the download was successful
//                            DownloadManager.Query query = new DownloadManager.Query();
//                            query.setFilterById(downloadId);
//                            Cursor cursor = downloadManager.query(query);
//                            if (cursor.moveToFirst()) {
//                                int status = cursor.getInt(cursor.getColumnIndex(DownloadManager.COLUMN_STATUS));
//                                if (status == DownloadManager.STATUS_SUCCESSFUL) {
//                                    Log.d(TAG, "Download successful");
//                                    Log.d(TAG, "File saved to: " + cursor.getString(cursor.getColumnIndex(DownloadManager.COLUMN_LOCAL_URI)));
//                                } else {
//                                    int reason = cursor.getInt(cursor.getColumnIndex(DownloadManager.COLUMN_REASON));
//                                    Log.e(TAG, "Download failed with reason: " + reason);
//                                }
//                            }
//                            cursor.close();
//                            unregisterReceiver(this);
//                        }
//                    }
//                }, new IntentFilter(DownloadManager.ACTION_DOWNLOAD_COMPLETE));
//
//                Toast.makeText(this, "Download started: " + fileName, Toast.LENGTH_SHORT).show();
//            } catch (IllegalArgumentException e) {
//                Log.e(TAG, "Invalid download URL: " + downloadUrl, e);
//                Toast.makeText(this, "Error: Invalid download URL - " + e.getMessage(), Toast.LENGTH_LONG).show();
//            } catch (SecurityException e) {
//                Log.e(TAG, "Security exception while starting download", e);
//                Toast.makeText(this, "Error: Permission denied - " + e.getMessage(), Toast.LENGTH_LONG).show();
//            }
//
//        } catch (Exception e) {
//            Log.e(TAG, "Error starting download for " + downloadUrl + ": " + e.getMessage(), e);
//            runOnUiThread(() -> {
//                Toast.makeText(this, "Error: Could not download file - " + e.getMessage(),
//                        Toast.LENGTH_LONG).show();
//            });
//        }
//    }

    private void openFile(FileInfo file) {
        if (file == null) {
            Log.e(TAG, "Cannot open file: File is null");
            Toast.makeText(this, "Error: Could not open file", Toast.LENGTH_SHORT).show();
            return;
        }

        // Get the view URL from the file object
        String viewUrl = file.getViewUrl();
        if (viewUrl == null || viewUrl.isEmpty()) {
            Log.e(TAG, "View URL is null or empty");
            Toast.makeText(this, "Error: Invalid file URL", Toast.LENGTH_SHORT).show();
            return;
        }

        Log.d(TAG, "Opening file in viewer: " + viewUrl);

        try {
            // Check if we have internet permission
            if (ContextCompat.checkSelfPermission(this, Manifest.permission.INTERNET) 
                    != PackageManager.PERMISSION_GRANTED) {
                // Request permission
                ActivityCompat.requestPermissions(this,
                        new String[]{Manifest.permission.INTERNET},
                        PERMISSION_REQUEST_INTERNET);
                return;
            }

            // Create a proper URI from the URL
            Uri uri = Uri.parse(viewUrl);
            
            // Create a view intent for PDF
            Intent intent = new Intent(Intent.ACTION_VIEW);
            intent.setDataAndType(uri, "application/pdf");
            intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK);
            intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
            
            // Create a chooser to let the user select how to open the PDF
            Intent chooser = Intent.createChooser(intent, "Open PDF with...");
            
            // Verify that the intent will resolve to an activity
            if (intent.resolveActivity(getPackageManager()) != null) {
                startActivity(chooser);
            } else {
                // If no PDF viewer is available, try opening in browser
                Intent browserIntent = new Intent(Intent.ACTION_VIEW, uri);
                startActivity(Intent.createChooser(browserIntent, "Open with..."));
            }
            
        } catch (Exception e) {
            Log.e(TAG, "Error opening file " + viewUrl + ": " + e.getMessage(), e);
            runOnUiThread(() -> {
                Toast.makeText(this, "Error: Could not open file - " + e.getMessage(), 
                        Toast.LENGTH_LONG).show();
            });
        }
    }
    
    private void deleteFile(FileInfo file) {
        // TODO: Implement file deletion logic
        // Show a confirmation dialog and then delete the file from the server
        new android.app.AlertDialog.Builder(this)
                .setTitle("Delete File")
                .setMessage("Are you sure you want to delete " + file.getName() + "?")
                .setPositiveButton(android.R.string.yes, (dialog, which) -> {
                    // Call API to delete the file
                    deleteFileFromServer(file);
                })
                .setNegativeButton(android.R.string.no, null)
                .setIcon(android.R.drawable.ic_dialog_alert)
                .show();
    }
    
    private void deleteFileFromServer(FileInfo file) {
        if (file == null) {
            Log.e(TAG, "Cannot delete file: File is null");
            return;
        }

        int fileId = file.getId();
        Log.d(TAG, "Deleting file with ID: " + fileId);
        
        // Show loading indicator
        showLoading(true);
        
        // Create API service
        ApiService apiService = ApiClient.getClient().create(ApiService.class);
        Call<FileListResponse> call = apiService.deleteFile(fileId);
        
        call.enqueue(new Callback<FileListResponse>() {
            @Override
            public void onResponse(Call<FileListResponse> call, Response<FileListResponse> response) {
                showLoading(false);
                
                if (response.isSuccessful() && response.body() != null && response.body().isSuccess()) {
                    // File deleted successfully
                    int position = fileList.indexOf(file);
                    if (position != -1) {
                        fileList.remove(position);
                        fileListAdapter.notifyItemRemoved(position);
                        
                        if (fileList.isEmpty()) {
                            showEmptyState(true);
                        }
                        
                        // Refresh the file list to ensure it's up to date
                        refreshFileList();
                        
                        Toast.makeText(DownloadListActivityFixed.this, 
                                "File deleted successfully", Toast.LENGTH_SHORT).show();
                    }
                } else {
                    // Handle error
                    String errorMsg = "Failed to delete file";
                    if (response.errorBody() != null) {
                        try {
                            errorMsg = response.errorBody().string();
                        } catch (IOException e) {
                            Log.e(TAG, "Error reading error response", e);
                        }
                    }
                    Log.e(TAG, errorMsg);
                    Toast.makeText(DownloadListActivityFixed.this, 
                            errorMsg, Toast.LENGTH_SHORT).show();
                }
            }

            @Override
            public void onFailure(Call<FileListResponse> call, Throwable t) {
                showLoading(false);
                String errorMsg = "Network error: " + t.getMessage();
                Log.e(TAG, errorMsg, t);
                Toast.makeText(DownloadListActivityFixed.this, 
                        errorMsg, Toast.LENGTH_SHORT).show();
            }
        });
    }

    private void setupSearchAndSort() {
        // Set up search functionality
        searchEditText.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {}

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {}

            @Override
            public void afterTextChanged(Editable s) {
                currentSearchQuery = s.toString().trim().toLowerCase();
                filterAndSortFiles();
            }
        });

        // Set up sort options
        sortOptions.setOnCheckedChangeListener((group, checkedId) -> {
            if (checkedId == R.id.sortByName) {
                currentSortBy = "name";
            } else if (checkedId == R.id.sortByDate) {
                currentSortBy = "date";
            } else if (checkedId == R.id.sortBySize) {
                currentSortBy = "size";
            }
            filterAndSortFiles();
        });
    }
    
    private void filterAndSortFiles() {
        if (allFiles == null || allFiles.isEmpty()) return;
        
        // Filter files based on search query
        List<FileInfo> filteredList = new ArrayList<>();
        if (currentSearchQuery.isEmpty()) {
            filteredList.addAll(allFiles);
        } else {
            for (FileInfo file : allFiles) {
                if (file.getName().toLowerCase().contains(currentSearchQuery)) {
                    filteredList.add(file);
                }
            }
        }
        
        // Sort the filtered list
        Collections.sort(filteredList, (f1, f2) -> {
            switch (currentSortBy) {
                case "name":
                    return f1.getName().compareToIgnoreCase(f2.getName());
                case "date":
                    return f2.getUploadDate().compareTo(f1.getUploadDate());
                case "size":
                    return Long.compare(f2.getSizeInBytes(), f1.getSizeInBytes());
                default:
                    return 0;
            }
        });
        
        // Update the adapter with filtered and sorted list
        fileList.clear();
        fileList.addAll(filteredList);
        fileListAdapter.notifyDataSetChanged();
        
        // Show empty state if no results
        showEmptyState(fileList.isEmpty());
    }
    

    
    private void loadNextPage() {
        if (!isLoading && !isLastPage) {
            currentPage++;
            loadFiles();
        }
    }
    

    private void retryLoading() {
        errorStateView.setVisibility(View.GONE);
        loadFiles();
    }

    private void loadFiles() {
        if (isLoading) return;
        
        // Ensure lists are initialized
        if (fileList == null) {
            fileList = new ArrayList<>();
        }
        if (allFiles == null) {
            allFiles = new ArrayList<>();
        }
        
        showLoading(true);
        isLoading = true;
        showEmptyState(false);
        showErrorState(false, "");
        
        // Create API service
        ApiService apiService = ApiClient.getClient().create(ApiService.class);
        Call<FileListResponse> call = apiService.getFileList(currentPage, PER_PAGE);
        
        call.enqueue(new Callback<FileListResponse>() {
            @Override
            public void onResponse(Call<FileListResponse> call, Response<FileListResponse> response) {
                isLoading = false;
                showLoading(false);
                swipeRefreshLayout.setRefreshing(false);
                
                if (response.isSuccessful() && response.body() != null) {
                    FileListResponse fileListResponse = response.body();
                    if (fileListResponse.isSuccess()) {
                        List<FileInfo> newFiles = fileListResponse.getFiles();
                        Log.d(TAG, "API Response - Success. Files received: " + (newFiles != null ? newFiles.size() : 0));
                        
                        if (newFiles != null) {
                            if (currentPage == 1) {
                                allFiles.clear();
                                fileList.clear();
                                Log.d(TAG, "Cleared file lists for first page");
                            }
                            
                            // Add new files to our lists
                            allFiles.addAll(newFiles);
                            fileList.addAll(newFiles);
                            
                            // Apply filtering and sorting
                            filterAndSortFiles();
                            
                            // Update adapter
                            if (fileListAdapter != null) {
                                Log.d(TAG, "Updating adapter with " + fileList.size() + " files");
                                if (currentPage == 1) {
                                    fileListAdapter.updateList(new ArrayList<>(fileList));
                                    Log.d(TAG, "Adapter updated with new list");
                                } else {
                                    int startPosition = fileList.size() - newFiles.size();
                                    fileListAdapter.notifyItemRangeInserted(startPosition, newFiles.size());
                                    Log.d(TAG, "Adapter items inserted from position " + startPosition + ", count: " + newFiles.size());
                                }
                            }
                            
                            // Update pagination state
                            isLastPage = newFiles.size() < PER_PAGE;
                            
                            // Show/hide empty state
                            showEmptyState(fileList.isEmpty());
                            
                            // Hide error state if shown
                            showErrorState(false, "");
                        }
                    } else {
                        String errorMsg = fileListResponse.getMessage() != null ? 
                                fileListResponse.getMessage() : "Failed to load files";
                        showErrorState(true, errorMsg);
                        Log.e(TAG, errorMsg);
                    }
                } else {
                    String errorMsg = "Failed to load files. Status code: " + (response != null ? response.code() : 0);
                    showErrorState(true, errorMsg);
                    Log.e(TAG, errorMsg);
                }
            }

            @Override
            public void onFailure(Call<FileListResponse> call, Throwable t) {
                isLoading = false;
                showLoading(false);
                swipeRefreshLayout.setRefreshing(false);
                String errorMsg = "Network error: " + (t != null ? t.getMessage() : "Unknown error");
                showErrorState(true, errorMsg);
                Log.e(TAG, errorMsg, t);
            }
        });
    }

    @Override
    public void onRefresh() {
        refreshFileList();
        swipeRefreshLayout.setRefreshing(false);
    }
    
    private void showLoading(boolean show) {
        runOnUiThread(() -> {
            if (fileList == null || fileList.isEmpty()) {
                if (loadingProgressBar != null) {
                    loadingProgressBar.setVisibility(show ? View.VISIBLE : View.GONE);
                }
                if (show) {
                    if (recyclerView != null) {
                        recyclerView.setVisibility(View.GONE);
                    }
                    if (emptyStateView != null) {
                        emptyStateView.setVisibility(View.GONE);
                    }
                    if (errorStateView != null) {
                        errorStateView.setVisibility(View.GONE);
                    }
                }
            } else {
                if (loadingProgressBar != null) {
                    loadingProgressBar.setVisibility(View.GONE);
                }
                if (recyclerView != null) {
                    recyclerView.setVisibility(View.VISIBLE);
                }
            }
        });
    }
    
    private void showEmptyState(boolean show) {
        runOnUiThread(() -> {
            if (emptyStateView != null) {
                emptyStateView.setVisibility(show ? View.VISIBLE : View.GONE);
            }
            if (recyclerView != null) {
                recyclerView.setVisibility(show ? View.GONE : View.VISIBLE);
            }
            if (loadingProgressBar != null) {
                loadingProgressBar.setVisibility(View.GONE);
            }
            if (errorStateView != null) {
                errorStateView.setVisibility(View.GONE);
            }
        });
    }
    
    private void showErrorState(boolean show, String errorMessage) {
        runOnUiThread(() -> {
            if (show) {
                if (errorMessageTextView != null) {
                    errorMessageTextView.setText(errorMessage);
                }
                if (errorStateView != null) {
                    errorStateView.setVisibility(View.VISIBLE);
                }
                if (recyclerView != null) {
                    recyclerView.setVisibility(View.GONE);
                }
                if (emptyStateView != null) {
                    emptyStateView.setVisibility(View.GONE);
                }
                if (loadingProgressBar != null) {
                    loadingProgressBar.setVisibility(View.GONE);
                }
            } else {
                if (errorStateView != null) {
                    errorStateView.setVisibility(View.GONE);
                }
                if (recyclerView != null) {
                    recyclerView.setVisibility(View.VISIBLE);
                }
            }
        });
    }









}
