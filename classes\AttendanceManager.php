<?php
/**
 * Attendance Manager Class
 * 
 * Handles all attendance-related operations including CRUD operations,
 * analytics, pattern detection, and alert generation.
 * 
 * @package MtcInvoice Attendance
 * @version 1.0
 */

require_once __DIR__ . '/../api/config/config.php';

class AttendanceManager {
    private $pdo;
    private $alertSystem;
    
    public function __construct() {
        $database = new Database();
        $this->pdo = $database->getConnection();
        
        if (!$this->pdo) {
            throw new Exception('Database connection failed');
        }
    }
    
    /**
     * Set alert system dependency
     */
    public function setAlertSystem($alertSystem) {
        $this->alertSystem = $alertSystem;
    }
    
    /**
     * Record attendance for a worker
     */
    public function recordAttendance($data) {
        try {
            // Validate required fields
            $required = ['worker_id', 'attendance_date', 'status'];
            foreach ($required as $field) {
                if (!isset($data[$field]) || empty($data[$field])) {
                    throw new Exception("Missing required field: $field");
                }
            }
            
            // Sanitize and validate data
            $data = $this->sanitizeAttendanceData($data);
            
            // Check if attendance already exists for this date
            $existing = $this->getAttendanceByWorkerAndDate($data['worker_id'], $data['attendance_date']);
            
            if ($existing) {
                return $this->updateAttendance($existing['id'], $data);
            }
            
            // Calculate overtime and break duration
            $data = $this->calculateTimeMetrics($data);
            
            // Insert new attendance record
            $stmt = $this->pdo->prepare("
                INSERT INTO attendance_records (
                    worker_id, attendance_date, status, check_in_time, check_out_time,
                    break_duration_minutes, overtime_minutes, emoji_tags, notes,
                    location_checkin, ip_address, weather_condition, productivity_score,
                    created_by
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            $stmt->execute([
                $data['worker_id'],
                $data['attendance_date'],
                $data['status'],
                $data['check_in_time'] ?? null,
                $data['check_out_time'] ?? null,
                $data['break_duration_minutes'] ?? 0,
                $data['overtime_minutes'] ?? 0,
                isset($data['emoji_tags']) ? json_encode($data['emoji_tags']) : null,
                $data['notes'] ?? null,
                $data['location_checkin'] ?? null,
                $data['ip_address'] ?? $_SERVER['REMOTE_ADDR'] ?? null,
                $data['weather_condition'] ?? null,
                $data['productivity_score'] ?? null,
                $data['created_by'] ?? 1
            ]);
            
            $attendanceId = $this->pdo->lastInsertId();
            
            // Trigger pattern analysis and alerts
            $this->analyzeAttendancePatterns($data['worker_id']);
            
            return $this->getAttendanceById($attendanceId);
            
        } catch (Exception $e) {
            error_log("AttendanceManager::recordAttendance error: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * Update existing attendance record
     */
    public function updateAttendance($id, $data) {
        try {
            // Sanitize data
            $data = $this->sanitizeAttendanceData($data);
            
            // Calculate time metrics
            $data = $this->calculateTimeMetrics($data);
            
            // Build update query
            $updates = [];
            $values = [];
            
            $allowedFields = [
                'status', 'check_in_time', 'check_out_time', 'break_duration_minutes',
                'overtime_minutes', 'emoji_tags', 'notes', 'location_checkin',
                'weather_condition', 'productivity_score'
            ];
            
            foreach ($allowedFields as $field) {
                if (isset($data[$field])) {
                    $updates[] = "$field = ?";
                    if ($field === 'emoji_tags' && is_array($data[$field])) {
                        $values[] = json_encode($data[$field]);
                    } else {
                        $values[] = $data[$field];
                    }
                }
            }
            
            if (empty($updates)) {
                throw new Exception('No valid fields provided for update');
            }
            
            $values[] = $id;
            
            $sql = "UPDATE attendance_records SET " . implode(', ', $updates) . " WHERE id = ?";
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($values);
            
            // Get worker_id for pattern analysis
            $attendance = $this->getAttendanceById($id);
            if ($attendance) {
                $this->analyzeAttendancePatterns($attendance['worker_id']);
            }
            
            return $this->getAttendanceById($id);
            
        } catch (Exception $e) {
            error_log("AttendanceManager::updateAttendance error: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * Get attendance record by ID
     */
    public function getAttendanceById($id) {
        try {
            $stmt = $this->pdo->prepare("
                SELECT ar.*, w.employee_id, w.name as worker_name, w.department
                FROM attendance_records ar
                JOIN workers w ON ar.worker_id = w.id
                WHERE ar.id = ?
            ");
            $stmt->execute([$id]);
            $record = $stmt->fetch();
            
            if ($record && $record['emoji_tags']) {
                $record['emoji_tags'] = json_decode($record['emoji_tags'], true);
            }
            
            return $record;
            
        } catch (Exception $e) {
            error_log("AttendanceManager::getAttendanceById error: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Get attendance records with filters
     */
    public function getAttendanceRecords($filters = []) {
        try {
            $where = ['1=1'];
            $params = [];
            
            // Apply filters
            if (!empty($filters['worker_id'])) {
                $where[] = 'ar.worker_id = ?';
                $params[] = $filters['worker_id'];
            }
            
            if (!empty($filters['department'])) {
                $where[] = 'w.department = ?';
                $params[] = $filters['department'];
            }
            
            if (!empty($filters['status'])) {
                $where[] = 'ar.status = ?';
                $params[] = $filters['status'];
            }
            
            if (!empty($filters['start_date'])) {
                $where[] = 'ar.attendance_date >= ?';
                $params[] = $filters['start_date'];
            }
            
            if (!empty($filters['end_date'])) {
                $where[] = 'ar.attendance_date <= ?';
                $params[] = $filters['end_date'];
            }
            
            // Pagination
            $page = isset($filters['page']) ? max(1, (int)$filters['page']) : 1;
            $limit = isset($filters['limit']) ? min(100, max(1, (int)$filters['limit'])) : 50;
            $offset = ($page - 1) * $limit;
            
            // Count total records
            $countSql = "
                SELECT COUNT(*) as total 
                FROM attendance_records ar
                JOIN workers w ON ar.worker_id = w.id
                WHERE " . implode(' AND ', $where);
            $countStmt = $this->pdo->prepare($countSql);
            $countStmt->execute($params);
            $total = $countStmt->fetch()['total'];
            
            // Get records
            $sql = "
                SELECT ar.*, w.employee_id, w.name as worker_name, w.department, w.position
                FROM attendance_records ar
                JOIN workers w ON ar.worker_id = w.id
                WHERE " . implode(' AND ', $where) . "
                ORDER BY ar.attendance_date DESC, w.name ASC
                LIMIT ? OFFSET ?
            ";
            
            $params[] = $limit;
            $params[] = $offset;
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);
            $records = $stmt->fetchAll();
            
            // Decode emoji tags
            foreach ($records as &$record) {
                if ($record['emoji_tags']) {
                    $record['emoji_tags'] = json_decode($record['emoji_tags'], true);
                }
            }
            
            return [
                'records' => $records,
                'pagination' => [
                    'page' => $page,
                    'limit' => $limit,
                    'total' => $total,
                    'pages' => ceil($total / $limit)
                ]
            ];
            
        } catch (Exception $e) {
            error_log("AttendanceManager::getAttendanceRecords error: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * Get attendance by worker and date
     */
    public function getAttendanceByWorkerAndDate($workerId, $date) {
        try {
            $stmt = $this->pdo->prepare("
                SELECT * FROM attendance_records 
                WHERE worker_id = ? AND attendance_date = ?
            ");
            $stmt->execute([$workerId, $date]);
            $record = $stmt->fetch();
            
            if ($record && $record['emoji_tags']) {
                $record['emoji_tags'] = json_decode($record['emoji_tags'], true);
            }
            
            return $record;
            
        } catch (Exception $e) {
            error_log("AttendanceManager::getAttendanceByWorkerAndDate error: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Sanitize attendance data
     */
    private function sanitizeAttendanceData($data) {
        $sanitized = [];
        
        foreach ($data as $key => $value) {
            if (is_string($value)) {
                $sanitized[$key] = htmlspecialchars(strip_tags(trim($value)), ENT_QUOTES, 'UTF-8');
            } elseif (is_array($value)) {
                $sanitized[$key] = $value; // Keep arrays as-is for emoji_tags
            } else {
                $sanitized[$key] = $value;
            }
        }
        
        return $sanitized;
    }
    
    /**
     * Calculate time metrics (overtime, break duration, etc.)
     */
    private function calculateTimeMetrics($data) {
        if (isset($data['check_in_time']) && isset($data['check_out_time'])) {
            $checkIn = new DateTime($data['check_in_time']);
            $checkOut = new DateTime($data['check_out_time']);
            
            // Calculate total minutes worked
            $totalMinutes = ($checkOut->getTimestamp() - $checkIn->getTimestamp()) / 60;
            
            // Subtract break duration
            $breakMinutes = $data['break_duration_minutes'] ?? 60; // Default 1 hour break
            $workedMinutes = $totalMinutes - $breakMinutes;
            
            // Calculate overtime (assuming 8 hours = 480 minutes standard)
            $standardMinutes = 480;
            $overtimeMinutes = max(0, $workedMinutes - $standardMinutes);
            
            $data['overtime_minutes'] = $overtimeMinutes;
            $data['break_duration_minutes'] = $breakMinutes;
        }
        
        return $data;
    }
    
    /**
     * Analyze attendance patterns for alerts
     */
    private function analyzeAttendancePatterns($workerId) {
        // This will be implemented with the AlertSystem
        if ($this->alertSystem) {
            $this->alertSystem->analyzeWorkerPatterns($workerId);
        }
    }
}
