<?php
/**
 * Analytics Engine Class
 * 
 * Handles attendance analytics, pattern recognition, and reporting.
 * Generates insights for attendance trends, productivity metrics, and alerts.
 * 
 * @package MtcInvoice Attendance
 * @version 1.0
 */

require_once __DIR__ . '/../api/config/config.php';

class AnalyticsEngine {
    private $pdo;
    
    public function __construct() {
        $database = new Database();
        $this->pdo = $database->getConnection();
        
        if (!$this->pdo) {
            throw new Exception('Database connection failed');
        }
    }
    
    /**
     * Get attendance overview statistics
     */
    public function getAttendanceOverview($filters = []) {
        try {
            $where = ['1=1'];
            $params = [];
            
            // Apply date filters
            if (!empty($filters['start_date'])) {
                $where[] = 'ar.attendance_date >= ?';
                $params[] = $filters['start_date'];
            } else {
                $where[] = 'ar.attendance_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)';
            }
            
            if (!empty($filters['end_date'])) {
                $where[] = 'ar.attendance_date <= ?';
                $params[] = $filters['end_date'];
            }
            
            if (!empty($filters['department'])) {
                $where[] = 'w.department = ?';
                $params[] = $filters['department'];
            }
            
            $stmt = $this->pdo->prepare("
                SELECT 
                    COUNT(*) as total_records,
                    COUNT(CASE WHEN ar.status = 'present' THEN 1 END) as present_count,
                    COUNT(CASE WHEN ar.status = 'absent' THEN 1 END) as absent_count,
                    COUNT(CASE WHEN ar.status = 'late' THEN 1 END) as late_count,
                    COUNT(CASE WHEN ar.status = 'half_day' THEN 1 END) as half_day_count,
                    COUNT(CASE WHEN ar.status = 'sick_leave' THEN 1 END) as sick_leave_count,
                    COUNT(CASE WHEN ar.status = 'vacation' THEN 1 END) as vacation_count,
                    COUNT(CASE WHEN ar.status = 'remote_work' THEN 1 END) as remote_work_count,
                    ROUND(AVG(ar.productivity_score), 2) as avg_productivity_score,
                    SUM(ar.overtime_minutes) as total_overtime_minutes,
                    COUNT(DISTINCT ar.worker_id) as unique_workers,
                    ROUND((COUNT(CASE WHEN ar.status IN ('present', 'late', 'half_day') THEN 1 END) / COUNT(*)) * 100, 2) as attendance_rate
                FROM attendance_records ar
                JOIN workers w ON ar.worker_id = w.id
                WHERE " . implode(' AND ', $where)
            );
            $stmt->execute($params);
            
            return $stmt->fetch();
            
        } catch (Exception $e) {
            error_log("AnalyticsEngine::getAttendanceOverview error: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * Get attendance trends by day/week/month
     */
    public function getAttendanceTrends($period = 'daily', $filters = []) {
        try {
            $where = ['1=1'];
            $params = [];
            
            // Date format based on period
            $dateFormat = match($period) {
                'daily' => '%Y-%m-%d',
                'weekly' => '%Y-%u',
                'monthly' => '%Y-%m',
                default => '%Y-%m-%d'
            };
            
            // Apply filters
            if (!empty($filters['start_date'])) {
                $where[] = 'ar.attendance_date >= ?';
                $params[] = $filters['start_date'];
            } else {
                $interval = match($period) {
                    'daily' => '30 DAY',
                    'weekly' => '12 WEEK',
                    'monthly' => '12 MONTH',
                    default => '30 DAY'
                };
                $where[] = "ar.attendance_date >= DATE_SUB(CURDATE(), INTERVAL $interval)";
            }
            
            if (!empty($filters['end_date'])) {
                $where[] = 'ar.attendance_date <= ?';
                $params[] = $filters['end_date'];
            }
            
            if (!empty($filters['department'])) {
                $where[] = 'w.department = ?';
                $params[] = $filters['department'];
            }
            
            $stmt = $this->pdo->prepare("
                SELECT 
                    DATE_FORMAT(ar.attendance_date, '$dateFormat') as period,
                    COUNT(*) as total_records,
                    COUNT(CASE WHEN ar.status = 'present' THEN 1 END) as present_count,
                    COUNT(CASE WHEN ar.status = 'absent' THEN 1 END) as absent_count,
                    COUNT(CASE WHEN ar.status = 'late' THEN 1 END) as late_count,
                    COUNT(CASE WHEN ar.status = 'sick_leave' THEN 1 END) as sick_leave_count,
                    COUNT(CASE WHEN ar.status = 'vacation' THEN 1 END) as vacation_count,
                    ROUND(AVG(ar.productivity_score), 2) as avg_productivity_score,
                    SUM(ar.overtime_minutes) as total_overtime_minutes,
                    ROUND((COUNT(CASE WHEN ar.status IN ('present', 'late', 'half_day') THEN 1 END) / COUNT(*)) * 100, 2) as attendance_rate
                FROM attendance_records ar
                JOIN workers w ON ar.worker_id = w.id
                WHERE " . implode(' AND ', $where) . "
                GROUP BY DATE_FORMAT(ar.attendance_date, '$dateFormat')
                ORDER BY period ASC
            ");
            $stmt->execute($params);
            
            return $stmt->fetchAll();
            
        } catch (Exception $e) {
            error_log("AnalyticsEngine::getAttendanceTrends error: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * Get worker performance analytics
     */
    public function getWorkerPerformance($workerId = null, $filters = []) {
        try {
            $where = ['1=1'];
            $params = [];
            
            if ($workerId) {
                $where[] = 'ar.worker_id = ?';
                $params[] = $workerId;
            }
            
            // Apply date filters
            if (!empty($filters['start_date'])) {
                $where[] = 'ar.attendance_date >= ?';
                $params[] = $filters['start_date'];
            } else {
                $where[] = 'ar.attendance_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)';
            }
            
            if (!empty($filters['end_date'])) {
                $where[] = 'ar.attendance_date <= ?';
                $params[] = $filters['end_date'];
            }
            
            $stmt = $this->pdo->prepare("
                SELECT 
                    w.id,
                    w.employee_id,
                    w.name,
                    w.department,
                    w.position,
                    COUNT(*) as total_days,
                    COUNT(CASE WHEN ar.status = 'present' THEN 1 END) as present_days,
                    COUNT(CASE WHEN ar.status = 'late' THEN 1 END) as late_days,
                    COUNT(CASE WHEN ar.status = 'absent' THEN 1 END) as absent_days,
                    COUNT(CASE WHEN ar.status = 'sick_leave' THEN 1 END) as sick_days,
                    COUNT(CASE WHEN ar.status = 'vacation' THEN 1 END) as vacation_days,
                    ROUND(AVG(ar.productivity_score), 2) as avg_productivity_score,
                    SUM(ar.overtime_minutes) as total_overtime_minutes,
                    ROUND((COUNT(CASE WHEN ar.status IN ('present', 'late', 'half_day') THEN 1 END) / COUNT(*)) * 100, 2) as attendance_rate,
                    ROUND((COUNT(CASE WHEN ar.status = 'present' THEN 1 END) / COUNT(*)) * 100, 2) as punctuality_rate
                FROM attendance_records ar
                JOIN workers w ON ar.worker_id = w.id
                WHERE " . implode(' AND ', $where) . "
                GROUP BY w.id, w.employee_id, w.name, w.department, w.position
                ORDER BY attendance_rate DESC, punctuality_rate DESC
            ");
            $stmt->execute($params);
            
            return $stmt->fetchAll();
            
        } catch (Exception $e) {
            error_log("AnalyticsEngine::getWorkerPerformance error: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * Get department analytics
     */
    public function getDepartmentAnalytics($filters = []) {
        try {
            $where = ['1=1'];
            $params = [];
            
            // Apply date filters
            if (!empty($filters['start_date'])) {
                $where[] = 'ar.attendance_date >= ?';
                $params[] = $filters['start_date'];
            } else {
                $where[] = 'ar.attendance_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)';
            }
            
            if (!empty($filters['end_date'])) {
                $where[] = 'ar.attendance_date <= ?';
                $params[] = $filters['end_date'];
            }
            
            $stmt = $this->pdo->prepare("
                SELECT 
                    w.department,
                    COUNT(DISTINCT w.id) as total_workers,
                    COUNT(*) as total_records,
                    COUNT(CASE WHEN ar.status = 'present' THEN 1 END) as present_count,
                    COUNT(CASE WHEN ar.status = 'late' THEN 1 END) as late_count,
                    COUNT(CASE WHEN ar.status = 'absent' THEN 1 END) as absent_count,
                    ROUND(AVG(ar.productivity_score), 2) as avg_productivity_score,
                    SUM(ar.overtime_minutes) as total_overtime_minutes,
                    ROUND((COUNT(CASE WHEN ar.status IN ('present', 'late', 'half_day') THEN 1 END) / COUNT(*)) * 100, 2) as attendance_rate,
                    ROUND((COUNT(CASE WHEN ar.status = 'present' THEN 1 END) / COUNT(*)) * 100, 2) as punctuality_rate
                FROM attendance_records ar
                JOIN workers w ON ar.worker_id = w.id
                WHERE " . implode(' AND ', $where) . "
                GROUP BY w.department
                ORDER BY attendance_rate DESC
            ");
            $stmt->execute($params);
            
            return $stmt->fetchAll();
            
        } catch (Exception $e) {
            error_log("AnalyticsEngine::getDepartmentAnalytics error: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * Get attendance heatmap data
     */
    public function getAttendanceHeatmap($workerId = null, $filters = []) {
        try {
            $where = ['1=1'];
            $params = [];
            
            if ($workerId) {
                $where[] = 'ar.worker_id = ?';
                $params[] = $workerId;
            }
            
            // Apply date filters (default to last 3 months)
            if (!empty($filters['start_date'])) {
                $where[] = 'ar.attendance_date >= ?';
                $params[] = $filters['start_date'];
            } else {
                $where[] = 'ar.attendance_date >= DATE_SUB(CURDATE(), INTERVAL 3 MONTH)';
            }
            
            if (!empty($filters['end_date'])) {
                $where[] = 'ar.attendance_date <= ?';
                $params[] = $filters['end_date'];
            }
            
            $stmt = $this->pdo->prepare("
                SELECT 
                    ar.attendance_date,
                    DAYOFWEEK(ar.attendance_date) as day_of_week,
                    WEEK(ar.attendance_date) as week_number,
                    ar.status,
                    COUNT(*) as count,
                    AVG(ar.productivity_score) as avg_productivity,
                    SUM(ar.overtime_minutes) as total_overtime
                FROM attendance_records ar
                JOIN workers w ON ar.worker_id = w.id
                WHERE " . implode(' AND ', $where) . "
                GROUP BY ar.attendance_date, ar.status
                ORDER BY ar.attendance_date ASC
            ");
            $stmt->execute($params);
            
            return $stmt->fetchAll();
            
        } catch (Exception $e) {
            error_log("AnalyticsEngine::getAttendanceHeatmap error: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * Get emoji usage analytics
     */
    public function getEmojiAnalytics($filters = []) {
        try {
            $where = ['ar.emoji_tags IS NOT NULL'];
            $params = [];
            
            // Apply date filters
            if (!empty($filters['start_date'])) {
                $where[] = 'ar.attendance_date >= ?';
                $params[] = $filters['start_date'];
            } else {
                $where[] = 'ar.attendance_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)';
            }
            
            if (!empty($filters['end_date'])) {
                $where[] = 'ar.attendance_date <= ?';
                $params[] = $filters['end_date'];
            }
            
            if (!empty($filters['department'])) {
                $where[] = 'w.department = ?';
                $params[] = $filters['department'];
            }
            
            $stmt = $this->pdo->prepare("
                SELECT 
                    ar.emoji_tags,
                    ar.status,
                    COUNT(*) as usage_count,
                    AVG(ar.productivity_score) as avg_productivity
                FROM attendance_records ar
                JOIN workers w ON ar.worker_id = w.id
                WHERE " . implode(' AND ', $where) . "
                GROUP BY ar.emoji_tags, ar.status
                ORDER BY usage_count DESC
            ");
            $stmt->execute($params);
            
            $results = $stmt->fetchAll();
            
            // Process emoji data
            $emojiStats = [];
            foreach ($results as $row) {
                $emojis = json_decode($row['emoji_tags'], true);
                if (is_array($emojis)) {
                    foreach ($emojis as $emoji) {
                        if (!isset($emojiStats[$emoji])) {
                            $emojiStats[$emoji] = [
                                'emoji' => $emoji,
                                'total_usage' => 0,
                                'by_status' => [],
                                'avg_productivity' => 0,
                                'productivity_sum' => 0,
                                'productivity_count' => 0
                            ];
                        }
                        
                        $emojiStats[$emoji]['total_usage'] += $row['usage_count'];
                        $emojiStats[$emoji]['by_status'][$row['status']] = 
                            ($emojiStats[$emoji]['by_status'][$row['status']] ?? 0) + $row['usage_count'];
                        
                        if ($row['avg_productivity']) {
                            $emojiStats[$emoji]['productivity_sum'] += $row['avg_productivity'] * $row['usage_count'];
                            $emojiStats[$emoji]['productivity_count'] += $row['usage_count'];
                        }
                    }
                }
            }
            
            // Calculate average productivity for each emoji
            foreach ($emojiStats as &$stat) {
                if ($stat['productivity_count'] > 0) {
                    $stat['avg_productivity'] = round($stat['productivity_sum'] / $stat['productivity_count'], 2);
                }
                unset($stat['productivity_sum'], $stat['productivity_count']);
            }
            
            // Sort by usage
            uasort($emojiStats, function($a, $b) {
                return $b['total_usage'] - $a['total_usage'];
            });
            
            return array_values($emojiStats);
            
        } catch (Exception $e) {
            error_log("AnalyticsEngine::getEmojiAnalytics error: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * Get productivity insights
     */
    public function getProductivityInsights($filters = []) {
        try {
            $where = ['ar.productivity_score IS NOT NULL'];
            $params = [];
            
            // Apply filters
            if (!empty($filters['start_date'])) {
                $where[] = 'ar.attendance_date >= ?';
                $params[] = $filters['start_date'];
            } else {
                $where[] = 'ar.attendance_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)';
            }
            
            if (!empty($filters['end_date'])) {
                $where[] = 'ar.attendance_date <= ?';
                $params[] = $filters['end_date'];
            }
            
            if (!empty($filters['department'])) {
                $where[] = 'w.department = ?';
                $params[] = $filters['department'];
            }
            
            $stmt = $this->pdo->prepare("
                SELECT 
                    DAYOFWEEK(ar.attendance_date) as day_of_week,
                    DAYNAME(ar.attendance_date) as day_name,
                    AVG(ar.productivity_score) as avg_productivity,
                    COUNT(*) as record_count,
                    MIN(ar.productivity_score) as min_productivity,
                    MAX(ar.productivity_score) as max_productivity
                FROM attendance_records ar
                JOIN workers w ON ar.worker_id = w.id
                WHERE " . implode(' AND ', $where) . "
                GROUP BY DAYOFWEEK(ar.attendance_date), DAYNAME(ar.attendance_date)
                ORDER BY day_of_week
            ");
            $stmt->execute($params);
            
            return $stmt->fetchAll();
            
        } catch (Exception $e) {
            error_log("AnalyticsEngine::getProductivityInsights error: " . $e->getMessage());
            throw $e;
        }
    }
}
