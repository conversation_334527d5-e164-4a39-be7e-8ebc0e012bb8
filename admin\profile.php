<?php
/**
 * Admin Profile Page
 * 
 * @package MtcInvoice Admin
 * @version 1.0
 */

require_once 'config/config.php';

// Require login
requireLogin();

$current_page = 'profile';
$page_title = 'Profile';

$current_user = getCurrentUser();
$success_message = '';
$error_message = '';

// Handle profile update
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'update_profile') {
        $name = sanitize($_POST['name'] ?? '');
        $email = sanitize($_POST['email'] ?? '');
        
        if (empty($name) || empty($email)) {
            $error_message = 'Name and email are required.';
        } else {
            try {
                $pdo = getDbConnection();
                
                // Check if email already exists (excluding current user)
                $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ? AND id != ?");
                $stmt->execute([$email, $current_user['id']]);
                
                if ($stmt->fetch()) {
                    $error_message = 'Email already exists.';
                } else {
                    // Update user profile
                    $stmt = $pdo->prepare("UPDATE users SET name = ?, email = ? WHERE id = ?");
                    $stmt->execute([$name, $email, $current_user['id']]);
                    
                    // Update session data
                    $_SESSION['admin_user']['name'] = $name;
                    $_SESSION['admin_user']['email'] = $email;
                    
                    $success_message = 'Profile updated successfully.';
                    $current_user = getCurrentUser(); // Refresh user data
                }
            } catch (Exception $e) {
                $error_message = 'Failed to update profile. Please try again.';
                error_log("Profile update error: " . $e->getMessage());
            }
        }
    }
    
    if ($action === 'change_password') {
        $current_password = $_POST['current_password'] ?? '';
        $new_password = $_POST['new_password'] ?? '';
        $confirm_password = $_POST['confirm_password'] ?? '';
        
        if (empty($current_password) || empty($new_password) || empty($confirm_password)) {
            $error_message = 'All password fields are required.';
        } elseif ($new_password !== $confirm_password) {
            $error_message = 'New passwords do not match.';
        } elseif (strlen($new_password) < 6) {
            $error_message = 'New password must be at least 6 characters long.';
        } else {
            try {
                $pdo = getDbConnection();
                
                // Verify current password
                $stmt = $pdo->prepare("SELECT password FROM users WHERE id = ?");
                $stmt->execute([$current_user['id']]);
                $user_data = $stmt->fetch();
                
                if (!password_verify($current_password, $user_data['password'])) {
                    $error_message = 'Current password is incorrect.';
                } else {
                    // Update password
                    $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
                    $stmt = $pdo->prepare("UPDATE users SET password = ? WHERE id = ?");
                    $stmt->execute([$hashed_password, $current_user['id']]);
                    
                    $success_message = 'Password changed successfully.';
                }
            } catch (Exception $e) {
                $error_message = 'Failed to change password. Please try again.';
                error_log("Password change error: " . $e->getMessage());
            }
        }
    }
}

include 'includes/header.php';
?>

<!-- Profile Content -->
<div class="row mb-4">
    <div class="col-12">
        <h1 class="h3 mb-0">Profile</h1>
        <p class="text-muted">Manage your account settings</p>
    </div>
</div>

<?php if ($success_message): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <?= htmlspecialchars($success_message) ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if ($error_message): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <?= htmlspecialchars($error_message) ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<div class="row">
    <!-- Profile Information -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-person me-2"></i>
                    Profile Information
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <input type="hidden" name="action" value="update_profile">
                    
                    <div class="mb-3">
                        <label for="name" class="form-label">Full Name</label>
                        <input type="text" class="form-control" id="name" name="name" 
                               value="<?= htmlspecialchars($current_user['name']) ?>" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="email" class="form-label">Email Address</label>
                        <input type="email" class="form-control" id="email" name="email" 
                               value="<?= htmlspecialchars($current_user['email']) ?>" required>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Role</label>
                        <input type="text" class="form-control" 
                               value="<?= ucfirst($current_user['role']) ?>" readonly>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-check-circle me-2"></i>
                        Update Profile
                    </button>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Change Password -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-shield-lock me-2"></i>
                    Change Password
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <input type="hidden" name="action" value="change_password">
                    
                    <div class="mb-3">
                        <label for="current_password" class="form-label">Current Password</label>
                        <input type="password" class="form-control" id="current_password" 
                               name="current_password" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="new_password" class="form-label">New Password</label>
                        <input type="password" class="form-control" id="new_password" 
                               name="new_password" minlength="6" required>
                        <div class="form-text">Password must be at least 6 characters long.</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="confirm_password" class="form-label">Confirm New Password</label>
                        <input type="password" class="form-control" id="confirm_password" 
                               name="confirm_password" minlength="6" required>
                    </div>
                    
                    <button type="submit" class="btn btn-warning">
                        <i class="bi bi-key me-2"></i>
                        Change Password
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Account Information -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-info-circle me-2"></i>
                    Account Information
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>User ID:</strong> <?= $current_user['id'] ?></p>
                        <p><strong>Role:</strong> <?= ucfirst($current_user['role']) ?></p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>Account Created:</strong> 
                            <?php
                            try {
                                $pdo = getDbConnection();
                                $stmt = $pdo->prepare("SELECT created_at FROM users WHERE id = ?");
                                $stmt->execute([$current_user['id']]);
                                $user_info = $stmt->fetch();
                                echo $user_info ? date('M j, Y g:i A', strtotime($user_info['created_at'])) : 'Unknown';
                            } catch (Exception $e) {
                                echo 'Unknown';
                            }
                            ?>
                        </p>
                        <p><strong>Last Login:</strong> 
                            <?php
                            try {
                                $stmt = $pdo->prepare("SELECT last_login FROM users WHERE id = ?");
                                $stmt->execute([$current_user['id']]);
                                $user_info = $stmt->fetch();
                                echo $user_info && $user_info['last_login'] ? 
                                     date('M j, Y g:i A', strtotime($user_info['last_login'])) : 'Never';
                            } catch (Exception $e) {
                                echo 'Unknown';
                            }
                            ?>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
$extra_js = "
<script>
// Password confirmation validation
document.getElementById('confirm_password').addEventListener('input', function() {
    const newPassword = document.getElementById('new_password').value;
    const confirmPassword = this.value;
    
    if (newPassword !== confirmPassword) {
        this.setCustomValidity('Passwords do not match');
    } else {
        this.setCustomValidity('');
    }
});

document.getElementById('new_password').addEventListener('input', function() {
    const confirmPassword = document.getElementById('confirm_password');
    if (confirmPassword.value && this.value !== confirmPassword.value) {
        confirmPassword.setCustomValidity('Passwords do not match');
    } else {
        confirmPassword.setCustomValidity('');
    }
});
</script>
";

include 'includes/footer.php';
?>
