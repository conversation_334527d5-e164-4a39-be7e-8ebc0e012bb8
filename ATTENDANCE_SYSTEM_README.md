# MtcInvoice Attendance Management System

A comprehensive, modern attendance management system with advanced analytics, pattern recognition, and interactive visualizations.

## 🚀 Features

### Core Functionality
- **Worker Management**: Complete CRUD operations for employee records
- **Attendance Tracking**: Real-time attendance recording with multiple status types
- **Interactive Calendar**: Drag-and-drop timeline calendar with emoji status indicators
- **Smart Alerts**: Automated pattern detection and intelligent alerting system
- **Advanced Analytics**: Interactive charts, heatmaps, and trend analysis
- **Comprehensive Reporting**: Automated report generation with multiple export formats

### Advanced Features
- **Pattern Recognition**: AI-powered analysis of attendance patterns and anomalies
- **Productivity Tracking**: Emoji-based mood and productivity scoring
- **Department Analytics**: Comparative performance analysis across departments
- **Predictive Insights**: Trend forecasting and recommendation engine
- **Real-time Dashboard**: Live updates with customizable widgets
- **Mobile Responsive**: Fully responsive design for all devices

## 📋 System Requirements

### Server Requirements
- **PHP**: 7.4 or higher
- **MySQL**: 5.7 or higher (or MariaDB 10.2+)
- **Web Server**: Apache 2.4+ or Nginx 1.16+
- **Memory**: Minimum 512MB RAM (2GB recommended)
- **Storage**: Minimum 1GB free space

### Client Requirements
- **Modern Web Browser**: Chrome 80+, Firefox 75+, Safari 13+, Edge 80+
- **JavaScript**: Enabled
- **Screen Resolution**: Minimum 1024x768 (responsive design)

## 🛠️ Installation

### 1. Database Setup

```sql
-- Create database
CREATE DATABASE mtc_attendance CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Import the database schema
mysql -u username -p mtc_attendance < database/attendance_schema.sql
```

### 2. Configuration

1. Copy the configuration template:
```bash
cp api/config/config.example.php api/config/config.php
```

2. Update database credentials in `api/config/config.php`:
```php
define('DB_HOST', 'localhost');
define('DB_NAME', 'mtc_attendance');
define('DB_USER', 'your_username');
define('DB_PASS', 'your_password');
```

### 3. File Permissions

Set appropriate permissions for upload directories:
```bash
chmod 755 uploads/
chmod 755 uploads/profiles/
chmod 755 uploads/reports/
```

### 4. Web Server Configuration

#### Apache (.htaccess)
```apache
RewriteEngine On
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^api/(.*)$ api/$1 [QSA,L]
```

#### Nginx
```nginx
location /api/ {
    try_files $uri $uri/ /api/index.php?$query_string;
}
```

## 📁 Project Structure

```
MtcInvoiceNewProject/
├── admin/
│   ├── attendance/
│   │   ├── index.php              # Main dashboard
│   │   └── analytics.php          # Analytics page
│   └── includes/
│       ├── header.php
│       └── footer.php
├── api/
│   ├── config/
│   │   └── config.php             # Database configuration
│   └── attendance/
│       ├── workers.php            # Workers API
│       ├── records.php            # Attendance records API
│       ├── analytics.php          # Analytics API
│       ├── alerts.php             # Alerts API
│       └── reports.php            # Reports API
├── assets/
│   ├── css/attendance/
│   │   ├── calendar.css           # Calendar styles
│   │   ├── dashboard.css          # Dashboard styles
│   │   ├── admin.css              # Admin interface styles
│   │   └── analytics.css          # Analytics styles
│   └── js/attendance/
│       ├── calendar.js            # Interactive calendar
│       ├── dashboard.js           # Dashboard widgets
│       ├── admin.js               # Admin functionality
│       ├── analytics-charts.js    # Chart components
│       ├── analytics-heatmap.js   # Heatmap visualization
│       └── analytics-patterns.js  # Pattern recognition
├── classes/
│   ├── WorkerManager.php          # Worker management
│   ├── AttendanceManager.php      # Attendance operations
│   ├── AnalyticsEngine.php        # Analytics processing
│   └── AlertSystem.php            # Alert management
└── database/
    └── attendance_schema.sql      # Database schema
```

## 🔧 API Documentation

### Authentication
All API endpoints require proper authentication. Include the session token in requests:
```javascript
headers: {
    'Authorization': 'Bearer ' + sessionToken,
    'Content-Type': 'application/json'
}
```

### Workers API (`/api/attendance/workers.php`)

#### Get All Workers
```http
GET /api/attendance/workers.php
```

Query Parameters:
- `department` (optional): Filter by department
- `status` (optional): Filter by status (active, inactive)
- `search` (optional): Search by name or employee ID
- `page` (optional): Page number for pagination
- `limit` (optional): Items per page

#### Create Worker
```http
POST /api/attendance/workers.php
Content-Type: application/json

{
    "employee_id": "EMP001",
    "name": "John Doe",
    "email": "<EMAIL>",
    "department": "IT",
    "position": "Developer",
    "hire_date": "2024-01-15",
    "hourly_rate": 25.00
}
```

#### Update Worker
```http
PUT /api/attendance/workers.php/{id}
Content-Type: application/json

{
    "name": "John Smith",
    "department": "Engineering"
}
```

### Attendance Records API (`/api/attendance/records.php`)

#### Record Attendance
```http
POST /api/attendance/records.php
Content-Type: application/json

{
    "worker_id": 1,
    "attendance_date": "2024-01-15",
    "status": "present",
    "check_in_time": "09:00:00",
    "check_out_time": "17:30:00",
    "break_duration_minutes": 60,
    "emoji_tags": ["😊", "💻", "🎯"],
    "notes": "Productive day",
    "productivity_score": 4
}
```

#### Get Timeline Data
```http
GET /api/attendance/records.php/timeline?start_date=2024-01-01&end_date=2024-01-31
```

### Analytics API (`/api/attendance/analytics.php`)

#### Get Dashboard Data
```http
GET /api/attendance/analytics.php/dashboard?start_date=2024-01-01&end_date=2024-01-31
```

#### Get Attendance Trends
```http
GET /api/attendance/analytics.php/trends?period=daily&start_date=2024-01-01&end_date=2024-01-31
```

#### Get Heatmap Data
```http
GET /api/attendance/analytics.php/heatmap?worker_id=1&start_date=2024-01-01&end_date=2024-01-31
```

### Alerts API (`/api/attendance/alerts.php`)

#### Get Active Alerts
```http
GET /api/attendance/alerts.php?severity=high&worker_id=1
```

#### Resolve Alert
```http
PUT /api/attendance/alerts.php/{id}/resolve
Content-Type: application/json

{
    "resolved_by": 1,
    "notes": "Issue resolved through discussion"
}
```

#### Analyze Patterns
```http
POST /api/attendance/alerts.php/analyze
```

### Reports API (`/api/attendance/reports.php`)

#### Generate Report
```http
GET /api/attendance/reports.php/monthly?start_date=2024-01-01&end_date=2024-01-31&department=IT
```

#### Export Data
```http
GET /api/attendance/reports.php/export?format=csv&start_date=2024-01-01&end_date=2024-01-31
```

## 🎨 Frontend Components

### Interactive Calendar
```javascript
// Initialize attendance calendar
const calendar = new AttendanceCalendar('attendanceCalendar', {
    apiBaseUrl: '/api/attendance',
    enableDragDrop: true,
    enableEmojis: true,
    autoRefresh: true
});
```

### Dashboard Widgets
```javascript
// Initialize dashboard
const dashboard = new AttendanceDashboard('attendanceDashboard', {
    apiBaseUrl: '/api/attendance',
    autoRefresh: true,
    refreshInterval: 60000
});
```

### Analytics Charts
```javascript
// Initialize analytics
const analytics = new AttendanceAnalyticsCharts();
```

### Heatmap Visualization
```javascript
// Initialize heatmap
const heatmap = new AttendanceHeatmap('attendanceHeatmap', {
    cellSize: 12,
    monthsToShow: 3
});
```

## 🔍 Pattern Recognition

The system includes advanced pattern recognition capabilities:

### Automatic Detection
- **Consecutive Absences**: Alerts when workers are absent for consecutive days
- **Late Arrival Patterns**: Identifies frequent tardiness
- **Productivity Drops**: Detects significant decreases in productivity scores
- **Overtime Patterns**: Monitors excessive overtime usage
- **Seasonal Trends**: Analyzes attendance patterns across seasons

### Custom Analysis
```javascript
// Run pattern analysis
function runPatternAnalysis() {
    const analysis = new AttendancePatternAnalysis();
    analysis.runPatternAnalysis();
}
```

## 📊 Reporting Features

### Available Reports
1. **Daily Reports**: Daily attendance summary with status breakdown
2. **Weekly Reports**: Weekly trends and performance metrics
3. **Monthly Reports**: Comprehensive monthly analysis
4. **Department Reports**: Department-wise performance comparison
5. **Individual Reports**: Detailed worker-specific reports
6. **Executive Summary**: High-level insights for management

### Export Formats
- **PDF**: Formatted reports with charts and visualizations
- **Excel**: Spreadsheet format with multiple sheets
- **CSV**: Raw data for further analysis

### Automated Reporting
```php
// Schedule automated reports
$reportScheduler = new ReportScheduler();
$reportScheduler->scheduleMonthlyReport('<EMAIL>', 'executive_summary');
```

## 🚨 Alert System

### Alert Types
- **Critical**: Immediate attention required (consecutive absences > 5 days)
- **High**: Important issues (attendance rate < 60%)
- **Medium**: Moderate concerns (frequent late arrivals)
- **Low**: Minor observations (productivity variations)

### Alert Configuration
```php
// Configure alert thresholds
$alertSystem = new AlertSystem();
$alertSystem->setThreshold('consecutive_absence_alert', 3);
$alertSystem->setThreshold('late_arrival_threshold', 5);
```

## 🔒 Security Features

### Data Protection
- **SQL Injection Prevention**: Prepared statements for all database queries
- **XSS Protection**: Input sanitization and output encoding
- **CSRF Protection**: Token-based request validation
- **Session Security**: Secure session management

### Access Control
- **Role-based Access**: Admin, Manager, and User roles
- **Permission System**: Granular permissions for different features
- **Audit Logging**: Complete audit trail of all actions

## 🧪 Testing

### Unit Tests
```bash
# Run PHP unit tests
vendor/bin/phpunit tests/

# Run JavaScript tests
npm test
```

### API Testing
```bash
# Test API endpoints
curl -X GET "http://localhost/api/attendance/workers.php" \
     -H "Authorization: Bearer {token}"
```

## 🚀 Deployment

### Production Checklist
- [ ] Update configuration for production environment
- [ ] Set up SSL certificate
- [ ] Configure database backups
- [ ] Set up monitoring and logging
- [ ] Optimize database indexes
- [ ] Enable caching (Redis/Memcached)
- [ ] Configure CDN for static assets

### Performance Optimization
- **Database Indexing**: Proper indexes on frequently queried columns
- **Caching**: Redis for session storage and data caching
- **Asset Optimization**: Minified CSS/JS files
- **Image Optimization**: Compressed profile images

## 🐛 Troubleshooting

### Common Issues

#### Database Connection Errors
```
Error: Connection failed: Access denied for user
```
**Solution**: Check database credentials in `config.php`

#### Permission Denied Errors
```
Error: Permission denied for uploads directory
```
**Solution**: Set proper file permissions
```bash
chmod 755 uploads/
chown www-data:www-data uploads/
```

#### JavaScript Errors
```
Error: Chart.js is not defined
```
**Solution**: Ensure Chart.js is loaded before attendance scripts

### Debug Mode
Enable debug mode in `config.php`:
```php
define('DEBUG_MODE', true);
define('LOG_LEVEL', 'DEBUG');
```

## 📞 Support

### Documentation
- **API Documentation**: `/docs/api.html`
- **User Manual**: `/docs/user-guide.pdf`
- **Admin Guide**: `/docs/admin-guide.pdf`

### Contact
- **Email**: <EMAIL>
- **Documentation**: https://docs.mtcinvoice.com/attendance
- **GitHub**: https://github.com/mtcinvoice/attendance-system

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Chart.js for beautiful charts
- Bootstrap for responsive design
- Font Awesome for icons
- PHP community for excellent documentation
