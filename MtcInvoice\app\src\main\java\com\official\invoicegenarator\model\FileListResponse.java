package com.official.invoicegenarator.model;

import com.google.gson.annotations.SerializedName;

import java.util.List;

public class FileListResponse {
    @SerializedName("success")
    private boolean success;
    
    @SerializedName("current_page")
    private int currentPage;
    
    @SerializedName("total_pages")
    private int totalPages;
    
    @SerializedName("total_files")
    private int totalFiles;
    
    @SerializedName("files")
    private List<FileInfo> files;
    
    @SerializedName("message")
    private String message;

    // Getters and Setters
    public String getMessage() {
        return message;
    }
    
    public void setMessage(String message) {
        this.message = message;
    }
    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public int getCurrentPage() {
        return currentPage;
    }

    public void setCurrentPage(int currentPage) {
        this.currentPage = currentPage;
    }

    public int getTotalPages() {
        return totalPages;
    }

    public void setTotalPages(int totalPages) {
        this.totalPages = totalPages;
    }

    public int getTotalFiles() {
        return totalFiles;
    }

    public void setTotalFiles(int totalFiles) {
        this.totalFiles = totalFiles;
    }

    public List<FileInfo> getFiles() {
        return files;
    }

    public void setFiles(List<FileInfo> files) {
        this.files = files;
    }
}
