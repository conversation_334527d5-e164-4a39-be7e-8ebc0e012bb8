/**
 * Attendance Analytics Styles
 * 
 * Comprehensive styles for the analytics dashboard including
 * charts, heatmaps, pattern analysis, and responsive design.
 * 
 * @package MtcInvoice Attendance
 * @version 1.0
 */

/* Analytics Page Header */
.page-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 30px;
    border-radius: 12px;
    margin-bottom: 30px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.date-range-picker {
    display: flex;
    align-items: center;
    gap: 10px;
}

.date-range-picker input {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    border-radius: 6px;
    padding: 8px 12px;
}

.date-range-picker input:focus {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    box-shadow: none;
}

.date-range-picker input::placeholder {
    color: rgba(255, 255, 255, 0.7);
}

/* Analytics Cards */
.analytics-card {
    background: white;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 20px;
    transition: all 0.3s ease;
    height: 100%;
}

.analytics-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.analytics-card .card-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
    flex-shrink: 0;
}

.analytics-card .card-icon.bg-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.analytics-card .card-icon.bg-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

.analytics-card .card-icon.bg-warning {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
}

.analytics-card .card-icon.bg-info {
    background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
}

.analytics-card .card-content {
    flex: 1;
}

.analytics-card .card-content h3 {
    font-size: 2rem;
    font-weight: 700;
    color: #495057;
    margin-bottom: 5px;
    line-height: 1;
}

.analytics-card .card-content p {
    color: #6c757d;
    margin-bottom: 8px;
    font-weight: 500;
}

.analytics-card .trend {
    font-size: 12px;
    font-weight: 500;
}

.analytics-card .trend i {
    margin-right: 4px;
}

/* Chart Cards */
.analytics-chart-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.analytics-chart-card .chart-header {
    padding: 20px 25px 15px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #f8f9fa;
}

.analytics-chart-card .chart-header h5 {
    margin: 0;
    font-weight: 600;
    color: #495057;
}

.analytics-chart-card .chart-controls {
    display: flex;
    gap: 10px;
    align-items: center;
}

.analytics-chart-card .chart-body {
    padding: 25px;
    flex: 1;
    position: relative;
    min-height: 300px;
}

.analytics-chart-card .chart-body canvas {
    max-height: 400px;
}

/* Heatmap Styles */
.heatmap-svg {
    width: 100%;
    height: auto;
    max-width: 100%;
    overflow: visible;
}

.heatmap-cell {
    cursor: pointer;
    transition: all 0.2s ease;
}

.heatmap-cell:hover {
    stroke: #495057;
    stroke-width: 1;
}

.heatmap-month-label,
.heatmap-day-label {
    font-size: 11px;
    fill: #6c757d;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.heatmap-tooltip {
    position: absolute;
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 12px;
    line-height: 1.4;
    pointer-events: none;
    z-index: 1000;
    max-width: 200px;
    word-wrap: break-word;
}

.heatmap-legend {
    margin-top: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
}

.heatmap-legend .legend-title {
    font-weight: 600;
    color: #495057;
    margin-bottom: 10px;
}

.heatmap-legend .legend-items {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
}

.heatmap-legend .legend-item {
    display: flex;
    align-items: center;
    gap: 6px;
}

.heatmap-legend .legend-color {
    width: 12px;
    height: 12px;
    border-radius: 2px;
}

.heatmap-legend .legend-label {
    font-size: 12px;
    color: #6c757d;
}

.heatmap-legend .legend-note {
    margin-top: 10px;
    color: #6c757d;
}

.heatmap-error {
    text-align: center;
    padding: 40px;
    color: #6c757d;
}

.heatmap-error i {
    font-size: 3rem;
    margin-bottom: 15px;
}

/* Emoji Analytics */
.emoji-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
    gap: 15px;
    max-height: 300px;
    overflow-y: auto;
}

.emoji-item {
    text-align: center;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.emoji-item:hover {
    background: #e9ecef;
    transform: scale(1.05);
}

.emoji-icon {
    font-size: 24px;
    margin-bottom: 8px;
}

.emoji-count {
    font-weight: 600;
    color: #495057;
    font-size: 16px;
}

.emoji-productivity {
    color: #6c757d;
    font-size: 11px;
}

/* Top Performers Table */
#topPerformersTable {
    margin-bottom: 0;
}

#topPerformersTable th {
    background: #f8f9fa;
    border-top: none;
    font-weight: 600;
    color: #495057;
    padding: 15px 12px;
}

#topPerformersTable td {
    padding: 15px 12px;
    vertical-align: middle;
}

.rank-badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    font-weight: 600;
    font-size: 14px;
    color: white;
}

.rank-badge.rank-1 {
    background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
    color: #333;
}

.rank-badge.rank-2 {
    background: linear-gradient(135deg, #c0c0c0 0%, #e8e8e8 100%);
    color: #333;
}

.rank-badge.rank-3 {
    background: linear-gradient(135deg, #cd7f32 0%, #daa520 100%);
    color: white;
}

.rank-badge:not(.rank-1):not(.rank-2):not(.rank-3) {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.performer-info .performer-name {
    font-weight: 600;
    color: #495057;
}

/* Quick Stats */
.quick-stats {
    padding: 10px 0;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #e9ecef;
}

.stat-item:last-child {
    border-bottom: none;
}

.stat-label {
    color: #6c757d;
    font-size: 14px;
}

.stat-value {
    font-weight: 600;
    color: #495057;
    font-size: 16px;
}

/* Pattern Analysis */
.pattern-results {
    padding: 20px 0;
}

.results-header {
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e9ecef;
}

.results-header h6 {
    color: #495057;
    font-weight: 600;
    margin-bottom: 5px;
}

.pattern-card {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 15px;
}

.pattern-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.pattern-title {
    font-weight: 600;
    color: #495057;
    display: flex;
    align-items: center;
    gap: 8px;
}

.pattern-description {
    color: #6c757d;
    margin-bottom: 15px;
    font-size: 14px;
}

.pattern-findings ul {
    margin-bottom: 15px;
    padding-left: 20px;
}

.pattern-findings li {
    color: #495057;
    margin-bottom: 5px;
    font-size: 14px;
}

.pattern-impact {
    display: flex;
    align-items: center;
    gap: 10px;
}

.impact-label {
    font-weight: 500;
    color: #6c757d;
    font-size: 14px;
}

/* Insights */
.insight-card {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    border-left: 4px solid #667eea;
}

.insight-card.priority-high {
    border-left-color: #dc3545;
}

.insight-card.priority-medium {
    border-left-color: #ffc107;
}

.insight-card.priority-low {
    border-left-color: #17a2b8;
}

.insight-category {
    font-weight: 600;
    color: #667eea;
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 8px;
}

.insight-text {
    color: #495057;
    margin-bottom: 10px;
    font-size: 14px;
    line-height: 1.5;
}

.insight-recommendation {
    color: #6c757d;
    font-size: 13px;
    margin-bottom: 10px;
}

.insight-priority {
    text-align: right;
}

/* Anomalies */
.anomaly-card {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 10px;
}

.anomaly-card.severity-high {
    border-left: 4px solid #dc3545;
}

.anomaly-card.severity-medium {
    border-left: 4px solid #ffc107;
}

.anomaly-card.severity-low {
    border-left: 4px solid #17a2b8;
}

.anomaly-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
}

.anomaly-date {
    font-weight: 600;
    color: #495057;
    font-size: 12px;
}

.anomaly-description {
    color: #495057;
    margin-bottom: 8px;
    font-size: 14px;
}

.anomaly-details {
    color: #6c757d;
    margin-bottom: 10px;
}

.anomaly-severity {
    text-align: right;
}

/* Analysis Progress */
.analysis-progress {
    padding: 40px;
}

.analysis-progress .progress {
    height: 8px;
    border-radius: 4px;
}

.analysis-progress .progress-bar {
    border-radius: 4px;
}

#analysisStatus {
    color: #6c757d;
    font-size: 14px;
    margin-top: 10px;
}

.analysis-error {
    padding: 40px;
}

.analysis-actions {
    padding-top: 20px;
    border-top: 1px solid #e9ecef;
    display: flex;
    gap: 10px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .page-header {
        padding: 20px;
        text-align: center;
    }
    
    .date-range-picker {
        flex-direction: column;
        width: 100%;
        margin-top: 15px;
    }
    
    .date-range-picker input {
        width: 100%;
    }
    
    .analytics-card {
        padding: 20px;
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }
    
    .analytics-card .card-content h3 {
        font-size: 1.5rem;
    }
    
    .analytics-chart-card .chart-header {
        padding: 15px 20px 10px;
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }
    
    .analytics-chart-card .chart-controls {
        justify-content: center;
    }
    
    .analytics-chart-card .chart-body {
        padding: 20px;
        min-height: 250px;
    }
    
    .emoji-grid {
        grid-template-columns: repeat(auto-fit, minmax(60px, 1fr));
        gap: 10px;
    }
    
    .emoji-item {
        padding: 10px;
    }
    
    .emoji-icon {
        font-size: 20px;
    }
    
    .heatmap-legend .legend-items {
        flex-direction: column;
        gap: 8px;
    }
    
    .pattern-header {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }
    
    .insight-priority {
        text-align: left;
        margin-top: 10px;
    }
    
    .analysis-actions {
        flex-direction: column;
    }
}

@media (max-width: 576px) {
    .analytics-card .card-icon {
        width: 50px;
        height: 50px;
        font-size: 20px;
    }
    
    .analytics-card .card-content h3 {
        font-size: 1.25rem;
    }
    
    .analytics-chart-card .chart-body {
        padding: 15px;
        min-height: 200px;
    }
    
    .pattern-card,
    .insight-card,
    .anomaly-card {
        padding: 12px;
    }
    
    .stat-item {
        padding: 8px 0;
    }
    
    .stat-label,
    .stat-value {
        font-size: 13px;
    }
    
    #topPerformersTable th,
    #topPerformersTable td {
        padding: 10px 8px;
        font-size: 13px;
    }
    
    .rank-badge {
        width: 25px;
        height: 25px;
        font-size: 12px;
    }
}
