<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Offline - Attendance Management System</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            padding: 20px;
        }
        
        .offline-container {
            max-width: 500px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .offline-icon {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.8;
        }
        
        .offline-title {
            font-size: 2rem;
            font-weight: 600;
            margin-bottom: 15px;
        }
        
        .offline-message {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 30px;
            opacity: 0.9;
        }
        
        .offline-features {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            text-align: left;
        }
        
        .offline-features h3 {
            font-size: 1.3rem;
            margin-bottom: 15px;
            text-align: center;
        }
        
        .offline-features ul {
            list-style: none;
            padding: 0;
        }
        
        .offline-features li {
            padding: 8px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
        }
        
        .offline-features li:last-child {
            border-bottom: none;
        }
        
        .offline-features li::before {
            content: "✓";
            margin-right: 10px;
            color: #4ade80;
            font-weight: bold;
        }
        
        .retry-button {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            margin: 0 10px;
        }
        
        .retry-button:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-2px);
            color: white;
            text-decoration: none;
        }
        
        .connection-status {
            margin-top: 20px;
            padding: 10px;
            border-radius: 10px;
            font-size: 0.9rem;
        }
        
        .connection-status.online {
            background: rgba(74, 222, 128, 0.2);
            border: 1px solid rgba(74, 222, 128, 0.3);
        }
        
        .connection-status.offline {
            background: rgba(248, 113, 113, 0.2);
            border: 1px solid rgba(248, 113, 113, 0.3);
        }
        
        .cached-data {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            margin-top: 20px;
        }
        
        .cached-data h4 {
            margin-bottom: 15px;
            color: #fbbf24;
        }
        
        .cached-item {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 10px;
            margin: 8px 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .cached-item-name {
            font-weight: 500;
        }
        
        .cached-item-time {
            font-size: 0.8rem;
            opacity: 0.7;
        }
        
        @media (max-width: 768px) {
            .offline-container {
                padding: 30px 20px;
                margin: 10px;
            }
            
            .offline-title {
                font-size: 1.5rem;
            }
            
            .offline-message {
                font-size: 1rem;
            }
            
            .retry-button {
                display: block;
                margin: 10px 0;
                width: 100%;
            }
        }
        
        .pulse {
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .sync-status {
            margin-top: 15px;
            padding: 10px;
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.1);
            font-size: 0.9rem;
        }
        
        .sync-pending {
            color: #fbbf24;
        }
        
        .sync-complete {
            color: #4ade80;
        }
    </style>
</head>
<body>
    <div class="offline-container">
        <div class="offline-icon pulse">📡</div>
        <h1 class="offline-title">You're Offline</h1>
        <p class="offline-message">
            No internet connection detected. Don't worry - you can still access some features of the Attendance Management System.
        </p>
        
        <div class="offline-features">
            <h3>Available Offline Features</h3>
            <ul>
                <li>View cached attendance data</li>
                <li>Browse worker information</li>
                <li>View recent analytics</li>
                <li>Queue attendance records for sync</li>
                <li>Access offline reports</li>
            </ul>
        </div>
        
        <div class="connection-status offline" id="connectionStatus">
            <strong>Status:</strong> <span id="statusText">Offline</span>
        </div>
        
        <div class="sync-status" id="syncStatus" style="display: none;">
            <span id="syncText">Checking for pending sync...</span>
        </div>
        
        <div style="margin-top: 30px;">
            <button class="retry-button" onclick="checkConnection()">
                🔄 Check Connection
            </button>
            <a href="/MtcInvoiceNewProject/admin/attendance/" class="retry-button">
                🏠 Go to Dashboard
            </a>
        </div>
        
        <div class="cached-data" id="cachedData" style="display: none;">
            <h4>📦 Cached Data Available</h4>
            <div id="cachedItems"></div>
        </div>
    </div>

    <script>
        // Check connection status
        function updateConnectionStatus() {
            const statusElement = document.getElementById('connectionStatus');
            const statusText = document.getElementById('statusText');
            
            if (navigator.onLine) {
                statusElement.className = 'connection-status online';
                statusText.textContent = 'Online - Reconnected!';
                
                // Show sync status
                document.getElementById('syncStatus').style.display = 'block';
                checkPendingSync();
                
                // Auto-redirect after 3 seconds
                setTimeout(() => {
                    window.location.href = '/MtcInvoiceNewProject/admin/attendance/';
                }, 3000);
            } else {
                statusElement.className = 'connection-status offline';
                statusText.textContent = 'Offline';
                document.getElementById('syncStatus').style.display = 'none';
            }
        }
        
        // Check for pending sync items
        async function checkPendingSync() {
            try {
                const db = await openOfflineDB();
                const transaction = db.transaction(['offlineRequests'], 'readonly');
                const store = transaction.objectStore('offlineRequests');
                const request = store.count();
                
                request.onsuccess = () => {
                    const count = request.result;
                    const syncText = document.getElementById('syncText');
                    
                    if (count > 0) {
                        syncText.innerHTML = `<span class="sync-pending">⏳ ${count} items pending sync</span>`;
                        
                        // Trigger background sync
                        if ('serviceWorker' in navigator && 'sync' in window.ServiceWorkerRegistration.prototype) {
                            navigator.serviceWorker.ready.then(registration => {
                                return registration.sync.register('attendance-sync');
                            });
                        }
                    } else {
                        syncText.innerHTML = `<span class="sync-complete">✅ All data synced</span>`;
                    }
                };
            } catch (error) {
                console.error('Error checking sync status:', error);
            }
        }
        
        // Open IndexedDB
        function openOfflineDB() {
            return new Promise((resolve, reject) => {
                const request = indexedDB.open('AttendanceOfflineDB', 1);
                request.onerror = () => reject(request.error);
                request.onsuccess = () => resolve(request.result);
            });
        }
        
        // Check connection manually
        function checkConnection() {
            const button = event.target;
            const originalText = button.textContent;
            
            button.textContent = '🔄 Checking...';
            button.disabled = true;
            
            // Simulate connection check
            fetch('/MtcInvoiceNewProject/admin/attendance/', { 
                method: 'HEAD',
                cache: 'no-cache'
            })
            .then(response => {
                if (response.ok) {
                    updateConnectionStatus();
                } else {
                    throw new Error('Connection failed');
                }
            })
            .catch(() => {
                button.textContent = '❌ Still Offline';
                setTimeout(() => {
                    button.textContent = originalText;
                    button.disabled = false;
                }, 2000);
            });
        }
        
        // Load cached data
        async function loadCachedData() {
            try {
                const cache = await caches.open('attendance-system-v1.0.0');
                const keys = await cache.keys();
                
                const cachedItems = keys
                    .filter(request => request.url.includes('/api/attendance/'))
                    .map(request => {
                        const url = new URL(request.url);
                        return {
                            name: url.pathname.split('/').pop(),
                            url: request.url,
                            time: 'Recently cached'
                        };
                    });
                
                if (cachedItems.length > 0) {
                    const cachedData = document.getElementById('cachedData');
                    const cachedItemsContainer = document.getElementById('cachedItems');
                    
                    cachedItemsContainer.innerHTML = cachedItems.map(item => `
                        <div class="cached-item">
                            <span class="cached-item-name">${item.name}</span>
                            <span class="cached-item-time">${item.time}</span>
                        </div>
                    `).join('');
                    
                    cachedData.style.display = 'block';
                }
            } catch (error) {
                console.error('Error loading cached data:', error);
            }
        }
        
        // Event listeners
        window.addEventListener('online', updateConnectionStatus);
        window.addEventListener('offline', updateConnectionStatus);
        
        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            updateConnectionStatus();
            loadCachedData();
        });
        
        // Periodic connection check
        setInterval(updateConnectionStatus, 30000);
    </script>
</body>
</html>
