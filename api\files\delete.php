<?php
require_once __DIR__ . '/../config/config.php';

header('Content-Type: application/json');

if (isset($_GET['id'])) {
    $fileId = intval($_GET['id']);

    $db = new Database();
    $conn = $db->getConnection();

    if ($conn) {
        // Fetch file info
        $stmt = $conn->prepare("SELECT file_path, file_name FROM documents WHERE id = ?");
        $stmt->execute([$fileId]);
        $file = $stmt->fetch();

        if ($file) {
            // Delete the file from the filesystem
            $filePath = __DIR__ . '/../../api/uploads/pdfs/' . $file['file_name'];
            if (file_exists($filePath)) {
                unlink($filePath);
            }

            // Delete from database
            $deleteStmt = $conn->prepare("DELETE FROM documents WHERE id = ?");
            $deleteStmt->execute([$fileId]);

            echo json_encode(["success" => true, "message" => "File deleted successfully."]);
        } else {
            echo json_encode(["success" => false, "message" => "File not found."]);
        }
    } else {
        echo json_encode(["success" => false, "message" => "Database connection failed."]);
    }
} else {
    echo json_encode(["success" => false, "message" => "Missing file ID."]);
}
