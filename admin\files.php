<?php
/**
 * File Management
 *
 * @package MtcInvoice Admin
 * @version 1.1 - Rewritten for correctness and robustness
 */

require_once 'config/config.php';

// Require login
requireLogin();

$current_page = 'files';
$page_title = 'File Management';

// Handle file deletion
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'delete') {
    $file_id = $_POST['file_id'] ?? '';
    if ($file_id) {
        $pdo = getDbConnection();
        try {
            // Use a transaction for data integrity. All operations must succeed or none will.
            $pdo->beginTransaction();

            // --- FIX 1: Corrected SQL Query ---
            // Get file info using correct column names (file_path, file_size_kb)
            // and JOIN with users table to get the correct user_id.
            $stmt = $pdo->prepare("
                SELECT
                    d.file_path,
                    d.file_size_kb,
                    u.id as user_id
                FROM documents d
                LEFT JOIN users u ON d.uploaded_by = u.email
                WHERE d.id = ?
            ");
            $stmt->execute([$file_id]);
            $file_info = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($file_info) {
                // 1. Delete file record from the database
                $stmt = $pdo->prepare("DELETE FROM documents WHERE id = ?");
                $stmt->execute([$file_id]);

                // --- FIX 2: Corrected Physical File Path Logic ---
                // Use the same flexible logic from the display section to find the file.
                $storage_path = $file_info['file_path'];
                $physical_file_path = '../api/uploads/' . $storage_path;
                
                if (!file_exists($physical_file_path)) {
                    $physical_file_path = '../api/uploads/pdfs/' . basename($storage_path);
                }

                if (file_exists($physical_file_path)) {
                    unlink($physical_file_path);
                }

                // 3. Update user's storage usage
                // Convert file_size_kb to bytes for the calculation
                $file_size_bytes = $file_info['file_size_kb'] * 1024;
                
                $stmt = $pdo->prepare("
                    UPDATE storage_usage
                    SET total_used = GREATEST(0, total_used - ?),
                        document_count = GREATEST(0, document_count - 1),
                        last_updated = NOW()
                    WHERE user_id = ?
                ");
                $stmt->execute([$file_size_bytes, $file_info['user_id']]);

                // If all operations were successful, commit the changes
                $pdo->commit();
                showAlert('File deleted successfully', 'success');

            } else {
                // This will happen if file_id is invalid or user link is broken
                $pdo->rollBack();
                showAlert('File not found or associated user is missing.', 'warning');
            }
        } catch (Exception $e) {
            // If any error occurs, roll back the entire transaction
            if ($pdo->inTransaction()) {
                $pdo->rollBack();
            }
            showAlert('Failed to delete file: ' . $e->getMessage(), 'danger');
            error_log("File deletion error: " . $e->getMessage());
        }
    }
    header('Location: ' . $_SERVER['PHP_SELF']);
    exit();
}

// Get file list from database
$files = [];
try {
    $pdo = getDbConnection();

    // Get files from database with user information
    $query = "
        SELECT
            d.id,
            d.file_name as original_name,
            d.file_path as storage_path,
            d.file_size_kb * 1024 as file_size,
            d.file_type,
            d.upload_date,
            d.download_count,
            d.uploaded_by as uploaded_by_name
        FROM documents d
        ORDER BY d.upload_date DESC
    ";
    
    $stmt = $pdo->query($query);
    $files = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Add additional file information needed for display
    foreach ($files as &$file) {
        $file['extension'] = strtolower(pathinfo($file['original_name'], PATHINFO_EXTENSION));
        
        // Construct the correct physical file path for checking existence
        $storage_path = $file['storage_path'];
        $file_path = '../api/uploads/' . $storage_path;
        
        if (!file_exists($file_path)) {
            $file_path = '../api/uploads/pdfs/' . basename($storage_path);
        }
        
        $file['exists'] = file_exists($file_path);
    }

} catch (Exception $e) {
    $error_message = "Error fetching files: " . $e->getMessage();
    error_log($error_message);
    showAlert($error_message, 'danger');
}

include 'includes/header.php';
?>

<!-- File Management Content -->
<div class="row mb-4">
    <div class="col-md-6">
        <h1 class="h3 mb-0">File Management</h1>
        <p class="text-muted">Manage all uploaded user documents.</p>
    </div>
    <div class="col-md-6 text-end">
        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#uploadModal">
            <i class="bi bi-cloud-upload me-2"></i>
            Upload File
        </button>
    </div>
</div>

<!-- File Statistics -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card stat-card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title text-white-50 mb-1">Total Files</h6>
                        <h3 class="mb-0"><?= count($files) ?></h3>
                    </div>
                    <div class="text-white-50"><i class="bi bi-files" style="font-size: 2rem;"></i></div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stat-card success">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title text-white-50 mb-1">Total Size</h6>
                        <h3 class="mb-0"><?= formatFileSize(array_sum(array_column($files, 'file_size'))) ?></h3>
                    </div>
                    <div class="text-white-50"><i class="bi bi-hdd" style="font-size: 2rem;"></i></div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stat-card warning">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title text-white-50 mb-1">PDF Files</h6>
                        <h3 class="mb-0"><?= count(array_filter($files, function($f) { return $f['extension'] === 'pdf'; })) ?></h3>
                    </div>
                    <div class="text-white-50"><i class="bi bi-file-earmark-pdf" style="font-size: 2rem;"></i></div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stat-card info">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title text-white-50 mb-1">Images</h6>
                        <h3 class="mb-0"><?= count(array_filter($files, function($f) { return in_array($f['extension'], ['jpg', 'jpeg', 'png']); })) ?></h3>
                    </div>
                    <div class="text-white-50"><i class="bi bi-image" style="font-size: 2rem;"></i></div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- File List -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0"><i class="bi bi-folder me-2"></i>Uploaded Files</h5>
    </div>
    <div class="card-body">
        <?php if (empty($files)): ?>
            <div class="text-center py-5">
                <i class="bi bi-cloud-slash text-muted" style="font-size: 4rem;"></i>
                <h4 class="text-muted mt-3">No Files Found</h4>
                <p class="text-muted">Upload a file to see it listed here.</p>
                <button class="btn btn-primary mt-2" data-bs-toggle="modal" data-bs-target="#uploadModal">
                    <i class="bi bi-cloud-upload me-2"></i>Upload Your First File
                </button>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover align-middle">
                    <thead>
                        <tr>
                            <th>File</th>
                            <th>Size</th>
                            <th>Uploaded By</th>
                            <th>Upload Date</th>
                            <th>Downloads</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($files as $file): ?>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <i class="bi bi-<?= getFileIcon($file['extension']) ?> me-2 text-primary fs-4"></i>
                                        <div>
                                            <div class="fw-bold"><?= htmlspecialchars($file['original_name']) ?></div>
                                            <small class="text-muted">ID: <?= $file['id'] ?> | Type: <?= strtoupper($file['extension']) ?></small>
                                        </div>
                                    </div>
                                </td>
                                <td><?= formatFileSize($file['file_size']) ?></td>
                                <td><?= htmlspecialchars($file['uploaded_by_name'] ?? 'Unknown') ?></td>
                                <td><?= date('M j, Y, g:i A', strtotime($file['upload_date'])) ?></td>
                                <td><span class="badge bg-info"><?= $file['download_count'] ?></span></td>
                                <td>
                                    <?php if ($file['exists']): ?>
                                        <span class="badge bg-success">Available</span>
                                    <?php else: ?>
                                        <span class="badge bg-danger">Missing</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <?php if ($file['exists']): ?>
                                            <a href="download.php?file=<?= urlencode($file['id']) ?>&action=view" class="btn btn-outline-primary" target="_blank" title="View"><i class="bi bi-eye"></i></a>
                                            <a href="download.php?file=<?= urlencode($file['id']) ?>&action=download" class="btn btn-outline-success" title="Download"><i class="bi bi-download"></i></a>
                                        <?php else: ?>
                                            <button type="button" class="btn btn-outline-secondary" disabled title="File Missing"><i class="bi bi-eye-slash"></i></button>
                                            <button type="button" class="btn btn-outline-secondary" disabled title="File Missing"><i class="bi bi-download"></i></button>
                                        <?php endif; ?>
                                        <button type="button" class="btn btn-outline-danger" onclick="deleteFile('<?= htmlspecialchars($file['id']) ?>', '<?= htmlspecialchars($file['original_name']) ?>')" title="Delete"><i class="bi bi-trash"></i></button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Upload Modal -->
<div class="modal fade" id="uploadModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Upload File</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="uploadForm" action="upload.php" method="POST" enctype="multipart/form-data">
                    <div class="mb-3">
                        <label for="file" class="form-label">Select File</label>
                        <input type="file" class="form-control" id="file" name="file" accept=".pdf,.jpg,.jpeg,.png,.doc,.docx" required>
                        <div class="form-text">Allowed types: PDF, JPG, PNG, DOC, DOCX. Max size: 50MB.</div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="uploadFile()">
                    <i class="bi bi-cloud-upload me-2"></i>Upload
                </button>
            </div>
        </div>
    </div>
</div>

<?php
function getFileIcon($extension) {
    switch($extension) {
        case 'pdf':
            return 'file-earmark-pdf';
        case 'jpg':
        case 'jpeg':
        case 'png':
        case 'gif':
            return 'image';
        case 'doc':
        case 'docx':
            return 'file-earmark-word';
        default:
            return 'file-earmark';
    }
}

$extra_js = "
<script>
function deleteFile(fileId, fileName) {
    // Assuming you have a standard confirm function, e.g., window.confirm
    // Or a custom one like `confirmDelete` from a global script.
    if (confirm('Are you sure you want to delete \"' + fileName + '\"? This action cannot be undone.')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.style.display = 'none'; // Hide the form
        form.innerHTML = `<input type=\"hidden\" name=\"action\" value=\"delete\">
                          <input type=\"hidden\" name=\"file_id\" value=\"\${fileId}\">`;
        document.body.appendChild(form);
        form.submit();
    }
}

function uploadFile() {
    const form = document.getElementById('uploadForm');
    const fileInput = document.getElementById('file');
    
    if (!fileInput.files[0]) {
        // Assuming you have a showToast function
        showToast('Please select a file to upload.', 'warning');
        return;
    }
    
    const formData = new FormData(form);
    const uploadBtn = event.target;
    
    // Disable button and show loading state
    uploadBtn.disabled = true;
    uploadBtn.innerHTML = '<span class=\"spinner-border spinner-border-sm\" role=\"status\" aria-hidden=\"true\"></span> Uploading...';
    
    fetch(form.action, {
        method: 'POST',
        body: formData
    })
    .then(response => {
        if (!response.ok) {
            // Handle HTTP errors like 500
            throw new Error(`Server responded with status: \${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            showToast('File uploaded successfully!', 'success');
            setTimeout(() => location.reload(), 1500);
        } else {
            showToast(data.error?.message || 'An unknown error occurred during upload.', 'danger');
        }
    })
    .catch(error => {
        showToast('Upload failed. Please check the console for details.', 'danger');
        console.error('Upload Error:', error);
    })
    .finally(() => {
        // Re-enable button and restore original text
        uploadBtn.disabled = false;
        uploadBtn.innerHTML = '<i class=\"bi bi-cloud-upload me-2\"></i>Upload';
    });
}
</script>
";

include 'includes/footer.php';
?>