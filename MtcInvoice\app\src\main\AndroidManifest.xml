<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission
        android:name="android.permission.MANAGE_EXTERNAL_STORAGE"
        tools:ignore="ScopedStorage" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" /> <!-- Need this for API 33 -->
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />

    <application
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:networkSecurityConfig="@xml/network_security_config"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.NewInvoice"
        tools:targetApi="31">
        <activity
            android:name=".InvoiceTwo"
            android:exported="false" />
        <activity
            android:name=".InvoiceTraker"
            android:exported="false" />
        <activity
            android:name=".PdfViewerActivity"
            android:exported="false" />
        <activity
            android:name=".DownloadListActivityFixed"
            android:exported="false" />
        <activity
            android:name=".ProfileActivity"
            android:exported="false" />
        <activity
            android:name=".FingerprintSettingsActivity"
            android:exported="false" />
        <activity
            android:name=".VarifyActivity"
            android:exported="false" />

        <activity
            android:name=".LoginActivity"
            android:exported="false"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name=".SelectionActivity"
            android:exported="false" />
        <activity
            android:name=".Home"
            android:exported="false"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".MainActivity"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
    </application>

</manifest>