/**
 * Attendance Analytics Charts
 * 
 * Advanced charting functionality for attendance analytics
 * including trend analysis, performance metrics, and interactive visualizations.
 * 
 * @package MtcInvoice Attendance
 * @version 1.0
 */

class AttendanceAnalyticsCharts {
    constructor() {
        this.apiBaseUrl = '/MtcInvoiceNewProject/api/attendance';
        this.charts = {};
        this.analyticsData = {};
        this.chartColors = {
            primary: '#667eea',
            success: '#28a745',
            warning: '#ffc107',
            danger: '#dc3545',
            info: '#17a2b8',
            secondary: '#6c757d',
            light: '#f8f9fa',
            dark: '#343a40'
        };
        
        this.init();
    }
    
    async init() {
        try {
            this.setupEventListeners();
            await this.loadAnalyticsData();
            this.renderAllCharts();
        } catch (error) {
            console.error('Failed to initialize analytics charts:', error);
            this.showError('Failed to initialize analytics');
        }
    }
    
    setupEventListeners() {
        // Date range change
        document.getElementById('analyticsStartDate').addEventListener('change', () => this.refreshData());
        document.getElementById('analyticsEndDate').addEventListener('change', () => this.refreshData());
        
        // Refresh button
        document.getElementById('refreshAnalytics').addEventListener('click', () => this.refreshData());
        
        // Trend period change
        document.querySelectorAll('input[name="trendPeriod"]').forEach(radio => {
            radio.addEventListener('change', (e) => this.updateTrendChart(e.target.value));
        });
        
        // Heatmap worker filter
        document.getElementById('heatmapWorker').addEventListener('change', () => this.updateHeatmap());
        
        // Performance metric change
        document.getElementById('performanceMetric').addEventListener('change', () => this.updateTopPerformers());
        
        // Export report type change
        document.getElementById('exportReportType').addEventListener('change', (e) => {
            const workerDiv = document.getElementById('workerSelectDiv');
            workerDiv.style.display = e.target.value === 'worker' ? 'block' : 'none';
        });
    }
    
    async loadAnalyticsData() {
        try {
            const startDate = document.getElementById('analyticsStartDate').value;
            const endDate = document.getElementById('analyticsEndDate').value;
            
            const params = new URLSearchParams({
                start_date: startDate,
                end_date: endDate
            });
            
            // Load dashboard data
            const dashboardResponse = await fetch(`${this.apiBaseUrl}/analytics.php/dashboard?${params}`);
            const dashboardData = await dashboardResponse.json();
            
            if (dashboardData.success) {
                this.analyticsData = dashboardData.data;
            }
            
            // Load additional analytics data
            await this.loadWorkerPerformance();
            await this.loadEmojiAnalytics();
            await this.loadProductivityInsights();
            
        } catch (error) {
            console.error('Failed to load analytics data:', error);
            throw error;
        }
    }
    
    async loadWorkerPerformance() {
        try {
            const startDate = document.getElementById('analyticsStartDate').value;
            const endDate = document.getElementById('analyticsEndDate').value;
            
            const params = new URLSearchParams({
                start_date: startDate,
                end_date: endDate
            });
            
            const response = await fetch(`${this.apiBaseUrl}/analytics.php/performance?${params}`);
            const data = await response.json();
            
            if (data.success) {
                this.analyticsData.worker_performance = data.data;
            }
        } catch (error) {
            console.error('Failed to load worker performance:', error);
        }
    }
    
    async loadEmojiAnalytics() {
        try {
            const startDate = document.getElementById('analyticsStartDate').value;
            const endDate = document.getElementById('analyticsEndDate').value;
            
            const params = new URLSearchParams({
                start_date: startDate,
                end_date: endDate
            });
            
            const response = await fetch(`${this.apiBaseUrl}/analytics.php/emoji?${params}`);
            const data = await response.json();
            
            if (data.success) {
                this.analyticsData.emoji_analytics = data.data;
            }
        } catch (error) {
            console.error('Failed to load emoji analytics:', error);
        }
    }
    
    async loadProductivityInsights() {
        try {
            const startDate = document.getElementById('analyticsStartDate').value;
            const endDate = document.getElementById('analyticsEndDate').value;
            
            const params = new URLSearchParams({
                start_date: startDate,
                end_date: endDate
            });
            
            const response = await fetch(`${this.apiBaseUrl}/analytics.php/productivity?${params}`);
            const data = await response.json();
            
            if (data.success) {
                this.analyticsData.productivity_insights = data.data;
            }
        } catch (error) {
            console.error('Failed to load productivity insights:', error);
        }
    }
    
    renderAllCharts() {
        this.renderOverviewCards();
        this.renderTrendChart();
        this.renderDepartmentChart();
        this.renderStatusDistributionChart();
        this.renderProductivityChart();
        this.renderEmojiAnalytics();
        this.renderTopPerformersTable();
        this.renderQuickStats();
        this.populateWorkerSelects();
    }
    
    renderOverviewCards() {
        const overview = this.analyticsData.overview || {};
        
        document.getElementById('overallAttendanceRate').textContent = (overview.attendance_rate || 0) + '%';
        
        // Calculate punctuality rate
        const presentCount = overview.present_count || 0;
        const lateCount = overview.late_count || 0;
        const punctualityRate = (presentCount + lateCount) > 0 ? 
            Math.round((presentCount / (presentCount + lateCount)) * 100) : 0;
        document.getElementById('punctualityRate').textContent = punctualityRate + '%';
        
        document.getElementById('totalAlerts').textContent = '0'; // Would come from alerts API
        document.getElementById('avgProductivity').textContent = 
            overview.avg_productivity_score ? overview.avg_productivity_score.toFixed(1) : 'N/A';
        
        // Add trend indicators (simplified for demo)
        this.updateTrendIndicator('attendanceRateTrend', 2.5);
        this.updateTrendIndicator('punctualityTrend', -1.2);
        this.updateTrendIndicator('alertsTrend', 0);
        this.updateTrendIndicator('productivityTrend', 0.8);
    }
    
    updateTrendIndicator(elementId, change) {
        const element = document.getElementById(elementId);
        if (!element) return;
        
        const isPositive = change > 0;
        const isNegative = change < 0;
        
        element.innerHTML = `
            <i class="bi bi-${isPositive ? 'arrow-up' : isNegative ? 'arrow-down' : 'dash'}"></i>
            ${Math.abs(change)}% ${isPositive ? 'increase' : isNegative ? 'decrease' : 'no change'}
        `;
        
        element.className = `trend ${isPositive ? 'text-success' : isNegative ? 'text-danger' : 'text-muted'}`;
    }
    
    renderTrendChart() {
        const ctx = document.getElementById('attendanceTrendsChart').getContext('2d');
        const trends = this.analyticsData.trends || [];
        
        // Destroy existing chart
        if (this.charts.trendsChart) {
            this.charts.trendsChart.destroy();
        }
        
        const labels = trends.map(t => {
            const date = new Date(t.period);
            return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
        });
        
        this.charts.trendsChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [
                    {
                        label: 'Present',
                        data: trends.map(t => t.present_count),
                        borderColor: this.chartColors.success,
                        backgroundColor: this.chartColors.success + '20',
                        tension: 0.4,
                        fill: true
                    },
                    {
                        label: 'Late',
                        data: trends.map(t => t.late_count),
                        borderColor: this.chartColors.warning,
                        backgroundColor: this.chartColors.warning + '20',
                        tension: 0.4,
                        fill: true
                    },
                    {
                        label: 'Absent',
                        data: trends.map(t => t.absent_count),
                        borderColor: this.chartColors.danger,
                        backgroundColor: this.chartColors.danger + '20',
                        tension: 0.4,
                        fill: true
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top'
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false
                    }
                },
                scales: {
                    x: {
                        display: true,
                        title: {
                            display: true,
                            text: 'Date'
                        }
                    },
                    y: {
                        display: true,
                        title: {
                            display: true,
                            text: 'Number of Workers'
                        },
                        beginAtZero: true
                    }
                },
                interaction: {
                    mode: 'nearest',
                    axis: 'x',
                    intersect: false
                }
            }
        });
    }
    
    renderDepartmentChart() {
        const ctx = document.getElementById('departmentPerformanceChart').getContext('2d');
        const departments = this.analyticsData.departments || [];
        
        // Destroy existing chart
        if (this.charts.departmentChart) {
            this.charts.departmentChart.destroy();
        }
        
        this.charts.departmentChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: departments.map(d => d.department),
                datasets: [{
                    label: 'Attendance Rate (%)',
                    data: departments.map(d => d.attendance_rate),
                    backgroundColor: departments.map((_, i) => 
                        Object.values(this.chartColors)[i % Object.values(this.chartColors).length] + '80'
                    ),
                    borderColor: departments.map((_, i) => 
                        Object.values(this.chartColors)[i % Object.values(this.chartColors).length]
                    ),
                    borderWidth: 2,
                    borderRadius: 4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100,
                        title: {
                            display: true,
                            text: 'Attendance Rate (%)'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Department'
                        }
                    }
                }
            }
        });
    }
    
    renderStatusDistributionChart() {
        const ctx = document.getElementById('statusDistributionChart').getContext('2d');
        const overview = this.analyticsData.overview || {};
        
        // Destroy existing chart
        if (this.charts.statusChart) {
            this.charts.statusChart.destroy();
        }
        
        const data = [
            overview.present_count || 0,
            overview.late_count || 0,
            overview.absent_count || 0,
            overview.sick_leave_count || 0,
            overview.vacation_count || 0,
            overview.remote_work_count || 0
        ];
        
        const labels = ['Present', 'Late', 'Absent', 'Sick Leave', 'Vacation', 'Remote Work'];
        const colors = [
            this.chartColors.success,
            this.chartColors.warning,
            this.chartColors.danger,
            this.chartColors.info,
            this.chartColors.primary,
            this.chartColors.secondary
        ];
        
        this.charts.statusChart = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: labels,
                datasets: [{
                    data: data,
                    backgroundColor: colors,
                    borderWidth: 3,
                    borderColor: '#fff',
                    hoverBorderWidth: 4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((context.parsed / total) * 100).toFixed(1);
                                return `${context.label}: ${context.parsed} (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        });
    }
    
    renderProductivityChart() {
        const ctx = document.getElementById('productivityByDayChart').getContext('2d');
        const productivity = this.analyticsData.productivity_insights || [];
        
        // Destroy existing chart
        if (this.charts.productivityChart) {
            this.charts.productivityChart.destroy();
        }
        
        this.charts.productivityChart = new Chart(ctx, {
            type: 'radar',
            data: {
                labels: productivity.map(p => p.day_name),
                datasets: [{
                    label: 'Average Productivity Score',
                    data: productivity.map(p => p.avg_productivity),
                    backgroundColor: this.chartColors.primary + '20',
                    borderColor: this.chartColors.primary,
                    borderWidth: 2,
                    pointBackgroundColor: this.chartColors.primary,
                    pointBorderColor: '#fff',
                    pointBorderWidth: 2,
                    pointRadius: 5
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    r: {
                        beginAtZero: true,
                        max: 5,
                        ticks: {
                            stepSize: 1
                        }
                    }
                }
            }
        });
    }
    
    renderEmojiAnalytics() {
        const container = document.getElementById('emojiAnalytics');
        const emojiData = this.analyticsData.emoji_analytics || [];
        
        if (emojiData.length === 0) {
            container.innerHTML = '<div class="text-center py-4 text-muted">No emoji data available</div>';
            return;
        }
        
        container.innerHTML = `
            <div class="emoji-grid">
                ${emojiData.slice(0, 12).map(emoji => `
                    <div class="emoji-item">
                        <div class="emoji-icon">${emoji.emoji}</div>
                        <div class="emoji-count">${emoji.total_usage}</div>
                        <div class="emoji-productivity">
                            <small>Avg: ${emoji.avg_productivity || 'N/A'}</small>
                        </div>
                    </div>
                `).join('')}
            </div>
        `;
    }
    
    renderTopPerformersTable() {
        const tableBody = document.querySelector('#topPerformersTable tbody');
        const performers = this.analyticsData.worker_performance || [];
        
        if (performers.length === 0) {
            tableBody.innerHTML = '<tr><td colspan="6" class="text-center">No performance data available</td></tr>';
            return;
        }
        
        tableBody.innerHTML = performers.slice(0, 10).map((performer, index) => `
            <tr>
                <td>
                    <span class="rank-badge rank-${index + 1}">#${index + 1}</span>
                </td>
                <td>
                    <div class="performer-info">
                        <div class="performer-name">${performer.name}</div>
                        <small class="text-muted">${performer.employee_id}</small>
                    </div>
                </td>
                <td>${performer.department}</td>
                <td>
                    <div class="progress" style="height: 20px;">
                        <div class="progress-bar bg-success" style="width: ${performer.attendance_rate}%">
                            ${performer.attendance_rate}%
                        </div>
                    </div>
                </td>
                <td>
                    <div class="progress" style="height: 20px;">
                        <div class="progress-bar bg-primary" style="width: ${performer.punctuality_rate}%">
                            ${performer.punctuality_rate}%
                        </div>
                    </div>
                </td>
                <td>
                    <span class="badge bg-${this.getProductivityBadgeClass(performer.avg_productivity_score)}">
                        ${performer.avg_productivity_score || 'N/A'}
                    </span>
                </td>
            </tr>
        `).join('');
    }
    
    renderQuickStats() {
        const overview = this.analyticsData.overview || {};
        const productivity = this.analyticsData.productivity_insights || [];
        
        document.getElementById('totalWorkingDays').textContent = 
            Math.round((overview.total_records || 0) / (overview.unique_workers || 1));
        
        document.getElementById('totalOvertimeHours').textContent = 
            Math.round((overview.total_overtime_minutes || 0) / 60) + ' hrs';
        
        document.getElementById('avgDailyAttendance').textContent = 
            Math.round((overview.present_count + overview.late_count) / 30) || 0;
        
        // Find most productive day
        if (productivity.length > 0) {
            const mostProductive = productivity.reduce((max, day) => 
                day.avg_productivity > max.avg_productivity ? day : max
            );
            document.getElementById('mostProductiveDay').textContent = mostProductive.day_name;
        }
        
        document.getElementById('peakAttendanceTime').textContent = '9:00 AM'; // Simplified
    }
    
    populateWorkerSelects() {
        const workers = this.analyticsData.worker_performance || [];
        const selects = [
            document.getElementById('heatmapWorker'),
            document.getElementById('exportWorker')
        ];
        
        selects.forEach(select => {
            if (select) {
                const currentValue = select.value;
                select.innerHTML = '<option value="">All Workers</option>' +
                    workers.map(worker => 
                        `<option value="${worker.id}">${worker.name} (${worker.employee_id})</option>`
                    ).join('');
                select.value = currentValue;
            }
        });
    }
    
    getProductivityBadgeClass(score) {
        if (!score) return 'secondary';
        if (score >= 4.5) return 'success';
        if (score >= 3.5) return 'primary';
        if (score >= 2.5) return 'warning';
        return 'danger';
    }
    
    async updateTrendChart(period) {
        try {
            const startDate = document.getElementById('analyticsStartDate').value;
            const endDate = document.getElementById('analyticsEndDate').value;
            
            const params = new URLSearchParams({
                start_date: startDate,
                end_date: endDate,
                period: period
            });
            
            const response = await fetch(`${this.apiBaseUrl}/analytics.php/trends?${params}`);
            const data = await response.json();
            
            if (data.success) {
                this.analyticsData.trends = data.data.trends;
                this.renderTrendChart();
            }
        } catch (error) {
            console.error('Failed to update trend chart:', error);
        }
    }
    
    async updateHeatmap() {
        // Heatmap update logic would go here
        console.log('Updating heatmap...');
    }
    
    updateTopPerformers() {
        // Re-sort and render based on selected metric
        const metric = document.getElementById('performanceMetric').value;
        if (this.analyticsData.worker_performance) {
            this.analyticsData.worker_performance.sort((a, b) => (b[metric] || 0) - (a[metric] || 0));
            this.renderTopPerformersTable();
        }
    }
    
    async refreshData() {
        try {
            const refreshBtn = document.getElementById('refreshAnalytics');
            const originalContent = refreshBtn.innerHTML;
            
            refreshBtn.innerHTML = '<i class="bi bi-arrow-clockwise spin"></i> Refreshing...';
            refreshBtn.disabled = true;
            
            await this.loadAnalyticsData();
            this.renderAllCharts();
            
            this.showSuccess('Analytics refreshed successfully');
        } catch (error) {
            console.error('Failed to refresh analytics:', error);
            this.showError('Failed to refresh analytics');
        } finally {
            const refreshBtn = document.getElementById('refreshAnalytics');
            refreshBtn.innerHTML = '<i class="bi bi-arrow-clockwise"></i> Refresh';
            refreshBtn.disabled = false;
        }
    }
    
    showError(message) {
        this.showNotification(message, 'danger');
    }
    
    showSuccess(message) {
        this.showNotification(message, 'success');
    }
    
    showNotification(message, type = 'info') {
        const notificationDiv = document.createElement('div');
        notificationDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        notificationDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; max-width: 400px;';
        notificationDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(notificationDiv);
        
        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (notificationDiv.parentNode) {
                notificationDiv.parentNode.removeChild(notificationDiv);
            }
        }, 5000);
    }
}

// Global functions
function exportAnalytics() {
    const modal = new bootstrap.Modal(document.getElementById('exportModal'));
    modal.show();
}

function generateExport() {
    const reportType = document.getElementById('exportReportType').value;
    const format = document.getElementById('exportFormat').value;
    const workerId = document.getElementById('exportWorker').value;
    const includeCharts = document.getElementById('includeCharts').checked;
    
    const startDate = document.getElementById('analyticsStartDate').value;
    const endDate = document.getElementById('analyticsEndDate').value;
    
    const params = new URLSearchParams({
        start_date: startDate,
        end_date: endDate,
        format: format,
        include_charts: includeCharts
    });
    
    if (workerId) params.append('worker_id', workerId);
    
    let url;
    switch (reportType) {
        case 'summary':
            url = `/MtcInvoiceNewProject/api/attendance/reports.php/summary?${params}`;
            break;
        case 'department':
            url = `/MtcInvoiceNewProject/api/attendance/reports.php/department/all?${params}`;
            break;
        case 'worker':
            if (!workerId) {
                window.analyticsCharts.showError('Please select a worker for individual report');
                return;
            }
            url = `/MtcInvoiceNewProject/api/attendance/reports.php/worker/${workerId}?${params}`;
            break;
        default:
            url = `/MtcInvoiceNewProject/api/attendance/reports.php/export?${params}`;
    }
    
    window.open(url, '_blank');
    
    // Close modal
    bootstrap.Modal.getInstance(document.getElementById('exportModal')).hide();
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    window.analyticsCharts = new AttendanceAnalyticsCharts();
});
