<?php
// Enable error reporting for debugging
ini_set('display_errors', 1);
error_reporting(E_ALL);
header('Content-Type: application/json; charset=utf-8');

// Function to send JSON response and exit
function sendJsonResponse($success, $message = '', $data = []) {
    $response = ['success' => $success];
    if ($message) $response['message'] = $message;
    if (!empty($data)) $response = array_merge($response, $data);
    
    header('Content-Type: application/json');
    echo json_encode($response, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    exit;
}

try {
    // Include the configuration file with database connection
    $configFile = __DIR__ . '/../../admin/config/config.php';
    if (!file_exists($configFile)) {
        throw new Exception('Configuration file not found: ' . $configFile);
    }
    require_once $configFile;
    
    // Check if user is logged in
    if (!function_exists('requireLogin')) {
        throw new Exception('Login functions not available');
    }
    requireLogin();
    
    // Get database connection
    $pdo = getDbConnection();
    if (!($pdo instanceof PDO)) {
        throw new Exception('Failed to establish database connection');
    }
    // Check if the request is a POST request
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        http_response_code(405);
        sendJsonResponse(false, 'Method not allowed');
    }

    // Get the raw POST data
    $json = file_get_contents('php://input');
    if ($json === false) {
        throw new Exception('Failed to read input data');
    }

    $data = json_decode($json, true);
    if (json_last_error() !== JSON_ERROR_NONE) {
        throw new Exception('Invalid JSON data: ' . json_last_error_msg());
    }

    if (!isset($data['pdfData']) || !isset($data['fileName'])) {
        throw new Exception('Missing required parameters');
    }

        // Validate request data
    if (!isset($data['pdfData']) || empty($data['pdfData'])) {
        throw new Exception('No PDF data received');
    }
    
    // Process the PDF data
    $pdfData = $data['pdfData'];
    
    // Debug log the first 50 characters of the PDF data
    error_log('Received PDF data (first 50 chars): ' . substr($pdfData, 0, 50));
    
    // Debug: Log the first 100 characters of the PDF data
    error_log('PDF Data (first 100 chars): ' . substr($pdfData, 0, 100));
    
    // Handle data URL format (if it's a data URL)
    if (strpos($pdfData, 'data:application/pdf;base64,') === 0) {
        $pdfData = substr($pdfData, strpos($pdfData, ',') + 1);
    } elseif (strpos($pdfData, 'base64,') !== false) {
        $pdfData = substr($pdfData, strpos($pdfData, ',') + 1);
    }
    
    // Clean the base64 string
    $pdfData = str_replace(' ', '+', $pdfData);
    $pdfData = str_replace('\n', '', $pdfData);
    $pdfData = str_replace('\r', '', $pdfData);
    
    // Validate base64
    if (!preg_match('/^[a-zA-Z0-9\/\r\n+]*={0,2}$/', $pdfData)) {
        throw new Exception('Invalid base64 PDF data');
    }
    
    // Decode the base64 data
    $fileData = base64_decode($pdfData, true);
    
    if ($fileData === false) {
        $error = 'Invalid base64 data';
        $lastError = 'Base64 decoding failed';
        
        // Simple base64 validation
        if (preg_match('/^[a-zA-Z0-9\/\r\n+]*={0,2}$/', $pdfData)) {
            $lastError = 'Invalid base64 characters in input';
        }
        
        error_log('Base64 decode error: ' . $lastError);
        error_log('Input length: ' . strlen($pdfData));
        error_log('First 50 chars: ' . substr($pdfData, 0, 50));
        
        throw new Exception('Failed to decode base64 PDF data. ' . $lastError);
    }
    
    // Verify we have valid PDF data
    if (strpos($fileData, '%PDF-') !== 0) {
        error_log('Invalid PDF header. First 20 bytes: ' . bin2hex(substr($fileData, 0, 20)));
        throw new Exception('Invalid PDF data format. Expected PDF header not found.');
    }
    
    // Get file information
    $originalFileName = $data['fileName'] ?? 'invoice.pdf';
    $cleanFileName = preg_replace('/[^a-zA-Z0-9_.-]/', '_', $originalFileName);
    $uniqueId = uniqid('', true);
    $fileName = 'invoice_' . date('Y-m-d') . '_' . $uniqueId . '.pdf';
    
    // Define paths
    $baseDir = dirname(__DIR__, 2); // Go up two levels from admin/invoices to project root
    $uploadDir = $baseDir . '/api/uploads/';
    $pdfDir = $uploadDir . 'pdfs/';
    $relativePath = 'pdfs/' . $fileName;
    $filePath = $pdfDir . $fileName;
    
    // Calculate file size
    $fileSizeKB = round(strlen($fileData) / 1024, 2);
    $uploadedBy = $_SESSION['user_email'] ?? 'system';
    
    // Save to database
    $pdo->beginTransaction();
    
    try {
        $stmt = $pdo->prepare("
            INSERT INTO documents (
                file_name, 
                file_type, 
                file_size_kb, 
                uploaded_by, 
                upload_date,
                file_path,
                status
            ) VALUES (?, ?, ?, ?, NOW(), ?, 'Available')
        ");
        
        $stmt->execute([
            $cleanFileName,  // Clean original filename
            'application/pdf',
            $fileSizeKB,
            $uploadedBy,
            $relativePath  // Store relative path only (pdfs/filename.pdf)
        ]);
        
        $documentId = $pdo->lastInsertId();
        
        // Create directories if they don't exist
        if (!file_exists($pdfDir)) {
            if (!mkdir($pdfDir, 0755, true)) {
                throw new Exception('Failed to create PDF directory: ' . $pdfDir);
            }
        }
        
        // Save the file
        $bytesWritten = file_put_contents($filePath, $fileData);
        if ($bytesWritten === false) {
            $error = error_get_last();
            throw new Exception('Failed to save PDF file: ' . ($error['message'] ?? 'Unknown error'));
        }
        
        // Log successful save
        error_log(sprintf(
            'PDF saved successfully - ID: %d, Name: %s, Size: %s KB, Path: %s, By: %s',
            $documentId,
            $cleanFileName,
            $fileSizeKB,
            $relativePath,
            $uploadedBy
        ));
        
        $pdo->commit();
        
        // Return success response with relative path
        sendJsonResponse(true, 'PDF saved successfully', [
            'document_id' => $documentId,
            'file_name' => $cleanFileName,
            'file_size_kb' => $fileSizeKB,
            'file_path' => $relativePath,
            'file_url' => '/api/uploads/' . $relativePath
        ]);
        

        
    } catch (Exception $e) {
        $pdo->rollBack();
        throw $e; // Re-throw to be caught by the outer try-catch
    }
    
} catch (Exception $e) {
    http_response_code(500);
    $errorDetails = [
        'success' => false,
        'message' => 'Error saving PDF: ' . $e->getMessage(),
        'error' => [
            'message' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ]
    ];
    
    // Log the full error
    error_log('PDF Save Error: ' . $e->getMessage() . ' in ' . $e->getFile() . ' on line ' . $e->getLine());
    
    // Send JSON response
    sendJsonResponse(false, 'Error saving PDF: ' . $e->getMessage());
}
