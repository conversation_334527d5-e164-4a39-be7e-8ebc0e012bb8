<?php
/**
 * Analytics API Endpoint
 * 
 * @package MtcInvoice Attendance
 * @version 1.0
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once '../config/config.php';
require_once '../../classes/AnalyticsEngine.php';

// Get request method and path
$method = $_SERVER['REQUEST_METHOD'];
$path = $_SERVER['REQUEST_URI'];
$segments = explode('/', trim(parse_url($path, PHP_URL_PATH), '/'));

// Remove 'api/attendance/analytics.php' from segments
$segments = array_slice($segments, 3);

/**
 * Send JSON response
 */
function sendJsonResponse($status, $data = null, $message = null) {
    http_response_code($status);
    echo json_encode([
        'success' => $status >= 200 && $status < 300,
        'data' => $data,
        'message' => $message,
        'timestamp' => date('Y-m-d H:i:s')
    ]);
    exit();
}

// Initialize AnalyticsEngine
try {
    $analyticsEngine = new AnalyticsEngine();
} catch (Exception $e) {
    sendJsonResponse(500, null, 'Service initialization failed');
}

// Handle the request based on method and endpoint
try {
    if ($method !== 'GET') {
        sendJsonResponse(405, null, 'Method not allowed');
    }
    
    if (isset($segments[0])) {
        switch ($segments[0]) {
            case 'overview':
                getAttendanceOverview();
                break;
                
            case 'trends':
                getAttendanceTrends();
                break;
                
            case 'performance':
                getWorkerPerformance();
                break;
                
            case 'departments':
                getDepartmentAnalytics();
                break;
                
            case 'heatmap':
                getAttendanceHeatmap();
                break;
                
            case 'emoji':
                getEmojiAnalytics();
                break;
                
            case 'productivity':
                getProductivityInsights();
                break;
                
            case 'dashboard':
                getDashboardData();
                break;
                
            default:
                sendJsonResponse(404, null, 'Analytics endpoint not found');
        }
    } else {
        // Default: return overview
        getAttendanceOverview();
    }
    
} catch (Exception $e) {
    error_log("Analytics API error: " . $e->getMessage());
    sendJsonResponse(500, null, 'Internal server error');
}

/**
 * Get attendance overview statistics
 */
function getAttendanceOverview() {
    global $analyticsEngine;
    
    try {
        $filters = [];
        
        if (isset($_GET['start_date']) && !empty($_GET['start_date'])) {
            $filters['start_date'] = $_GET['start_date'];
        }
        
        if (isset($_GET['end_date']) && !empty($_GET['end_date'])) {
            $filters['end_date'] = $_GET['end_date'];
        }
        
        if (isset($_GET['department']) && !empty($_GET['department'])) {
            $filters['department'] = $_GET['department'];
        }
        
        $overview = $analyticsEngine->getAttendanceOverview($filters);
        sendJsonResponse(200, $overview);
        
    } catch (Exception $e) {
        error_log("Get attendance overview error: " . $e->getMessage());
        sendJsonResponse(500, null, 'Failed to retrieve attendance overview');
    }
}

/**
 * Get attendance trends
 */
function getAttendanceTrends() {
    global $analyticsEngine;
    
    try {
        $period = $_GET['period'] ?? 'daily'; // daily, weekly, monthly
        $filters = [];
        
        if (isset($_GET['start_date']) && !empty($_GET['start_date'])) {
            $filters['start_date'] = $_GET['start_date'];
        }
        
        if (isset($_GET['end_date']) && !empty($_GET['end_date'])) {
            $filters['end_date'] = $_GET['end_date'];
        }
        
        if (isset($_GET['department']) && !empty($_GET['department'])) {
            $filters['department'] = $_GET['department'];
        }
        
        $trends = $analyticsEngine->getAttendanceTrends($period, $filters);
        sendJsonResponse(200, [
            'period' => $period,
            'trends' => $trends
        ]);
        
    } catch (Exception $e) {
        error_log("Get attendance trends error: " . $e->getMessage());
        sendJsonResponse(500, null, 'Failed to retrieve attendance trends');
    }
}

/**
 * Get worker performance analytics
 */
function getWorkerPerformance() {
    global $analyticsEngine;
    
    try {
        $workerId = isset($_GET['worker_id']) && !empty($_GET['worker_id']) ? $_GET['worker_id'] : null;
        $filters = [];
        
        if (isset($_GET['start_date']) && !empty($_GET['start_date'])) {
            $filters['start_date'] = $_GET['start_date'];
        }
        
        if (isset($_GET['end_date']) && !empty($_GET['end_date'])) {
            $filters['end_date'] = $_GET['end_date'];
        }
        
        $performance = $analyticsEngine->getWorkerPerformance($workerId, $filters);
        sendJsonResponse(200, $performance);
        
    } catch (Exception $e) {
        error_log("Get worker performance error: " . $e->getMessage());
        sendJsonResponse(500, null, 'Failed to retrieve worker performance');
    }
}

/**
 * Get department analytics
 */
function getDepartmentAnalytics() {
    global $analyticsEngine;
    
    try {
        $filters = [];
        
        if (isset($_GET['start_date']) && !empty($_GET['start_date'])) {
            $filters['start_date'] = $_GET['start_date'];
        }
        
        if (isset($_GET['end_date']) && !empty($_GET['end_date'])) {
            $filters['end_date'] = $_GET['end_date'];
        }
        
        $analytics = $analyticsEngine->getDepartmentAnalytics($filters);
        sendJsonResponse(200, $analytics);
        
    } catch (Exception $e) {
        error_log("Get department analytics error: " . $e->getMessage());
        sendJsonResponse(500, null, 'Failed to retrieve department analytics');
    }
}

/**
 * Get attendance heatmap data
 */
function getAttendanceHeatmap() {
    global $analyticsEngine;
    
    try {
        $workerId = isset($_GET['worker_id']) && !empty($_GET['worker_id']) ? $_GET['worker_id'] : null;
        $filters = [];
        
        if (isset($_GET['start_date']) && !empty($_GET['start_date'])) {
            $filters['start_date'] = $_GET['start_date'];
        }
        
        if (isset($_GET['end_date']) && !empty($_GET['end_date'])) {
            $filters['end_date'] = $_GET['end_date'];
        }
        
        $heatmap = $analyticsEngine->getAttendanceHeatmap($workerId, $filters);
        sendJsonResponse(200, $heatmap);
        
    } catch (Exception $e) {
        error_log("Get attendance heatmap error: " . $e->getMessage());
        sendJsonResponse(500, null, 'Failed to retrieve attendance heatmap');
    }
}

/**
 * Get emoji usage analytics
 */
function getEmojiAnalytics() {
    global $analyticsEngine;
    
    try {
        $filters = [];
        
        if (isset($_GET['start_date']) && !empty($_GET['start_date'])) {
            $filters['start_date'] = $_GET['start_date'];
        }
        
        if (isset($_GET['end_date']) && !empty($_GET['end_date'])) {
            $filters['end_date'] = $_GET['end_date'];
        }
        
        if (isset($_GET['department']) && !empty($_GET['department'])) {
            $filters['department'] = $_GET['department'];
        }
        
        $emojiStats = $analyticsEngine->getEmojiAnalytics($filters);
        sendJsonResponse(200, $emojiStats);
        
    } catch (Exception $e) {
        error_log("Get emoji analytics error: " . $e->getMessage());
        sendJsonResponse(500, null, 'Failed to retrieve emoji analytics');
    }
}

/**
 * Get productivity insights
 */
function getProductivityInsights() {
    global $analyticsEngine;
    
    try {
        $filters = [];
        
        if (isset($_GET['start_date']) && !empty($_GET['start_date'])) {
            $filters['start_date'] = $_GET['start_date'];
        }
        
        if (isset($_GET['end_date']) && !empty($_GET['end_date'])) {
            $filters['end_date'] = $_GET['end_date'];
        }
        
        if (isset($_GET['department']) && !empty($_GET['department'])) {
            $filters['department'] = $_GET['department'];
        }
        
        $insights = $analyticsEngine->getProductivityInsights($filters);
        sendJsonResponse(200, $insights);
        
    } catch (Exception $e) {
        error_log("Get productivity insights error: " . $e->getMessage());
        sendJsonResponse(500, null, 'Failed to retrieve productivity insights');
    }
}

/**
 * Get comprehensive dashboard data
 */
function getDashboardData() {
    global $analyticsEngine;
    
    try {
        $filters = [];
        
        if (isset($_GET['start_date']) && !empty($_GET['start_date'])) {
            $filters['start_date'] = $_GET['start_date'];
        }
        
        if (isset($_GET['end_date']) && !empty($_GET['end_date'])) {
            $filters['end_date'] = $_GET['end_date'];
        }
        
        if (isset($_GET['department']) && !empty($_GET['department'])) {
            $filters['department'] = $_GET['department'];
        }
        
        // Get all dashboard data in one response
        $dashboardData = [
            'overview' => $analyticsEngine->getAttendanceOverview($filters),
            'trends' => $analyticsEngine->getAttendanceTrends('daily', $filters),
            'departments' => $analyticsEngine->getDepartmentAnalytics($filters),
            'top_performers' => array_slice($analyticsEngine->getWorkerPerformance(null, $filters), 0, 5),
            'emoji_stats' => array_slice($analyticsEngine->getEmojiAnalytics($filters), 0, 10),
            'productivity_by_day' => $analyticsEngine->getProductivityInsights($filters)
        ];
        
        sendJsonResponse(200, $dashboardData);
        
    } catch (Exception $e) {
        error_log("Get dashboard data error: " . $e->getMessage());
        sendJsonResponse(500, null, 'Failed to retrieve dashboard data');
    }
}
