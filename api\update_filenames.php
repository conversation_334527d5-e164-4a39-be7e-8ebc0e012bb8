<?php
require_once __DIR__ . '/config/config.php';

// Get all files in the uploads directory
$uploadDir = __DIR__ . '/uploads/pdfs/';
$files = scandir($uploadDir);

$db = new Database();
$conn = $db->getConnection();

if (!$conn) {
    die("Database connection failed");
}

// Get all documents from the database
$stmt = $conn->query("SELECT id, file_name FROM documents");
$dbFiles = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "Starting filename update...\n";
$updated = 0;

foreach ($dbFiles as $dbFile) {
    $originalName = $dbFile['file_name'];
    $found = false;
    
    // Try to find a matching file in the uploads directory
    foreach ($files as $file) {
        if ($file === '.' || $file === '..') continue;
        
        // Check if this is the same file (with or without timestamp)
        if (strpos($file, pathinfo($originalName, PATHINFO_FILENAME)) === 0) {
            // Found a match, update the database
            $updateStmt = $conn->prepare("UPDATE documents SET file_name = ? WHERE id = ?");
            $updateStmt->execute([$file, $dbFile['id']]);
            
            echo "Updated ID {$dbFile['id']}: {$originalName} -> {$file}\n";
            $updated++;
            $found = true;
            break;
        }
    }
    
    if (!$found) {
        echo "No match found for: {$originalName}\n";
    }
}

echo "\nUpdate complete. Updated {$updated} records.\n";
?>
