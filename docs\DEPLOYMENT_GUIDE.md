# Attendance Management System - Deployment Guide

## Overview

This guide provides step-by-step instructions for deploying the Attendance Management System in production environments. It covers server setup, security configuration, performance optimization, and maintenance procedures.

## System Requirements

### Minimum Requirements
- **CPU**: 2 cores, 2.4 GHz
- **RAM**: 4 GB
- **Storage**: 20 GB SSD
- **Network**: 100 Mbps connection
- **OS**: Ubuntu 20.04 LTS, CentOS 8, or Windows Server 2019

### Recommended Requirements
- **CPU**: 4 cores, 3.0 GHz
- **RAM**: 8 GB
- **Storage**: 50 GB SSD
- **Network**: 1 Gbps connection
- **Load Balancer**: For high availability

### Software Requirements
- **Web Server**: Apache 2.4+ or Nginx 1.18+
- **PHP**: 7.4 or 8.0+
- **Database**: MySQL 8.0+ or MariaDB 10.5+
- **SSL Certificate**: Required for production
- **Backup Solution**: Automated backup system

## Pre-Deployment Checklist

### Security Preparation
- [ ] SSL certificate obtained and configured
- [ ] Firewall rules configured
- [ ] Database credentials secured
- [ ] File permissions set correctly
- [ ] Security headers configured
- [ ] Rate limiting implemented

### Performance Preparation
- [ ] Database indexes optimized
- [ ] Caching solution configured
- [ ] CDN setup for static assets
- [ ] Image optimization enabled
- [ ] Gzip compression enabled
- [ ] Database connection pooling configured

### Monitoring Preparation
- [ ] Log aggregation system setup
- [ ] Performance monitoring tools installed
- [ ] Uptime monitoring configured
- [ ] Error tracking system enabled
- [ ] Backup verification system ready

## Server Setup

### Ubuntu/Debian Installation

#### 1. Update System
```bash
sudo apt update && sudo apt upgrade -y
```

#### 2. Install Required Packages
```bash
# Install web server and PHP
sudo apt install apache2 php8.0 php8.0-mysql php8.0-curl php8.0-json php8.0-mbstring php8.0-xml php8.0-zip php8.0-gd -y

# Install MySQL
sudo apt install mysql-server -y

# Install additional tools
sudo apt install git unzip curl wget htop -y
```

#### 3. Configure Apache
```bash
# Enable required modules
sudo a2enmod rewrite ssl headers

# Create virtual host
sudo nano /etc/apache2/sites-available/attendance.conf
```

**Virtual Host Configuration:**
```apache
<VirtualHost *:80>
    ServerName your-domain.com
    DocumentRoot /var/www/attendance
    
    # Redirect to HTTPS
    Redirect permanent / https://your-domain.com/
</VirtualHost>

<VirtualHost *:443>
    ServerName your-domain.com
    DocumentRoot /var/www/attendance
    
    # SSL Configuration
    SSLEngine on
    SSLCertificateFile /path/to/certificate.crt
    SSLCertificateKeyFile /path/to/private.key
    SSLCertificateChainFile /path/to/chain.crt
    
    # Security Headers
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains"
    Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' cdn.jsdelivr.net; style-src 'self' 'unsafe-inline' cdn.jsdelivr.net; img-src 'self' data:; font-src 'self' cdn.jsdelivr.net"
    
    # Directory Configuration
    <Directory /var/www/attendance>
        Options -Indexes +FollowSymLinks
        AllowOverride All
        Require all granted
    </Directory>
    
    # API Configuration
    <Directory /var/www/attendance/api>
        Options -Indexes
        AllowOverride All
        Require all granted
    </Directory>
    
    # Uploads Security
    <Directory /var/www/attendance/uploads>
        Options -Indexes -ExecCGI
        AllowOverride None
        Require all granted
        
        # Prevent PHP execution
        <Files "*.php">
            Require all denied
        </Files>
    </Directory>
    
    # Error and Access Logs
    ErrorLog ${APACHE_LOG_DIR}/attendance_error.log
    CustomLog ${APACHE_LOG_DIR}/attendance_access.log combined
</VirtualHost>
```

#### 4. Configure PHP
```bash
sudo nano /etc/php/8.0/apache2/php.ini
```

**Key PHP Settings:**
```ini
; Security
expose_php = Off
allow_url_fopen = Off
allow_url_include = Off

; Performance
memory_limit = 256M
max_execution_time = 300
max_input_time = 300
post_max_size = 50M
upload_max_filesize = 20M

; Error Handling
display_errors = Off
log_errors = On
error_log = /var/log/php/error.log

; Session Security
session.cookie_httponly = 1
session.cookie_secure = 1
session.use_strict_mode = 1

; Timezone
date.timezone = UTC
```

### Database Setup

#### 1. Secure MySQL Installation
```bash
sudo mysql_secure_installation
```

#### 2. Create Database and User
```sql
-- Connect to MySQL
sudo mysql -u root -p

-- Create database
CREATE DATABASE mtc_attendance CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Create user
CREATE USER 'attendance_user'@'localhost' IDENTIFIED BY 'secure_password_here';

-- Grant permissions
GRANT SELECT, INSERT, UPDATE, DELETE, CREATE, ALTER, INDEX ON mtc_attendance.* TO 'attendance_user'@'localhost';

-- Flush privileges
FLUSH PRIVILEGES;

-- Exit MySQL
EXIT;
```

#### 3. Import Database Schema
```bash
mysql -u attendance_user -p mtc_attendance < database/attendance_schema.sql
```

#### 4. Configure MySQL for Performance
```bash
sudo nano /etc/mysql/mysql.conf.d/mysqld.cnf
```

**MySQL Configuration:**
```ini
[mysqld]
# Performance
innodb_buffer_pool_size = 2G
innodb_log_file_size = 256M
innodb_flush_log_at_trx_commit = 2
query_cache_type = 1
query_cache_size = 128M

# Security
bind-address = 127.0.0.1
skip-networking = 0
local-infile = 0

# Logging
slow_query_log = 1
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 2
log_queries_not_using_indexes = 1
```

## Application Deployment

### 1. Download and Extract
```bash
# Create directory
sudo mkdir -p /var/www/attendance

# Clone or extract application files
cd /var/www/attendance
sudo git clone https://github.com/your-repo/attendance-system.git .

# Or extract from archive
sudo tar -xzf attendance-system.tar.gz -C /var/www/attendance --strip-components=1
```

### 2. Set File Permissions
```bash
# Set ownership
sudo chown -R www-data:www-data /var/www/attendance

# Set directory permissions
sudo find /var/www/attendance -type d -exec chmod 755 {} \;

# Set file permissions
sudo find /var/www/attendance -type f -exec chmod 644 {} \;

# Set executable permissions for scripts
sudo chmod +x /var/www/attendance/scripts/*.sh

# Set writable permissions for uploads
sudo chmod 775 /var/www/attendance/uploads
sudo chmod 775 /var/www/attendance/uploads/profiles
sudo chmod 775 /var/www/attendance/uploads/reports
```

### 3. Configure Application
```bash
# Copy configuration template
sudo cp api/config/config.example.php api/config/config.php

# Edit configuration
sudo nano api/config/config.php
```

**Configuration Settings:**
```php
<?php
// Database Configuration
define('DB_HOST', 'localhost');
define('DB_NAME', 'mtc_attendance');
define('DB_USER', 'attendance_user');
define('DB_PASS', 'secure_password_here');
define('DB_CHARSET', 'utf8mb4');

// Application Settings
define('APP_URL', 'https://your-domain.com');
define('APP_NAME', 'Attendance Management System');
define('APP_VERSION', '1.0.0');

// Security Settings
define('SECRET_KEY', 'generate-random-32-character-key');
define('ENCRYPTION_KEY', 'generate-random-encryption-key');
define('SESSION_LIFETIME', 3600); // 1 hour

// File Upload Settings
define('UPLOAD_MAX_SIZE', 20971520); // 20MB
define('ALLOWED_EXTENSIONS', 'jpg,jpeg,png,gif,pdf,doc,docx,csv,xlsx');

// Email Configuration
define('SMTP_HOST', 'smtp.your-provider.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'your-email-password');
define('SMTP_ENCRYPTION', 'tls');

// Cache Configuration
define('CACHE_ENABLED', true);
define('CACHE_DRIVER', 'redis'); // redis, memcached, file
define('REDIS_HOST', '127.0.0.1');
define('REDIS_PORT', 6379);

// Debug Settings (DISABLE IN PRODUCTION)
define('DEBUG_MODE', false);
define('LOG_LEVEL', 'ERROR');
define('DISPLAY_ERRORS', false);
?>
```

### 4. Install Dependencies
```bash
# Install Composer (if not already installed)
curl -sS https://getcomposer.org/installer | php
sudo mv composer.phar /usr/local/bin/composer

# Install PHP dependencies
cd /var/www/attendance
sudo -u www-data composer install --no-dev --optimize-autoloader
```

### 5. Set Up Caching (Redis)
```bash
# Install Redis
sudo apt install redis-server -y

# Configure Redis
sudo nano /etc/redis/redis.conf
```

**Redis Configuration:**
```
# Security
requirepass your-redis-password
bind 127.0.0.1

# Performance
maxmemory 512mb
maxmemory-policy allkeys-lru

# Persistence
save 900 1
save 300 10
save 60 10000
```

## SSL Certificate Setup

### Using Let's Encrypt (Recommended)
```bash
# Install Certbot
sudo apt install certbot python3-certbot-apache -y

# Obtain certificate
sudo certbot --apache -d your-domain.com

# Test auto-renewal
sudo certbot renew --dry-run
```

### Using Custom Certificate
```bash
# Copy certificate files
sudo cp your-certificate.crt /etc/ssl/certs/
sudo cp your-private.key /etc/ssl/private/
sudo cp your-chain.crt /etc/ssl/certs/

# Set permissions
sudo chmod 644 /etc/ssl/certs/your-certificate.crt
sudo chmod 600 /etc/ssl/private/your-private.key
sudo chmod 644 /etc/ssl/certs/your-chain.crt
```

## Security Hardening

### 1. Firewall Configuration
```bash
# Install UFW
sudo apt install ufw -y

# Default policies
sudo ufw default deny incoming
sudo ufw default allow outgoing

# Allow SSH
sudo ufw allow ssh

# Allow HTTP and HTTPS
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp

# Allow MySQL (only from localhost)
sudo ufw allow from 127.0.0.1 to any port 3306

# Enable firewall
sudo ufw enable
```

### 2. Fail2Ban Setup
```bash
# Install Fail2Ban
sudo apt install fail2ban -y

# Configure Fail2Ban
sudo nano /etc/fail2ban/jail.local
```

**Fail2Ban Configuration:**
```ini
[DEFAULT]
bantime = 3600
findtime = 600
maxretry = 5

[apache-auth]
enabled = true
port = http,https
filter = apache-auth
logpath = /var/log/apache2/attendance_error.log

[apache-badbots]
enabled = true
port = http,https
filter = apache-badbots
logpath = /var/log/apache2/attendance_access.log

[apache-noscript]
enabled = true
port = http,https
filter = apache-noscript
logpath = /var/log/apache2/attendance_access.log
```

### 3. Additional Security Measures
```bash
# Disable unnecessary services
sudo systemctl disable bluetooth
sudo systemctl disable cups

# Update system packages regularly
sudo apt update && sudo apt upgrade -y

# Install security updates automatically
sudo apt install unattended-upgrades -y
sudo dpkg-reconfigure unattended-upgrades
```

## Performance Optimization

### 1. Enable Gzip Compression
```apache
# Add to Apache configuration
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>
```

### 2. Browser Caching
```apache
# Add to Apache configuration
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/pdf "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType application/x-javascript "access plus 1 month"
    ExpiresByType application/x-shockwave-flash "access plus 1 month"
    ExpiresByType image/x-icon "access plus 1 year"
    ExpiresDefault "access plus 2 days"
</IfModule>
```

### 3. Database Optimization
```sql
-- Create indexes for better performance
CREATE INDEX idx_attendance_worker_date ON attendance_records(worker_id, attendance_date);
CREATE INDEX idx_attendance_date ON attendance_records(attendance_date);
CREATE INDEX idx_attendance_status ON attendance_records(status);
CREATE INDEX idx_workers_department ON workers(department);
CREATE INDEX idx_workers_status ON workers(status);
CREATE INDEX idx_alerts_worker ON attendance_alerts(worker_id);
CREATE INDEX idx_alerts_created ON attendance_alerts(created_at);
```

## Monitoring and Logging

### 1. Log Rotation
```bash
# Configure logrotate
sudo nano /etc/logrotate.d/attendance
```

**Logrotate Configuration:**
```
/var/log/apache2/attendance_*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
    postrotate
        systemctl reload apache2
    endscript
}

/var/www/attendance/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
}
```

### 2. System Monitoring
```bash
# Install monitoring tools
sudo apt install htop iotop nethogs -y

# Install log analysis tools
sudo apt install goaccess -y

# Generate web server statistics
sudo goaccess /var/log/apache2/attendance_access.log -o /var/www/attendance/stats.html --log-format=COMBINED
```

## Backup Strategy

### 1. Database Backup Script
```bash
# Create backup script
sudo nano /usr/local/bin/backup-attendance.sh
```

**Backup Script:**
```bash
#!/bin/bash

# Configuration
DB_NAME="mtc_attendance"
DB_USER="attendance_user"
DB_PASS="secure_password_here"
BACKUP_DIR="/var/backups/attendance"
DATE=$(date +%Y%m%d_%H%M%S)

# Create backup directory
mkdir -p $BACKUP_DIR

# Database backup
mysqldump -u $DB_USER -p$DB_PASS $DB_NAME | gzip > $BACKUP_DIR/db_backup_$DATE.sql.gz

# Files backup
tar -czf $BACKUP_DIR/files_backup_$DATE.tar.gz -C /var/www attendance

# Remove old backups (keep 30 days)
find $BACKUP_DIR -name "*.gz" -mtime +30 -delete

# Log backup completion
echo "$(date): Backup completed successfully" >> /var/log/attendance-backup.log
```

### 2. Schedule Backups
```bash
# Make script executable
sudo chmod +x /usr/local/bin/backup-attendance.sh

# Add to crontab
sudo crontab -e

# Add this line for daily backups at 2 AM
0 2 * * * /usr/local/bin/backup-attendance.sh
```

## Health Checks and Monitoring

### 1. Application Health Check
```bash
# Create health check script
sudo nano /usr/local/bin/health-check.sh
```

**Health Check Script:**
```bash
#!/bin/bash

# Check web server
if ! systemctl is-active --quiet apache2; then
    echo "Apache is down" | mail -s "Alert: Apache Down" <EMAIL>
    systemctl restart apache2
fi

# Check database
if ! systemctl is-active --quiet mysql; then
    echo "MySQL is down" | mail -s "Alert: MySQL Down" <EMAIL>
    systemctl restart mysql
fi

# Check disk space
DISK_USAGE=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 80 ]; then
    echo "Disk usage is at ${DISK_USAGE}%" | mail -s "Alert: High Disk Usage" <EMAIL>
fi

# Check application response
HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" https://your-domain.com/api/attendance/health)
if [ $HTTP_STATUS -ne 200 ]; then
    echo "Application health check failed with status $HTTP_STATUS" | mail -s "Alert: Application Down" <EMAIL>
fi
```

### 2. Schedule Health Checks
```bash
# Make script executable
sudo chmod +x /usr/local/bin/health-check.sh

# Add to crontab for every 5 minutes
*/5 * * * * /usr/local/bin/health-check.sh
```

## Maintenance Procedures

### Regular Maintenance Tasks
1. **Weekly**: Review error logs and performance metrics
2. **Monthly**: Update system packages and security patches
3. **Quarterly**: Review and optimize database performance
4. **Annually**: Security audit and penetration testing

### Update Procedure
1. Create full backup
2. Test updates in staging environment
3. Schedule maintenance window
4. Apply updates during low-traffic period
5. Verify functionality post-update
6. Monitor for issues

### Troubleshooting Common Issues
- **High CPU usage**: Check for slow queries, optimize database
- **Memory issues**: Increase PHP memory limit, optimize caching
- **Disk space**: Clean old logs, optimize file storage
- **Database locks**: Identify and optimize long-running queries

This deployment guide provides a comprehensive foundation for deploying the Attendance Management System in a production environment. Always test thoroughly in a staging environment before deploying to production.
