/**
 * Attendance Dashboard Widget
 * 
 * Interactive dashboard with charts, analytics, and real-time updates
 * for attendance management overview.
 * 
 * @package MtcInvoice Attendance
 * @version 1.0
 */

class AttendanceDashboard {
    constructor(containerId, options = {}) {
        this.container = document.getElementById(containerId);
        this.options = {
            apiBaseUrl: '/MtcInvoiceNewProject/api/attendance',
            autoRefresh: true,
            refreshInterval: 60000, // 1 minute
            chartColors: {
                primary: '#667eea',
                success: '#28a745',
                warning: '#ffc107',
                danger: '#dc3545',
                info: '#17a2b8',
                secondary: '#6c757d'
            },
            ...options
        };
        
        this.charts = {};
        this.dashboardData = null;
        
        this.init();
    }
    
    async init() {
        try {
            this.createDashboardStructure();
            await this.loadDashboardData();
            this.renderDashboard();
            this.setupEventListeners();
            
            if (this.options.autoRefresh) {
                this.startAutoRefresh();
            }
        } catch (error) {
            console.error('Failed to initialize attendance dashboard:', error);
            this.showError('Failed to initialize dashboard');
        }
    }
    
    createDashboardStructure() {
        this.container.innerHTML = `
            <div class="attendance-dashboard">
                <div class="dashboard-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0">Attendance Overview</h4>
                        <div class="dashboard-controls">
                            <select class="form-select form-select-sm me-2" id="dashboardPeriod">
                                <option value="7">Last 7 days</option>
                                <option value="30" selected>Last 30 days</option>
                                <option value="90">Last 90 days</option>
                            </select>
                            <button class="btn btn-sm btn-outline-primary" id="refreshDashboard">
                                <i class="bi bi-arrow-clockwise"></i>
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="dashboard-content">
                    <!-- Key Metrics Row -->
                    <div class="row mb-4">
                        <div class="col-md-3 col-sm-6 mb-3">
                            <div class="metric-card">
                                <div class="metric-icon bg-primary">
                                    <i class="bi bi-people-fill"></i>
                                </div>
                                <div class="metric-content">
                                    <div class="metric-value" id="totalWorkers">-</div>
                                    <div class="metric-label">Total Workers</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-3 col-sm-6 mb-3">
                            <div class="metric-card">
                                <div class="metric-icon bg-success">
                                    <i class="bi bi-check-circle-fill"></i>
                                </div>
                                <div class="metric-content">
                                    <div class="metric-value" id="attendanceRate">-</div>
                                    <div class="metric-label">Attendance Rate</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-3 col-sm-6 mb-3">
                            <div class="metric-card">
                                <div class="metric-icon bg-warning">
                                    <i class="bi bi-clock-fill"></i>
                                </div>
                                <div class="metric-content">
                                    <div class="metric-value" id="punctualityRate">-</div>
                                    <div class="metric-label">Punctuality Rate</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-3 col-sm-6 mb-3">
                            <div class="metric-card">
                                <div class="metric-icon bg-info">
                                    <i class="bi bi-star-fill"></i>
                                </div>
                                <div class="metric-content">
                                    <div class="metric-value" id="avgProductivity">-</div>
                                    <div class="metric-label">Avg Productivity</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Charts Row -->
                    <div class="row mb-4">
                        <div class="col-lg-8 mb-3">
                            <div class="chart-card">
                                <div class="chart-header">
                                    <h6>Attendance Trends</h6>
                                    <div class="chart-controls">
                                        <button class="btn btn-sm btn-outline-secondary trend-btn active" data-period="daily">Daily</button>
                                        <button class="btn btn-sm btn-outline-secondary trend-btn" data-period="weekly">Weekly</button>
                                    </div>
                                </div>
                                <div class="chart-body">
                                    <canvas id="attendanceTrendChart"></canvas>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-lg-4 mb-3">
                            <div class="chart-card">
                                <div class="chart-header">
                                    <h6>Status Distribution</h6>
                                </div>
                                <div class="chart-body">
                                    <canvas id="statusDistributionChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Department Analytics and Alerts Row -->
                    <div class="row mb-4">
                        <div class="col-lg-6 mb-3">
                            <div class="chart-card">
                                <div class="chart-header">
                                    <h6>Department Performance</h6>
                                </div>
                                <div class="chart-body">
                                    <canvas id="departmentChart"></canvas>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-lg-6 mb-3">
                            <div class="alerts-card">
                                <div class="chart-header">
                                    <h6>Recent Alerts</h6>
                                    <a href="#" class="btn btn-sm btn-outline-primary" id="viewAllAlerts">View All</a>
                                </div>
                                <div class="alerts-list" id="recentAlerts">
                                    <!-- Alerts will be populated here -->
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Top Performers and Emoji Stats Row -->
                    <div class="row">
                        <div class="col-lg-6 mb-3">
                            <div class="performers-card">
                                <div class="chart-header">
                                    <h6>Top Performers</h6>
                                </div>
                                <div class="performers-list" id="topPerformers">
                                    <!-- Top performers will be populated here -->
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-lg-6 mb-3">
                            <div class="emoji-stats-card">
                                <div class="chart-header">
                                    <h6>Popular Emoji Tags</h6>
                                </div>
                                <div class="emoji-stats-list" id="emojiStats">
                                    <!-- Emoji stats will be populated here -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
    
    setupEventListeners() {
        // Period selector
        document.getElementById('dashboardPeriod').addEventListener('change', () => {
            this.loadDashboardData().then(() => this.renderDashboard());
        });
        
        // Refresh button
        document.getElementById('refreshDashboard').addEventListener('click', () => {
            this.refresh();
        });
        
        // Trend chart period buttons
        this.container.querySelectorAll('.trend-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                // Update active button
                this.container.querySelectorAll('.trend-btn').forEach(b => b.classList.remove('active'));
                e.target.classList.add('active');
                
                // Update trend chart
                this.updateTrendChart(e.target.dataset.period);
            });
        });
        
        // View all alerts
        document.getElementById('viewAllAlerts').addEventListener('click', (e) => {
            e.preventDefault();
            // Navigate to alerts page or open modal
            window.location.href = '/MtcInvoiceNewProject/admin/attendance/alerts.php';
        });
    }
    
    async loadDashboardData() {
        try {
            const period = document.getElementById('dashboardPeriod')?.value || 30;
            const endDate = new Date().toISOString().split('T')[0];
            const startDate = new Date(Date.now() - period * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
            
            const params = new URLSearchParams({
                start_date: startDate,
                end_date: endDate
            });
            
            const response = await fetch(`${this.options.apiBaseUrl}/analytics.php/dashboard?${params}`);
            const data = await response.json();
            
            if (data.success) {
                this.dashboardData = data.data;
            } else {
                throw new Error(data.message || 'Failed to load dashboard data');
            }
            
        } catch (error) {
            console.error('Failed to load dashboard data:', error);
            this.dashboardData = null;
            throw error;
        }
    }
    
    renderDashboard() {
        if (!this.dashboardData) return;
        
        this.renderMetrics();
        this.renderTrendChart();
        this.renderStatusDistributionChart();
        this.renderDepartmentChart();
        this.renderRecentAlerts();
        this.renderTopPerformers();
        this.renderEmojiStats();
    }
    
    renderMetrics() {
        const overview = this.dashboardData.overview;
        
        document.getElementById('totalWorkers').textContent = overview.unique_workers || 0;
        document.getElementById('attendanceRate').textContent = (overview.attendance_rate || 0) + '%';
        
        // Calculate punctuality rate (present / (present + late))
        const presentCount = overview.present_count || 0;
        const lateCount = overview.late_count || 0;
        const punctualityRate = (presentCount + lateCount) > 0 ? 
            Math.round((presentCount / (presentCount + lateCount)) * 100) : 0;
        document.getElementById('punctualityRate').textContent = punctualityRate + '%';
        
        document.getElementById('avgProductivity').textContent = 
            overview.avg_productivity_score ? overview.avg_productivity_score.toFixed(1) : 'N/A';
    }
    
    renderTrendChart() {
        const ctx = document.getElementById('attendanceTrendChart').getContext('2d');
        const trends = this.dashboardData.trends || [];
        
        // Destroy existing chart
        if (this.charts.trendChart) {
            this.charts.trendChart.destroy();
        }
        
        const labels = trends.map(t => {
            const date = new Date(t.period);
            return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
        });
        
        this.charts.trendChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [
                    {
                        label: 'Present',
                        data: trends.map(t => t.present_count),
                        borderColor: this.options.chartColors.success,
                        backgroundColor: this.options.chartColors.success + '20',
                        tension: 0.4
                    },
                    {
                        label: 'Late',
                        data: trends.map(t => t.late_count),
                        borderColor: this.options.chartColors.warning,
                        backgroundColor: this.options.chartColors.warning + '20',
                        tension: 0.4
                    },
                    {
                        label: 'Absent',
                        data: trends.map(t => t.absent_count),
                        borderColor: this.options.chartColors.danger,
                        backgroundColor: this.options.chartColors.danger + '20',
                        tension: 0.4
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }
    
    renderStatusDistributionChart() {
        const ctx = document.getElementById('statusDistributionChart').getContext('2d');
        const overview = this.dashboardData.overview;
        
        // Destroy existing chart
        if (this.charts.statusChart) {
            this.charts.statusChart.destroy();
        }
        
        const data = [
            overview.present_count || 0,
            overview.late_count || 0,
            overview.absent_count || 0,
            overview.sick_leave_count || 0,
            overview.vacation_count || 0,
            overview.remote_work_count || 0
        ];
        
        const labels = ['Present', 'Late', 'Absent', 'Sick Leave', 'Vacation', 'Remote Work'];
        const colors = [
            this.options.chartColors.success,
            this.options.chartColors.warning,
            this.options.chartColors.danger,
            this.options.chartColors.info,
            this.options.chartColors.primary,
            this.options.chartColors.secondary
        ];
        
        this.charts.statusChart = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: labels,
                datasets: [{
                    data: data,
                    backgroundColor: colors,
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }
    
    renderDepartmentChart() {
        const ctx = document.getElementById('departmentChart').getContext('2d');
        const departments = this.dashboardData.departments || [];
        
        // Destroy existing chart
        if (this.charts.departmentChart) {
            this.charts.departmentChart.destroy();
        }
        
        this.charts.departmentChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: departments.map(d => d.department),
                datasets: [{
                    label: 'Attendance Rate (%)',
                    data: departments.map(d => d.attendance_rate),
                    backgroundColor: this.options.chartColors.primary + '80',
                    borderColor: this.options.chartColors.primary,
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100
                    }
                }
            }
        });
    }
    
    async renderRecentAlerts() {
        try {
            const response = await fetch(`${this.options.apiBaseUrl}/alerts.php?limit=5`);
            const data = await response.json();
            
            const alertsList = document.getElementById('recentAlerts');
            
            if (data.success && data.data.alerts && data.data.alerts.length > 0) {
                alertsList.innerHTML = data.data.alerts.map(alert => `
                    <div class="alert-item severity-${alert.severity}">
                        <div class="alert-icon">
                            <i class="bi bi-exclamation-triangle-fill"></i>
                        </div>
                        <div class="alert-content">
                            <div class="alert-message">${alert.alert_message}</div>
                            <div class="alert-meta">
                                <span class="worker-name">${alert.worker_name}</span>
                                <span class="alert-time">${this.formatTimeAgo(alert.created_at)}</span>
                            </div>
                        </div>
                    </div>
                `).join('');
            } else {
                alertsList.innerHTML = '<div class="no-alerts">No recent alerts</div>';
            }
            
        } catch (error) {
            console.error('Failed to load recent alerts:', error);
            document.getElementById('recentAlerts').innerHTML = '<div class="error">Failed to load alerts</div>';
        }
    }
    
    renderTopPerformers() {
        const performers = this.dashboardData.top_performers || [];
        const performersList = document.getElementById('topPerformers');
        
        if (performers.length > 0) {
            performersList.innerHTML = performers.map((performer, index) => `
                <div class="performer-item">
                    <div class="performer-rank">#${index + 1}</div>
                    <div class="performer-info">
                        <div class="performer-name">${performer.name}</div>
                        <div class="performer-stats">
                            <span class="attendance-rate">${performer.attendance_rate}% attendance</span>
                            <span class="productivity-score">⭐ ${performer.avg_productivity_score || 'N/A'}</span>
                        </div>
                    </div>
                </div>
            `).join('');
        } else {
            performersList.innerHTML = '<div class="no-data">No performance data available</div>';
        }
    }
    
    renderEmojiStats() {
        const emojiStats = this.dashboardData.emoji_stats || [];
        const emojiList = document.getElementById('emojiStats');
        
        if (emojiStats.length > 0) {
            emojiList.innerHTML = emojiStats.slice(0, 8).map(stat => `
                <div class="emoji-stat-item">
                    <div class="emoji-icon">${stat.emoji}</div>
                    <div class="emoji-info">
                        <div class="emoji-count">${stat.total_usage}</div>
                        <div class="emoji-productivity">Avg: ${stat.avg_productivity || 'N/A'}</div>
                    </div>
                </div>
            `).join('');
        } else {
            emojiList.innerHTML = '<div class="no-data">No emoji data available</div>';
        }
    }
    
    updateTrendChart(period) {
        // This would reload trend data for the specified period
        // For now, we'll just re-render with existing data
        this.renderTrendChart();
    }
    
    async refresh() {
        const refreshBtn = document.getElementById('refreshDashboard');
        const originalContent = refreshBtn.innerHTML;
        
        refreshBtn.innerHTML = '<i class="bi bi-arrow-clockwise spin"></i>';
        refreshBtn.disabled = true;
        
        try {
            await this.loadDashboardData();
            this.renderDashboard();
        } catch (error) {
            this.showError('Failed to refresh dashboard');
        } finally {
            refreshBtn.innerHTML = originalContent;
            refreshBtn.disabled = false;
        }
    }
    
    formatTimeAgo(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diffInSeconds = Math.floor((now - date) / 1000);
        
        if (diffInSeconds < 60) return 'Just now';
        if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
        if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
        return `${Math.floor(diffInSeconds / 86400)}d ago`;
    }
    
    showError(message) {
        // Create a simple error notification
        const errorDiv = document.createElement('div');
        errorDiv.className = 'alert alert-danger alert-dismissible fade show position-fixed';
        errorDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; max-width: 400px;';
        errorDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(errorDiv);
        
        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (errorDiv.parentNode) {
                errorDiv.parentNode.removeChild(errorDiv);
            }
        }, 5000);
    }
    
    startAutoRefresh() {
        setInterval(() => {
            this.loadDashboardData().then(() => this.renderDashboard());
        }, this.options.refreshInterval);
    }
}

// Initialize dashboard when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    // Auto-initialize if container exists
    const dashboardContainer = document.getElementById('attendanceDashboard');
    if (dashboardContainer) {
        // Ensure Chart.js is loaded
        if (typeof Chart !== 'undefined') {
            window.attendanceDashboard = new AttendanceDashboard('attendanceDashboard');
        } else {
            console.error('Chart.js is required for the attendance dashboard');
        }
    }
});
