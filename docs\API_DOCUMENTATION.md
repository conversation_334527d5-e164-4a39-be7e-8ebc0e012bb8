# Attendance Management System - API Documentation

## Overview

The Attendance Management System provides a comprehensive RESTful API for managing worker attendance, analytics, alerts, and reporting. All endpoints return JSON responses and follow standard HTTP status codes.

## Base URL
```
https://your-domain.com/MtcInvoiceNewProject/api/attendance
```

## Authentication

All API endpoints require authentication. Include the session token in the Authorization header:

```http
Authorization: Bearer {session_token}
```

## Response Format

All API responses follow this standard format:

```json
{
    "success": true|false,
    "data": {...},
    "message": "Response message",
    "timestamp": "2024-01-15 10:30:00"
}
```

## Error Handling

### HTTP Status Codes
- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `409` - Conflict
- `422` - Validation Error
- `500` - Internal Server Error

### Error Response Format
```json
{
    "success": false,
    "data": null,
    "message": "Error description",
    "errors": {
        "field_name": ["Validation error message"]
    },
    "timestamp": "2024-01-15 10:30:00"
}
```

## Workers API

### Get All Workers
Retrieve a list of all workers with optional filtering and pagination.

```http
GET /workers.php
```

**Query Parameters:**
- `department` (string, optional) - Filter by department
- `status` (string, optional) - Filter by status (active, inactive)
- `search` (string, optional) - Search by name or employee ID
- `page` (integer, optional) - Page number (default: 1)
- `limit` (integer, optional) - Items per page (default: 50)

**Response:**
```json
{
    "success": true,
    "data": {
        "workers": [
            {
                "id": 1,
                "employee_id": "EMP001",
                "name": "John Doe",
                "email": "<EMAIL>",
                "phone": "+1234567890",
                "department": "IT",
                "position": "Developer",
                "hire_date": "2024-01-15",
                "hourly_rate": 25.00,
                "status": "active",
                "profile_image": "/uploads/profiles/john_doe.jpg",
                "created_at": "2024-01-15 09:00:00",
                "updated_at": "2024-01-15 09:00:00"
            }
        ],
        "pagination": {
            "current_page": 1,
            "total_pages": 5,
            "total_records": 250,
            "per_page": 50
        }
    }
}
```

### Get Single Worker
Retrieve details of a specific worker.

```http
GET /workers.php/{id}
```

**Response:**
```json
{
    "success": true,
    "data": {
        "id": 1,
        "employee_id": "EMP001",
        "name": "John Doe",
        "email": "<EMAIL>",
        "phone": "+1234567890",
        "department": "IT",
        "position": "Developer",
        "hire_date": "2024-01-15",
        "hourly_rate": 25.00,
        "status": "active",
        "profile_image": "/uploads/profiles/john_doe.jpg",
        "manager_id": 5,
        "manager_name": "Jane Smith",
        "recent_attendance_rate": 95.5,
        "created_at": "2024-01-15 09:00:00",
        "updated_at": "2024-01-15 09:00:00"
    }
}
```

### Create Worker
Create a new worker record.

```http
POST /workers.php
Content-Type: application/json
```

**Request Body:**
```json
{
    "employee_id": "EMP002",
    "name": "Jane Smith",
    "email": "<EMAIL>",
    "phone": "+1234567891",
    "department": "HR",
    "position": "HR Manager",
    "hire_date": "2024-01-20",
    "hourly_rate": 30.00,
    "manager_id": null,
    "status": "active"
}
```

**Response:**
```json
{
    "success": true,
    "data": {
        "id": 2,
        "employee_id": "EMP002",
        "name": "Jane Smith",
        "email": "<EMAIL>",
        "phone": "+1234567891",
        "department": "HR",
        "position": "HR Manager",
        "hire_date": "2024-01-20",
        "hourly_rate": 30.00,
        "status": "active",
        "created_at": "2024-01-20 10:00:00",
        "updated_at": "2024-01-20 10:00:00"
    },
    "message": "Worker created successfully"
}
```

### Update Worker
Update an existing worker record.

```http
PUT /workers.php/{id}
Content-Type: application/json
```

**Request Body:**
```json
{
    "name": "Jane Smith-Johnson",
    "email": "<EMAIL>",
    "position": "Senior HR Manager",
    "hourly_rate": 35.00
}
```

### Delete Worker
Soft delete a worker record.

```http
DELETE /workers.php/{id}
```

## Attendance Records API

### Get Attendance Records
Retrieve attendance records with filtering options.

```http
GET /records.php
```

**Query Parameters:**
- `worker_id` (integer, optional) - Filter by worker ID
- `department` (string, optional) - Filter by department
- `status` (string, optional) - Filter by attendance status
- `start_date` (date, optional) - Start date (YYYY-MM-DD)
- `end_date` (date, optional) - End date (YYYY-MM-DD)
- `page` (integer, optional) - Page number
- `limit` (integer, optional) - Items per page

**Response:**
```json
{
    "success": true,
    "data": {
        "records": [
            {
                "id": 1,
                "worker_id": 1,
                "worker_name": "John Doe",
                "employee_id": "EMP001",
                "department": "IT",
                "attendance_date": "2024-01-15",
                "status": "present",
                "check_in_time": "09:00:00",
                "check_out_time": "17:30:00",
                "break_duration_minutes": 60,
                "overtime_minutes": 30,
                "emoji_tags": ["😊", "💻", "🎯"],
                "notes": "Productive day",
                "productivity_score": 4,
                "created_at": "2024-01-15 09:00:00",
                "updated_at": "2024-01-15 17:30:00"
            }
        ],
        "pagination": {
            "current_page": 1,
            "total_pages": 10,
            "total_records": 500,
            "per_page": 50
        }
    }
}
```

### Record Attendance
Create a new attendance record.

```http
POST /records.php
Content-Type: application/json
```

**Request Body:**
```json
{
    "worker_id": 1,
    "attendance_date": "2024-01-15",
    "status": "present",
    "check_in_time": "09:00:00",
    "check_out_time": "17:30:00",
    "break_duration_minutes": 60,
    "emoji_tags": ["😊", "💻", "🎯"],
    "notes": "Productive day",
    "productivity_score": 4
}
```

**Attendance Status Options:**
- `present` - Worker is present
- `absent` - Worker is absent
- `late` - Worker arrived late
- `half_day` - Worker worked half day
- `sick_leave` - Worker on sick leave
- `vacation` - Worker on vacation
- `remote_work` - Worker working remotely
- `business_trip` - Worker on business trip
- `holiday` - Public holiday

### Get Timeline Data
Retrieve attendance data formatted for calendar timeline view.

```http
GET /records.php/timeline
```

**Query Parameters:**
- `start_date` (date, required) - Start date
- `end_date` (date, required) - End date
- `department` (string, optional) - Filter by department

### Bulk Record Attendance
Create multiple attendance records in a single request.

```http
POST /records.php/bulk
Content-Type: application/json
```

**Request Body:**
```json
{
    "records": [
        {
            "worker_id": 1,
            "attendance_date": "2024-01-15",
            "status": "present",
            "check_in_time": "09:00:00",
            "check_out_time": "17:30:00"
        },
        {
            "worker_id": 2,
            "attendance_date": "2024-01-15",
            "status": "late",
            "check_in_time": "09:30:00",
            "check_out_time": "17:30:00"
        }
    ]
}
```

## Analytics API

### Get Dashboard Data
Retrieve comprehensive dashboard analytics.

```http
GET /analytics.php/dashboard
```

**Query Parameters:**
- `start_date` (date, optional) - Start date
- `end_date` (date, optional) - End date
- `department` (string, optional) - Filter by department

**Response:**
```json
{
    "success": true,
    "data": {
        "overview": {
            "total_records": 1500,
            "unique_workers": 50,
            "attendance_rate": 92.5,
            "present_count": 1200,
            "late_count": 180,
            "absent_count": 120,
            "avg_productivity_score": 3.8,
            "total_overtime_minutes": 15000
        },
        "trends": [
            {
                "period": "2024-01-01",
                "present_count": 45,
                "late_count": 3,
                "absent_count": 2,
                "attendance_rate": 96.0
            }
        ],
        "departments": [
            {
                "department": "IT",
                "worker_count": 15,
                "attendance_rate": 94.5,
                "avg_productivity": 4.1
            }
        ],
        "top_performers": [
            {
                "id": 1,
                "name": "John Doe",
                "employee_id": "EMP001",
                "department": "IT",
                "attendance_rate": 98.5,
                "punctuality_rate": 95.0,
                "avg_productivity_score": 4.5
            }
        ]
    }
}
```

### Get Attendance Trends
Retrieve attendance trends over time.

```http
GET /analytics.php/trends
```

**Query Parameters:**
- `period` (string, required) - Period type (daily, weekly, monthly)
- `start_date` (date, optional) - Start date
- `end_date` (date, optional) - End date
- `department` (string, optional) - Filter by department

### Get Heatmap Data
Retrieve data for attendance heatmap visualization.

```http
GET /analytics.php/heatmap
```

**Query Parameters:**
- `worker_id` (integer, optional) - Specific worker ID
- `start_date` (date, optional) - Start date
- `end_date` (date, optional) - End date

### Get Worker Performance
Retrieve detailed worker performance metrics.

```http
GET /analytics.php/performance
```

**Query Parameters:**
- `worker_id` (integer, optional) - Specific worker ID
- `start_date` (date, optional) - Start date
- `end_date` (date, optional) - End date

## Alerts API

### Get Active Alerts
Retrieve active attendance alerts.

```http
GET /alerts.php
```

**Query Parameters:**
- `worker_id` (integer, optional) - Filter by worker ID
- `severity` (string, optional) - Filter by severity (critical, high, medium, low)
- `alert_type` (string, optional) - Filter by alert type

**Response:**
```json
{
    "success": true,
    "data": {
        "alerts": [
            {
                "id": 1,
                "worker_id": 5,
                "worker_name": "Bob Johnson",
                "employee_id": "EMP005",
                "department": "Sales",
                "alert_type": "consecutive_absences",
                "alert_message": "Worker has been absent for 4 consecutive days",
                "severity": "high",
                "trigger_data": {
                    "count": 4,
                    "dates": ["2024-01-10", "2024-01-11", "2024-01-12", "2024-01-15"],
                    "threshold": 3
                },
                "is_resolved": false,
                "created_at": "2024-01-15 10:00:00"
            }
        ],
        "grouped_alerts": {
            "critical": [],
            "high": [/* high severity alerts */],
            "medium": [/* medium severity alerts */],
            "low": [/* low severity alerts */]
        },
        "total_count": 5,
        "severity_counts": {
            "critical": 0,
            "high": 2,
            "medium": 2,
            "low": 1
        }
    }
}
```

### Resolve Alert
Mark an alert as resolved.

```http
PUT /alerts.php/{id}/resolve
Content-Type: application/json
```

**Request Body:**
```json
{
    "resolved_by": 1,
    "notes": "Discussed with employee, issue resolved"
}
```

### Analyze Worker Patterns
Trigger pattern analysis for a specific worker.

```http
POST /alerts.php/analyze/{worker_id}
```

### Analyze All Workers
Trigger pattern analysis for all active workers.

```http
POST /alerts.php/analyze
```

## Reports API

### Generate Daily Report
Generate a daily attendance report.

```http
GET /reports.php/daily
```

**Query Parameters:**
- `date` (date, optional) - Specific date (default: today)
- `department` (string, optional) - Filter by department

### Generate Weekly Report
Generate a weekly attendance report.

```http
GET /reports.php/weekly
```

**Query Parameters:**
- `start_date` (date, optional) - Week start date
- `end_date` (date, optional) - Week end date
- `department` (string, optional) - Filter by department

### Generate Monthly Report
Generate a monthly attendance report.

```http
GET /reports.php/monthly
```

**Query Parameters:**
- `month` (string, optional) - Month in YYYY-MM format
- `department` (string, optional) - Filter by department

### Generate Worker Report
Generate an individual worker report.

```http
GET /reports.php/worker/{worker_id}
```

**Query Parameters:**
- `start_date` (date, optional) - Start date
- `end_date` (date, optional) - End date

### Export Data
Export attendance data in various formats.

```http
GET /reports.php/export
```

**Query Parameters:**
- `format` (string, required) - Export format (csv, excel, pdf)
- `type` (string, optional) - Report type (daily, weekly, monthly)
- `start_date` (date, optional) - Start date
- `end_date` (date, optional) - End date
- `department` (string, optional) - Filter by department
- `include_charts` (boolean, optional) - Include charts in PDF export

## Rate Limiting

API requests are rate-limited to prevent abuse:
- **Standard endpoints**: 100 requests per minute
- **Analytics endpoints**: 50 requests per minute
- **Export endpoints**: 10 requests per minute

Rate limit headers are included in responses:
```http
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1642248000
```

## Webhooks

The system supports webhooks for real-time notifications:

### Available Events
- `worker.created` - New worker added
- `worker.updated` - Worker information updated
- `attendance.recorded` - New attendance record
- `alert.created` - New alert generated
- `alert.resolved` - Alert resolved

### Webhook Payload
```json
{
    "event": "alert.created",
    "data": {
        "alert_id": 1,
        "worker_id": 5,
        "alert_type": "consecutive_absences",
        "severity": "high"
    },
    "timestamp": "2024-01-15T10:00:00Z"
}
```

## SDK Examples

### JavaScript/Node.js
```javascript
const AttendanceAPI = require('./attendance-api-client');

const client = new AttendanceAPI({
    baseURL: 'https://your-domain.com/api/attendance',
    token: 'your-session-token'
});

// Get workers
const workers = await client.workers.getAll({
    department: 'IT',
    status: 'active'
});

// Record attendance
const attendance = await client.records.create({
    worker_id: 1,
    attendance_date: '2024-01-15',
    status: 'present',
    check_in_time: '09:00:00'
});
```

### PHP
```php
use AttendanceAPI\Client;

$client = new Client([
    'base_url' => 'https://your-domain.com/api/attendance',
    'token' => 'your-session-token'
]);

// Get analytics
$analytics = $client->analytics()->getDashboard([
    'start_date' => '2024-01-01',
    'end_date' => '2024-01-31'
]);

// Generate report
$report = $client->reports()->generateMonthly([
    'month' => '2024-01',
    'format' => 'pdf'
]);
```

## Testing

### API Testing with cURL
```bash
# Get workers
curl -X GET "https://your-domain.com/api/attendance/workers.php" \
     -H "Authorization: Bearer your-token" \
     -H "Content-Type: application/json"

# Record attendance
curl -X POST "https://your-domain.com/api/attendance/records.php" \
     -H "Authorization: Bearer your-token" \
     -H "Content-Type: application/json" \
     -d '{
       "worker_id": 1,
       "attendance_date": "2024-01-15",
       "status": "present",
       "check_in_time": "09:00:00"
     }'
```

### Postman Collection
A complete Postman collection is available at `/docs/postman/attendance-api.json`

## Support

For API support and questions:
- **Documentation**: https://docs.mtcinvoice.com/attendance/api
- **Email**: <EMAIL>
- **GitHub Issues**: https://github.com/mtcinvoice/attendance-system/issues
