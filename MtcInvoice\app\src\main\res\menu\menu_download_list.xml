<?xml version="1.0" encoding="utf-8"?>
<menu xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    
    <!-- Refresh action -->
    <item
        android:id="@+id/action_refresh"
        android:icon="@drawable/ic_refresh"
        android:title="@string/refresh"
        app:showAsAction="ifRoom"
        app:tint="@android:color/white" />

    <!-- Sort submenu -->
    <item
        android:id="@+id/action_sort"
        android:icon="@drawable/ic_sort"
        android:title="@string/sort_by"
        app:showAsAction="ifRoom">
        <menu>
            <group android:checkableBehavior="single">
                <item
                    android:id="@+id/sort_date"
                    android:title="@string/sort_date"
                    android:checked="true"
                    android:iconTint="@color/primary"
                    android:icon="@drawable/ic_sort" />
                <item
                    android:id="@+id/sort_name"
                    android:title="@string/sort_name"
                    android:iconTint="@color/primary"
                    android:icon="@drawable/ic_sort_by_alpha" />
                <item
                    android:id="@+id/sort_size"
                    android:title="@string/sort_size"
                    android:iconTint="@color/primary"
                    android:icon="@drawable/ic_storage" />
            </group>
        </menu>
    </item>

    <!-- Settings action -->
    <item
        android:id="@+id/action_settings"
        android:icon="@drawable/ic_settings"
        android:title="@string/settings"
        app:showAsAction="never" />

    <!-- Logout action -->
    <item
        android:id="@+id/action_logout"
        android:icon="@drawable/ic_logout"
        android:title="@string/logout"
        app:showAsAction="never" />

</menu>
