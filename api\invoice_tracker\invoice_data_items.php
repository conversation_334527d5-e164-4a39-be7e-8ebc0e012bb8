<?php
// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Set content type to JSON
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Get the request method
$method = $_SERVER['REQUEST_METHOD'];

// Get the endpoint segments
$endpoint = str_replace('/MtcInvoiceMasudvi/api/invoice_tracker', '', $_SERVER['REQUEST_URI']);
$endpoint = strtok($endpoint, '?');
$segments = array_values(array_filter(explode('/', $endpoint)));

// Function to send JSON response
function sendJsonResponse($status, $data = null, $message = '') {
    http_response_code($status);
    $response = [
        'status' => $status,
        'message' => $message,
        'data' => $data
    ];
    echo json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    exit();
}

// Include config with error handling
try {
    require_once __DIR__ . '/../config/config.php';
} catch (Exception $e) {
    sendJsonResponse(500, null, 'Configuration error: ' . $e->getMessage());
}

// Handle the request based on method and endpoint
try {
    switch ($method) {
        case 'GET':
            // Get single item or list
            if (isset($segments[0]) && is_numeric($segments[0])) {
                getInvoiceDataItem($segments[0]);
            } else {
                getInvoiceDataItems();
            }
            break;
            
        case 'POST':
            // Create new item
            createInvoiceDataItem();
            break;
            
        case 'PUT':
        case 'PATCH':
            // Update existing item
            if (isset($segments[0]) && is_numeric($segments[0])) {
                updateInvoiceDataItem($segments[0]);
            } else {
                sendJsonResponse(400, null, 'Item ID is required for update');
            }
            break;
            
        case 'DELETE':
            // Delete item (soft delete)
            if (isset($segments[0]) && is_numeric($segments[0])) {
                deleteInvoiceDataItem($segments[0]);
            } else {
                sendJsonResponse(400, null, 'Item ID is required for deletion');
            }
            break;
            
        default:
            sendJsonResponse(405, null, 'Method Not Allowed');
    }
} catch (Exception $e) {
    sendJsonResponse(500, null, 'Server Error: ' . $e->getMessage());
}

/**
 * Get all invoice data items
 */
function getInvoiceDataItems() {
    try {
        $pdo = (new Database())->getConnection();
        $stmt = $pdo->query("SELECT * FROM invoice_data_items WHERE deleted_at IS NULL");
        $items = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        sendJsonResponse(200, $items);
    } catch (PDOException $e) {
        sendJsonResponse(500, null, 'Failed to fetch invoice data items: ' . $e->getMessage());
    }
}

/**
 * Get single invoice data item by ID
 */
function getInvoiceDataItem($id) {
    try {
        $pdo = (new Database())->getConnection();
        $stmt = $pdo->prepare("SELECT * FROM invoice_data_items WHERE id = :id AND deleted_at IS NULL");
        $stmt->execute(['id' => $id]);
        $item = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($item) {
            sendJsonResponse(200, $item);
        } else {
            sendJsonResponse(404, null, 'Invoice data item not found');
        }
    } catch (PDOException $e) {
        sendJsonResponse(500, null, 'Failed to fetch invoice data item: ' . $e->getMessage());
    }
}

/**
 * Create a new invoice data item
 */
function createInvoiceDataItem() {
    try {
        $data = json_decode(file_get_contents('php://input'), true);
        
        if (empty($data)) {
            sendJsonResponse(400, null, 'No data provided');
            return;
        }
        
        $pdo = (new Database())->getConnection();
        $fields = array_keys($data);
        $values = array_values($data);
        $placeholders = array_fill(0, count($fields), '?');
        
        $sql = "INSERT INTO invoice_data_items (" . implode(', ', $fields) . ") VALUES (" . implode(', ', $placeholders) . ")";
        $stmt = $pdo->prepare($sql);
        $stmt->execute($values);
        
        $id = $pdo->lastInsertId();
        $stmt = $pdo->prepare("SELECT * FROM invoice_data_items WHERE id = ?");
        $stmt->execute([$id]);
        $item = $stmt->fetch(PDO::FETCH_ASSOC);
        
        sendJsonResponse(201, $item, 'Invoice data item created successfully');
    } catch (PDOException $e) {
        sendJsonResponse(500, null, 'Failed to create invoice data item: ' . $e->getMessage());
    } catch (Exception $e) {
        sendJsonResponse(500, null, 'Server error: ' . $e->getMessage());
    }
}

/**
 * Update an existing invoice data item
 */
function updateInvoiceDataItem($id) {
    try {
        $data = json_decode(file_get_contents('php://input'), true);
        
        if (empty($data)) {
            sendJsonResponse(400, null, 'No data provided for update');
            return;
        }
        
        $pdo = (new Database())->getConnection();
        
        // Check if item exists
        $stmt = $pdo->prepare("SELECT id FROM invoice_data_items WHERE id = ? AND deleted_at IS NULL");
        $stmt->execute([$id]);
        if (!$stmt->fetch()) {
            sendJsonResponse(404, null, 'Invoice data item not found');
            return;
        }
        
        // Prepare the SQL query
        $updates = [];
        $values = [];
        
        $allowedFields = [
            'description', 'location', 'qr', 'lpo', 'inb', 
            'amount', 'w_a', 'payment_status', 'timestamp', 'current_date_time'
        ];
        
        foreach ($data as $field => $value) {
            if (in_array($field, $allowedFields)) {
                $updates[] = "$field = ?";
                $values[] = $value;
            }
        }
        
        if (empty($updates)) {
            sendJsonResponse(400, null, 'No valid fields provided for update');
            return;
        }
        
        $values[] = $id; // Add ID for WHERE clause
        
        $sql = "UPDATE invoice_data_items SET " . implode(', ', $updates) . " WHERE id = ?";
        $stmt = $pdo->prepare($sql);
        $stmt->execute($values);
        
        // Fetch updated item
        $stmt = $pdo->prepare("SELECT * FROM invoice_data_items WHERE id = ?");
        $stmt->execute([$id]);
        $item = $stmt->fetch(PDO::FETCH_ASSOC);
        
        sendJsonResponse(200, $item, 'Invoice data item updated successfully');
    } catch (PDOException $e) {
        sendJsonResponse(500, null, 'Failed to update invoice data item: ' . $e->getMessage());
    } catch (Exception $e) {
        sendJsonResponse(500, null, 'Server error: ' . $e->getMessage());
    }
}

/**
 * Soft delete an invoice data item
 */
function deleteInvoiceDataItem($id) {
    try {
        $pdo = (new Database())->getConnection();
        
        // Check if item exists
        $stmt = $pdo->prepare("SELECT id FROM invoice_data_items WHERE id = ? AND deleted_at IS NULL");
        $stmt->execute([$id]);
        if (!$stmt->fetch()) {
            sendJsonResponse(404, null, 'Invoice data item not found');
            return;
        }
        
        // Soft delete
        $stmt = $pdo->prepare("UPDATE invoice_data_items SET deleted_at = NOW() WHERE id = ?");
        $stmt->execute([$id]);
        
        sendJsonResponse(200, null, 'Invoice data item deleted successfully');
    } catch (PDOException $e) {
        sendJsonResponse(500, null, 'Failed to delete invoice data item: ' . $e->getMessage());
    } catch (Exception $e) {
        sendJsonResponse(500, null, 'Server error: ' . $e->getMessage());
    }
}
?>
