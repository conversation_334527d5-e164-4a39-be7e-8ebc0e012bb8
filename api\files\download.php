<?php
require_once __DIR__ . '/../config/config.php';

// Enable CORS if needed
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");

// <PERSON>le preflight request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

$db = new Database();
$conn = $db->getConnection();

if (!$conn) {
    http_response_code(500);
    die("Database connection failed.");
}

// Check if file parameter exists in the URL
if (isset($_GET['file'])) {
    $fileName = basename($_GET['file']);
    
    // First try to find the file in the database
    $stmt = $conn->prepare("SELECT file_path, file_name FROM documents WHERE file_name = ?");
    $stmt->execute([$fileName]);
    $file = $stmt->fetch();
    
    // If file exists in database, use that path
    if ($file && file_exists($file['file_path'])) {
        $filePath = $file['file_path'];
        // Update download count
        $conn->prepare("UPDATE documents SET download_count = download_count + 1 WHERE file_name = ?")
             ->execute([$fileName]);
    } 
    // If not in database, try to find in uploads directory
    else {
        $basePath = __DIR__ . '/../../api/uploads/';
        $filePath = $basePath . $fileName;
        
        // Check common directories
        $possibleDirs = ['pdfs/', 'images/', ''];
        $found = false;
        
        foreach ($possibleDirs as $dir) {
            $fullPath = $basePath . $dir . $fileName;
            if (file_exists($fullPath)) {
                $filePath = $fullPath;
                $found = true;
                break;
            }
        }
        
        if (!$found) {
            http_response_code(404);
            die("File not found.");
        }
    }
    
    // Set appropriate headers for file download
    $fileSize = filesize($filePath);
    $mimeType = mime_content_type($filePath);
    
    header('Content-Description: File Transfer');
    header('Content-Type: ' . $mimeType);
    header('Content-Disposition: attachment; filename="' . basename($filePath) . '"');
    header('Content-Length: ' . $fileSize);
    header('Expires: 0');
    header('Cache-Control: must-revalidate');
    header('Pragma: public');
    
    // Clear output buffer
    if (ob_get_level()) {
        ob_end_clean();
    }
    
    // Read the file and output it
    readfile($filePath);
    exit;
}
// If no file parameter is provided
else {
    http_response_code(400);
    die("Missing file parameter.");
}
