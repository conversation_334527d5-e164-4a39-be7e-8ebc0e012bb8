<?php
require_once 'config/config.php'; // Assuming this provides getDbConnection() and other necessary functions

// Require login, if applicable
requireLogin();

if (isset($_GET['file']) && isset($_GET['action'])) {
    $file_id = $_GET['file'];
    $action = $_GET['action']; // 'view' or 'download'

    try {
        $pdo = getDbConnection();

        // Get file info from database
        $stmt = $pdo->prepare("SELECT file_name, file_path, file_type FROM documents WHERE id = ?");
        $stmt->execute([$file_id]);
        $file_info = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($file_info) {
            $original_file_name = $file_info['file_name'];
            $storage_path = $file_info['file_path'];
            $file_type = $file_info['file_type'];

            // Construct the correct file path (mirroring files.php logic)
            $file_path = '../api/uploads/' . $storage_path;
            if (!file_exists($file_path)) {
                $file_path = '../api/uploads/pdfs/' . basename($storage_path);
            }

            if (file_exists($file_path)) {
                // Determine Content-Type
                $mime_type = mime_content_type($file_path); // More robust than relying on database type
                if ($mime_type === false) { // Fallback if mime_content_type fails
                    $extension = strtolower(pathinfo($file_path, PATHINFO_EXTENSION));
                    if ($extension === 'pdf') {
                        $mime_type = 'application/pdf';
                    } elseif (in_array($extension, ['jpg', 'jpeg'])) {
                        $mime_type = 'image/jpeg';
                    } elseif ($extension === 'png') {
                        $mime_type = 'image/png';
                    } elseif (in_array($extension, ['doc', 'docx'])) {
                        $mime_type = 'application/msword'; // Or application/vnd.openxmlformats-officedocument.wordprocessingml.document
                    } else {
                        $mime_type = 'application/octet-stream'; // Default for unknown types
                    }
                }

                header('Content-Type: ' . $mime_type);
                header('Content-Length: ' . filesize($file_path));

                if ($action === 'download') {
                    // Force download
                    header('Content-Disposition: attachment; filename="' . basename($original_file_name) . '"');
                } else { // action is 'view'
                    // For viewing, suggest filename but let browser display inline
                    header('Content-Disposition: inline; filename="' . basename($original_file_name) . '"');
                }

                // Clear any previous output buffers to prevent corruption
                if (ob_get_level()) {
                    ob_end_clean();
                }

                readfile($file_path); // Read the file and output its content
                exit();

            } else {
                showAlert('File not found on server.', 'danger');
                error_log("download.php: Physical file not found at " . $file_path);
            }
        } else {
            showAlert('File record not found in database.', 'danger');
            error_log("download.php: File ID " . $file_id . " not found in database.");
        }
    } catch (Exception $e) {
        showAlert('Error serving file: ' . $e->getMessage(), 'danger');
        error_log("download.php: File serving error: " . $e->getMessage());
    }
} else {
    showAlert('Invalid file request.', 'warning');
}

// Redirect back or display a generic error if no file was served
// This part might not be reached if readfile() and exit() are successful
header('Location: files.php');
exit();
?>