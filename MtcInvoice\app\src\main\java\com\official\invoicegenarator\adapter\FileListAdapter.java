package com.official.invoicegenarator.adapter;

import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.official.invoicegenarator.R;
import com.official.invoicegenarator.model.FileInfo;

import java.util.ArrayList;
import java.util.List;

public class FileListAdapter extends RecyclerView.Adapter<FileListAdapter.FileViewHolder> {

    private List<FileInfo> fileList;
    private OnItemClickListener listener;

    public interface OnItemClickListener {
        void onItemClick(FileInfo file);
        void onDeleteClick(FileInfo file);
        void onDownloadClick(FileInfo file);
    }

    public void setOnItemClickListener(OnItemClickListener listener) {
        this.listener = listener;
    }

    public FileListAdapter(List<FileInfo> fileList) {
        this.fileList = fileList != null ? new ArrayList<>(fileList) : new ArrayList<>();
    }

    @NonNull
    @Override
    public FileViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.pdf_item, parent, false);
        return new FileViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull FileViewHolder holder, int position) {
        if (fileList == null || position < 0 || position >= fileList.size()) {
            Log.e("FileListAdapter", "Invalid position or fileList is null. Position: " + position + ", size: " + (fileList != null ? fileList.size() : 0));
            return;
        }
        FileInfo file = fileList.get(position);
        Log.d("FileListAdapter", "Binding file at position " + position + ": " + (file != null ? file.getName() : "null"));
        holder.bind(file);
    }

    @Override
    public int getItemCount() {
        return fileList != null ? fileList.size() : 0;
    }

    public void updateList(List<FileInfo> newList) {
        Log.d("FileListAdapter", "Updating list with " + (newList != null ? newList.size() : 0) + " items");
        if (fileList == null) {
            fileList = new ArrayList<>();
        } else {
            fileList.clear();
        }
        if (newList != null) {
            fileList.addAll(newList);
        }
        Log.d("FileListAdapter", "Notifying data set changed, new size: " + fileList.size());
        notifyDataSetChanged();
    }

    class FileViewHolder extends RecyclerView.ViewHolder {
        private final TextView pdfName;
        private final TextView pdfTimestamp;
        private final TextView pdfSize;
        private final ImageView viewButton;
        private final ImageView deleteButton;
        private final ImageView downloadButton;

        public FileViewHolder(@NonNull View itemView) {
            super(itemView);
            pdfName = itemView.findViewById(R.id.pdfName);
            pdfTimestamp = itemView.findViewById(R.id.pdfTimestamp);
            pdfSize = itemView.findViewById(R.id.pdfSize);
            viewButton = itemView.findViewById(R.id.viewButton);
            deleteButton = itemView.findViewById(R.id.deleteButton);
            downloadButton = itemView.findViewById(R.id.downloadButton);
            
            Log.d("FileViewHolder", "View references: " + 
                "pdfName=" + (pdfName != null) + ", " +
                "pdfTimestamp=" + (pdfTimestamp != null) + ", " +
                "pdfSize=" + (pdfSize != null) + ", " +
                "viewButton=" + (viewButton != null) + ", " +
                "deleteButton=" + (deleteButton != null) + ", " +
                "downloadButton=" + (downloadButton != null));

            // Make sure buttons are clickable
            viewButton.setClickable(true);
            deleteButton.setClickable(true);
            downloadButton.setClickable(true);
            
            // Set focusable to true to ensure touch events are received
            viewButton.setFocusable(true);
            deleteButton.setFocusable(true);
            downloadButton.setFocusable(true);
            
            // Set click listeners with logging
            viewButton.setOnClickListener(v -> {
                Log.d("FileListAdapter", "View button clicked");
                int position = getAbsoluteAdapterPosition();
                if (listener != null && position != RecyclerView.NO_POSITION) {
                    Log.d("FileListAdapter", "Calling onItemClick for position: " + position);
                    listener.onItemClick(fileList.get(position));
                }
            });

            deleteButton.setOnClickListener(v -> {
                Log.d("FileListAdapter", "Delete button clicked");
                int position = getAbsoluteAdapterPosition();
                if (listener != null && position != RecyclerView.NO_POSITION) {
                    Log.d("FileListAdapter", "Calling onDeleteClick for position: " + position);
                    listener.onDeleteClick(fileList.get(position));
                }
            });
            
            downloadButton.setOnClickListener(v -> {
                Log.d("FileListAdapter", "Download button clicked");
                int position = getAbsoluteAdapterPosition();
                if (listener != null && position != RecyclerView.NO_POSITION) {
                    Log.d("FileListAdapter", "Download clicked for position: " + position);
                    listener.onDownloadClick(fileList.get(position));
                }
            });
        }

        public void bind(FileInfo file) {
            if (file == null) {
                Log.e("FileViewHolder", "File is null");
                return;
            }
            
            Log.d("FileViewHolder", "Binding file: " + file.getName() + ", size: " + file.getSize() + ", date: " + file.getUploadDate());
            
            // Set file name
            if (pdfName != null) {
                pdfName.setText(file.getName());
            } else {
                Log.e("FileViewHolder", "pdfName TextView is null");
            }
            
            // Set file size
            if (pdfSize != null) {
                pdfSize.setText(file.getSize());
            } else {
                Log.e("FileViewHolder", "pdfSize TextView is null");
            }
            
            // Set upload date/time
            if (pdfTimestamp != null) {
                pdfTimestamp.setText(file.getUploadDate());
            } else {
                Log.e("FileViewHolder", "pdfTimestamp TextView is null");
            }
        }
    }
}
