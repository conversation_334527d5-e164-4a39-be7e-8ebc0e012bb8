<?php
/**
 * API Test for Attendance System
 */

require_once '../config/config.php';

echo "<h2>Attendance API Test</h2>";

// Test API endpoints
$endpoints = [
    'workers.php' => 'GET',
    'records.php' => 'GET', 
    'analytics.php/dashboard' => 'GET',
    'alerts.php' => 'GET'
];

echo "<div style='font-family: monospace; background: #f5f5f5; padding: 20px; border-radius: 5px;'>";

foreach ($endpoints as $endpoint => $method) {
    $url = API_BASE_URL . 'attendance/' . $endpoint;
    
    echo "<h4>Testing: $method $endpoint</h4>";
    echo "<p>URL: $url</p>";
    
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_CUSTOMREQUEST => $method,
        CURLOPT_HTTPHEADER => [
            'Content-Type: application/json',
        ],
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_TIMEOUT => 10,
        CURLOPT_CONNECTTIMEOUT => 5
    ]);
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        echo "<p style='color: red;'>✗ CURL Error: $error</p>";
    } else {
        $status_color = ($http_code >= 200 && $http_code < 300) ? 'green' : 'red';
        echo "<p style='color: $status_color;'>HTTP Status: $http_code</p>";
        
        if ($response) {
            $data = json_decode($response, true);
            if ($data) {
                echo "<p style='color: blue;'>Response: " . json_encode($data, JSON_PRETTY_PRINT) . "</p>";
            } else {
                echo "<p style='color: orange;'>Raw Response: " . substr($response, 0, 200) . "...</p>";
            }
        }
    }
    
    echo "<hr>";
}

echo "</div>";

// Test file existence
echo "<h3>File Existence Check</h3>";
echo "<div style='font-family: monospace; background: #f5f5f5; padding: 20px; border-radius: 5px;'>";

$files = [
    '../../api/attendance/workers.php',
    '../../api/attendance/records.php',
    '../../api/attendance/analytics.php',
    '../../api/attendance/alerts.php',
    '../../assets/js/attendance/calendar.js',
    '../../assets/js/attendance/dashboard.js',
    '../../assets/js/attendance/admin.js',
    '../../assets/css/attendance/calendar.css',
    '../../assets/css/attendance/dashboard.css',
    '../../assets/css/attendance/admin.css'
];

foreach ($files as $file) {
    $exists = file_exists($file);
    $color = $exists ? 'green' : 'red';
    $status = $exists ? '✓' : '✗';
    echo "<p style='color: $color;'>$status $file</p>";
}

echo "</div>";
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
    background-color: #f8f9fa;
}

h2, h3, h4 {
    color: #333;
}

pre {
    background: #f8f9fa;
    padding: 10px;
    border-radius: 5px;
    overflow-x: auto;
}
</style>
