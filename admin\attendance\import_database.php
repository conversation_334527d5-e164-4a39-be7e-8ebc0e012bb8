<?php
/**
 * Database Import Script for Attendance System
 * 
 * This script safely imports the attendance database schema
 * and handles any existing table conflicts.
 */

require_once '../config/config.php';

// Set execution time limit for large imports
set_time_limit(300);

$page_title = 'Database Import';
include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5><i class="bi bi-database"></i> Attendance Database Import</h5>
                </div>
                <div class="card-body">
                    <?php
                    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['import'])) {
                        performImport();
                    } else {
                        showImportForm();
                    }
                    ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
function showImportForm() {
    // Check current database status
    try {
        $pdo = getDbConnection();
        
        echo "<h6>Current Database Status</h6>";
        echo "<div class='table-responsive'>";
        echo "<table class='table table-sm'>";
        echo "<thead><tr><th>Table</th><th>Status</th><th>Records</th></tr></thead>";
        echo "<tbody>";
        
        $tables = ['departments', 'workers', 'attendance_records', 'attendance_alerts', 'attendance_settings', 'push_subscriptions', 'notification_history'];
        
        foreach ($tables as $table) {
            try {
                $stmt = $pdo->prepare("SHOW TABLES LIKE ?");
                $stmt->execute([$table]);
                $exists = $stmt->fetch();
                
                if ($exists) {
                    $countStmt = $pdo->prepare("SELECT COUNT(*) as count FROM `$table`");
                    $countStmt->execute();
                    $count = $countStmt->fetch()['count'];
                    
                    echo "<tr>";
                    echo "<td>$table</td>";
                    echo "<td><span class='badge bg-success'>Exists</span></td>";
                    echo "<td>$count</td>";
                    echo "</tr>";
                } else {
                    echo "<tr>";
                    echo "<td>$table</td>";
                    echo "<td><span class='badge bg-warning'>Missing</span></td>";
                    echo "<td>-</td>";
                    echo "</tr>";
                }
            } catch (Exception $e) {
                echo "<tr>";
                echo "<td>$table</td>";
                echo "<td><span class='badge bg-danger'>Error</span></td>";
                echo "<td>-</td>";
                echo "</tr>";
            }
        }
        
        echo "</tbody></table>";
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<div class='alert alert-danger'>";
        echo "<strong>Database Connection Error:</strong> " . $e->getMessage();
        echo "</div>";
        return;
    }
    
    echo "<hr>";
    echo "<h6>Import Options</h6>";
    echo "<form method='post'>";
    echo "<div class='mb-3'>";
    echo "<div class='form-check'>";
    echo "<input class='form-check-input' type='radio' name='import_mode' id='safe_import' value='safe' checked>";
    echo "<label class='form-check-label' for='safe_import'>";
    echo "<strong>Safe Import</strong> - Only create missing tables, preserve existing data";
    echo "</label>";
    echo "</div>";
    echo "<div class='form-check'>";
    echo "<input class='form-check-input' type='radio' name='import_mode' id='fresh_import' value='fresh'>";
    echo "<label class='form-check-label' for='fresh_import'>";
    echo "<strong>Fresh Import</strong> - Drop and recreate all tables (⚠️ This will delete all existing data!)";
    echo "</label>";
    echo "</div>";
    echo "</div>";
    
    echo "<div class='mb-3'>";
    echo "<div class='form-check'>";
    echo "<input class='form-check-input' type='checkbox' name='include_sample_data' id='include_sample_data' checked>";
    echo "<label class='form-check-label' for='include_sample_data'>";
    echo "Include sample data (workers, departments, attendance records)";
    echo "</label>";
    echo "</div>";
    echo "</div>";
    
    echo "<div class='alert alert-info'>";
    echo "<strong>Note:</strong> This will import the complete attendance management database schema. ";
    echo "Make sure you have a backup of your existing data before proceeding with a fresh import.";
    echo "</div>";
    
    echo "<button type='submit' name='import' class='btn btn-primary'>";
    echo "<i class='bi bi-download'></i> Start Import";
    echo "</button>";
    echo "</form>";
}

function performImport() {
    try {
        $pdo = getDbConnection();
        $importMode = $_POST['import_mode'] ?? 'safe';
        $includeSampleData = isset($_POST['include_sample_data']);
        
        echo "<h6>Import Progress</h6>";
        echo "<div class='progress mb-3'>";
        echo "<div class='progress-bar' role='progressbar' style='width: 0%' id='importProgress'></div>";
        echo "</div>";
        echo "<div id='importLog' style='font-family: monospace; background: #f8f9fa; padding: 15px; border-radius: 5px; max-height: 400px; overflow-y: auto;'>";
        
        // Read the SQL file
        $sqlFile = '../../database/attendance_clean_import.sql';
        if (!file_exists($sqlFile)) {
            throw new Exception("SQL file not found: $sqlFile");
        }
        
        $sql = file_get_contents($sqlFile);
        
        // Modify SQL based on import mode
        if ($importMode === 'safe') {
            // Replace DROP TABLE with DROP TABLE IF EXISTS and add IF NOT EXISTS to CREATE TABLE
            $sql = str_replace('DROP TABLE IF EXISTS', '-- DROP TABLE IF EXISTS', $sql);
            $sql = str_replace('CREATE TABLE `', 'CREATE TABLE IF NOT EXISTS `', $sql);
            
            // Skip sample data if tables already have data
            if (!$includeSampleData) {
                $sql = preg_replace('/INSERT INTO.*?;/s', '-- Skipped sample data insertion', $sql);
            }
        }
        
        // Split SQL into statements
        $statements = array_filter(
            array_map('trim', preg_split('/;[\r\n]+/', $sql)),
            function($stmt) {
                return !empty($stmt) && 
                       !preg_match('/^(--|\/\*|\s*$)/', $stmt) &&
                       !preg_match('/^(SET|START|COMMIT)/', $stmt);
            }
        );
        
        $totalStatements = count($statements);
        $successCount = 0;
        $errorCount = 0;
        
        echo "<p>Starting import with " . count($statements) . " statements...</p>";
        
        foreach ($statements as $index => $statement) {
            try {
                $pdo->exec($statement);
                $successCount++;
                
                // Extract operation type for logging
                if (preg_match('/^(CREATE|INSERT|ALTER|DROP)\s+(\w+)?\s*`?(\w+)`?/i', $statement, $matches)) {
                    $operation = $matches[1];
                    $object = $matches[3] ?? $matches[2] ?? 'unknown';
                    echo "<p style='color: green;'>✓ $operation: $object</p>";
                } else {
                    echo "<p style='color: green;'>✓ Executed statement " . ($index + 1) . "</p>";
                }
                
                // Update progress
                $progress = round((($index + 1) / $totalStatements) * 100);
                echo "<script>document.getElementById('importProgress').style.width = '$progress%';</script>";
                
                // Flush output for real-time updates
                if (ob_get_level()) ob_flush();
                flush();
                
            } catch (PDOException $e) {
                $errorCount++;
                
                // Check if it's a harmless error (like table already exists)
                if (strpos($e->getMessage(), 'already exists') !== false || 
                    strpos($e->getMessage(), 'Duplicate entry') !== false) {
                    echo "<p style='color: orange;'>⚠ Skipped (already exists): " . substr($statement, 0, 50) . "...</p>";
                } else {
                    echo "<p style='color: red;'>✗ Error: " . $e->getMessage() . "</p>";
                    echo "<p style='color: red; font-size: 12px;'>Statement: " . substr($statement, 0, 100) . "...</p>";
                }
            }
        }
        
        echo "</div>";
        
        echo "<div class='mt-3'>";
        if ($errorCount == 0) {
            echo "<div class='alert alert-success'>";
            echo "<h6>✓ Import Completed Successfully!</h6>";
            echo "<p>Successfully executed $successCount statements.</p>";
            echo "<a href='index.php' class='btn btn-success'>Go to Attendance Dashboard</a>";
            echo "</div>";
        } else {
            echo "<div class='alert alert-warning'>";
            echo "<h6>⚠ Import Completed with Warnings</h6>";
            echo "<p>Successful: $successCount | Errors/Warnings: $errorCount</p>";
            echo "<p>Most errors are likely due to existing tables or data, which is normal for safe imports.</p>";
            echo "<a href='index.php' class='btn btn-primary'>Go to Attendance Dashboard</a>";
            echo "</div>";
        }
        echo "</div>";
        
        // Verify import
        echo "<h6>Import Verification</h6>";
        $tables = ['departments', 'workers', 'attendance_records', 'attendance_alerts'];
        echo "<div class='row'>";
        
        foreach ($tables as $table) {
            try {
                $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM `$table`");
                $stmt->execute();
                $count = $stmt->fetch()['count'];
                
                echo "<div class='col-md-3 mb-2'>";
                echo "<div class='card text-center'>";
                echo "<div class='card-body'>";
                echo "<h6 class='card-title'>$table</h6>";
                echo "<p class='card-text'><strong>$count</strong> records</p>";
                echo "</div>";
                echo "</div>";
                echo "</div>";
                
            } catch (Exception $e) {
                echo "<div class='col-md-3 mb-2'>";
                echo "<div class='card text-center border-danger'>";
                echo "<div class='card-body'>";
                echo "<h6 class='card-title text-danger'>$table</h6>";
                echo "<p class='card-text text-danger'>Error</p>";
                echo "</div>";
                echo "</div>";
                echo "</div>";
            }
        }
        
        echo "</div>";
        
    } catch (Exception $e) {
        echo "</div>";
        echo "<div class='alert alert-danger'>";
        echo "<h6>Import Failed</h6>";
        echo "<p>Error: " . $e->getMessage() . "</p>";
        echo "<a href='?' class='btn btn-secondary'>Try Again</a>";
        echo "</div>";
    }
}

include '../includes/footer.php';
?>
