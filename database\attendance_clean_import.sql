-- ============================================================================
-- MtcInvoice Attendance Management System - Clean Database Import
-- Version: 1.0
-- Created: 2024
-- 
-- This file creates all necessary tables for the attendance system
-- Run this file to set up the complete database structure
-- ============================================================================

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";

-- ============================================================================
-- 1. DEPARTMENTS TABLE
-- ============================================================================

DROP TABLE IF EXISTS `departments`;
CREATE TABLE `departments` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `name` varchar(100) NOT NULL,
    `description` text,
    `manager_id` int(11) DEFAULT NULL,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert default departments
INSERT INTO `departments` (`name`, `description`) VALUES
('IT', 'Information Technology'),
('HR', 'Human Resources'),
('Finance', 'Finance and Accounting'),
('Operations', 'Operations and Logistics'),
('Marketing', 'Marketing and Communications'),
('Sales', 'Sales and Business Development');

-- ============================================================================
-- 2. WORKERS TABLE
-- ============================================================================

DROP TABLE IF EXISTS `workers`;
CREATE TABLE `workers` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `employee_id` varchar(50) NOT NULL,
    `user_id` bigint(20) UNSIGNED NULL,
    `name` varchar(255) NOT NULL,
    `first_name` varchar(100) NOT NULL DEFAULT '',
    `last_name` varchar(100) NOT NULL DEFAULT '',
    `email` varchar(255) DEFAULT NULL,
    `phone` varchar(20) DEFAULT NULL,
    `department` varchar(100) NOT NULL,
    `position` varchar(100) NOT NULL,
    `hire_date` date DEFAULT NULL,
    `work_schedule` JSON NULL COMMENT 'Flexible work schedules',
    `hourly_rate` decimal(10,2) DEFAULT 0.00,
    `manager_id` int(11) DEFAULT NULL,
    `status` enum('active','inactive','terminated') NOT NULL DEFAULT 'active',
    `profile_image` varchar(255) DEFAULT NULL,
    `emergency_contact` JSON NULL COMMENT 'Emergency contact information',
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `employee_id` (`employee_id`),
    KEY `department` (`department`),
    KEY `status` (`status`),
    KEY `manager_id` (`manager_id`),
    KEY `user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert sample workers
INSERT INTO `workers` (`employee_id`, `name`, `first_name`, `last_name`, `email`, `department`, `position`, `hire_date`, `hourly_rate`, `work_schedule`, `emergency_contact`, `status`) VALUES
('EMP001', 'John Doe', 'John', 'Doe', '<EMAIL>', 'IT', 'Software Developer', '2024-01-15', 25.00, 
 '{"monday": {"start": "09:00", "end": "17:00"}, "tuesday": {"start": "09:00", "end": "17:00"}, "wednesday": {"start": "09:00", "end": "17:00"}, "thursday": {"start": "09:00", "end": "17:00"}, "friday": {"start": "09:00", "end": "17:00"}}',
 '{"name": "Jane Doe", "phone": "+1234567890", "relationship": "Spouse"}', 'active'),
('EMP002', 'Jane Smith', 'Jane', 'Smith', '<EMAIL>', 'HR', 'HR Manager', '2024-01-10', 30.00,
 '{"monday": {"start": "08:30", "end": "16:30"}, "tuesday": {"start": "08:30", "end": "16:30"}, "wednesday": {"start": "08:30", "end": "16:30"}, "thursday": {"start": "08:30", "end": "16:30"}, "friday": {"start": "08:30", "end": "16:30"}}',
 '{"name": "Robert Smith", "phone": "+**********", "relationship": "Husband"}', 'active'),
('EMP003', 'Bob Johnson', 'Bob', 'Johnson', '<EMAIL>', 'Finance', 'Accountant', '2024-01-20', 22.00,
 '{"monday": {"start": "09:00", "end": "17:00"}, "tuesday": {"start": "09:00", "end": "17:00"}, "wednesday": {"start": "09:00", "end": "17:00"}, "thursday": {"start": "09:00", "end": "17:00"}, "friday": {"start": "09:00", "end": "17:00"}}',
 '{"name": "Mary Johnson", "phone": "+**********", "relationship": "Wife"}', 'active'),
('EMP004', 'Alice Brown', 'Alice', 'Brown', '<EMAIL>', 'Marketing', 'Marketing Specialist', '2024-01-25', 20.00,
 '{"monday": {"start": "10:00", "end": "18:00"}, "tuesday": {"start": "10:00", "end": "18:00"}, "wednesday": {"start": "10:00", "end": "18:00"}, "thursday": {"start": "10:00", "end": "18:00"}, "friday": {"start": "10:00", "end": "18:00"}}',
 '{"name": "Tom Brown", "phone": "+**********", "relationship": "Brother"}', 'active'),
('EMP005', 'Charlie Wilson', 'Charlie', 'Wilson', '<EMAIL>', 'Sales', 'Sales Representative', '2024-02-01', 18.00,
 '{"monday": {"start": "08:00", "end": "16:00"}, "tuesday": {"start": "08:00", "end": "16:00"}, "wednesday": {"start": "08:00", "end": "16:00"}, "thursday": {"start": "08:00", "end": "16:00"}, "friday": {"start": "08:00", "end": "16:00"}}',
 '{"name": "Sarah Wilson", "phone": "+1234567894", "relationship": "Sister"}', 'active');

-- ============================================================================
-- 3. ATTENDANCE RECORDS TABLE
-- ============================================================================

DROP TABLE IF EXISTS `attendance_records`;
CREATE TABLE `attendance_records` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `worker_id` int(11) NOT NULL,
    `attendance_date` date NOT NULL,
    `status` enum('present','absent','late','half_day','sick_leave','vacation','remote_work','business_trip','holiday') NOT NULL,
    `check_in_time` time DEFAULT NULL,
    `check_out_time` time DEFAULT NULL,
    `break_duration_minutes` int(11) DEFAULT 0,
    `overtime_minutes` int(11) DEFAULT 0,
    `emoji_tags` json DEFAULT NULL,
    `notes` text DEFAULT NULL,
    `productivity_score` tinyint(1) DEFAULT NULL CHECK (`productivity_score` >= 1 AND `productivity_score` <= 5),
    `approved_by` int(11) DEFAULT NULL,
    `approved_at` timestamp NULL DEFAULT NULL,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `worker_date` (`worker_id`, `attendance_date`),
    KEY `attendance_date` (`attendance_date`),
    KEY `status` (`status`),
    KEY `worker_id` (`worker_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert sample attendance records
INSERT INTO `attendance_records` (`worker_id`, `attendance_date`, `status`, `check_in_time`, `check_out_time`, `productivity_score`, `emoji_tags`) VALUES
(1, '2024-01-15', 'present', '09:00:00', '17:30:00', 4, '["😊", "💻", "🎯"]'),
(1, '2024-01-16', 'late', '09:30:00', '17:30:00', 3, '["😅", "🚗"]'),
(1, '2024-01-17', 'present', '08:45:00', '17:15:00', 5, '["😊", "💻", "🚀"]'),
(2, '2024-01-15', 'present', '09:00:00', '17:00:00', 4, '["😊", "📋"]'),
(2, '2024-01-16', 'present', '08:55:00', '17:05:00', 4, '["😊", "📊"]'),
(3, '2024-01-15', 'present', '09:15:00', '17:45:00', 3, '["😐", "📊"]'),
(3, '2024-01-16', 'sick_leave', NULL, NULL, NULL, '["😷"]'),
(4, '2024-01-15', 'remote_work', '09:00:00', '17:00:00', 4, '["🏠", "💻"]'),
(5, '2024-01-15', 'present', '09:30:00', '18:00:00', 3, '["😊", "📞"]');

-- ============================================================================
-- 4. ATTENDANCE ALERTS TABLE
-- ============================================================================

DROP TABLE IF EXISTS `attendance_alerts`;
CREATE TABLE `attendance_alerts` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `worker_id` int(11) NOT NULL,
    `alert_type` varchar(50) NOT NULL,
    `alert_message` text NOT NULL,
    `severity` enum('low','medium','high','critical') NOT NULL DEFAULT 'medium',
    `trigger_data` json DEFAULT NULL,
    `is_resolved` tinyint(1) NOT NULL DEFAULT 0,
    `resolved_by` int(11) DEFAULT NULL,
    `resolved_at` timestamp NULL DEFAULT NULL,
    `resolution_notes` text DEFAULT NULL,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `worker_id` (`worker_id`),
    KEY `alert_type` (`alert_type`),
    KEY `severity` (`severity`),
    KEY `is_resolved` (`is_resolved`),
    KEY `created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ============================================================================
-- 5. ATTENDANCE SETTINGS TABLE
-- ============================================================================

DROP TABLE IF EXISTS `attendance_settings`;
CREATE TABLE `attendance_settings` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `setting_key` varchar(100) NOT NULL,
    `setting_value` text NOT NULL,
    `description` text DEFAULT NULL,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `setting_key` (`setting_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert default settings
INSERT INTO `attendance_settings` (`setting_key`, `setting_value`, `description`) VALUES
('work_start_time', '09:00:00', 'Default work start time'),
('work_end_time', '17:00:00', 'Default work end time'),
('late_threshold_minutes', '15', 'Minutes after start time considered late'),
('overtime_threshold_hours', '8', 'Hours after which overtime applies'),
('consecutive_absence_alert', '3', 'Days of consecutive absence before alert'),
('monthly_working_days', '22', 'Expected working days per month'),
('require_approval', '0', 'Require approval for attendance changes');

-- ============================================================================
-- 6. PUSH NOTIFICATIONS TABLES
-- ============================================================================

DROP TABLE IF EXISTS `push_subscriptions`;
CREATE TABLE `push_subscriptions` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `user_id` int(11) DEFAULT NULL,
    `endpoint` text NOT NULL,
    `p256dh_key` text NOT NULL,
    `auth_key` text NOT NULL,
    `user_agent` text,
    `ip_address` varchar(45),
    `is_active` tinyint(1) DEFAULT 1,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `user_id` (`user_id`),
    KEY `is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

DROP TABLE IF EXISTS `notification_history`;
CREATE TABLE `notification_history` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `subscription_id` int(11) DEFAULT NULL,
    `title` varchar(255) NOT NULL,
    `body` text,
    `data` json DEFAULT NULL,
    `status` enum('sent','failed','pending') DEFAULT 'pending',
    `error_message` text,
    `sent_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `subscription_id` (`subscription_id`),
    KEY `status` (`status`),
    KEY `sent_at` (`sent_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ============================================================================
-- 7. ADD FOREIGN KEY CONSTRAINTS
-- ============================================================================

-- Add foreign key constraints after all tables are created
ALTER TABLE `workers` 
ADD CONSTRAINT `workers_manager_id_foreign` FOREIGN KEY (`manager_id`) REFERENCES `workers` (`id`) ON DELETE SET NULL;

ALTER TABLE `attendance_records` 
ADD CONSTRAINT `attendance_records_worker_id_foreign` FOREIGN KEY (`worker_id`) REFERENCES `workers` (`id`) ON DELETE CASCADE;

ALTER TABLE `attendance_alerts` 
ADD CONSTRAINT `attendance_alerts_worker_id_foreign` FOREIGN KEY (`worker_id`) REFERENCES `workers` (`id`) ON DELETE CASCADE;

ALTER TABLE `notification_history` 
ADD CONSTRAINT `notification_history_subscription_id_foreign` FOREIGN KEY (`subscription_id`) REFERENCES `push_subscriptions` (`id`) ON DELETE CASCADE;

-- ============================================================================
-- 8. CREATE PERFORMANCE INDEXES
-- ============================================================================

CREATE INDEX `idx_attendance_worker_date` ON `attendance_records`(`worker_id`, `attendance_date`);
CREATE INDEX `idx_attendance_date_status` ON `attendance_records`(`attendance_date`, `status`);
CREATE INDEX `idx_workers_department_status` ON `workers`(`department`, `status`);
CREATE INDEX `idx_alerts_worker_resolved` ON `attendance_alerts`(`worker_id`, `is_resolved`);

-- ============================================================================
-- 9. CREATE VIEWS FOR COMMON QUERIES
-- ============================================================================

-- Worker attendance summary view
CREATE OR REPLACE VIEW `worker_attendance_summary` AS
SELECT 
    w.id,
    w.employee_id,
    w.name,
    w.department,
    w.status,
    COUNT(ar.id) as total_records,
    SUM(CASE WHEN ar.status IN ('present', 'late', 'remote_work') THEN 1 ELSE 0 END) as present_days,
    SUM(CASE WHEN ar.status = 'late' THEN 1 ELSE 0 END) as late_days,
    SUM(CASE WHEN ar.status = 'absent' THEN 1 ELSE 0 END) as absent_days,
    ROUND(
        (SUM(CASE WHEN ar.status IN ('present', 'late', 'remote_work') THEN 1 ELSE 0 END) / 
         NULLIF(COUNT(ar.id), 0)) * 100, 2
    ) as attendance_rate,
    AVG(ar.productivity_score) as avg_productivity_score
FROM workers w
LEFT JOIN attendance_records ar ON w.id = ar.worker_id 
    AND ar.attendance_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
WHERE w.status = 'active'
GROUP BY w.id, w.employee_id, w.name, w.department, w.status;

-- Department attendance summary view
CREATE OR REPLACE VIEW `department_attendance_summary` AS
SELECT 
    w.department,
    COUNT(DISTINCT w.id) as total_workers,
    COUNT(ar.id) as total_records,
    SUM(CASE WHEN ar.status IN ('present', 'late', 'remote_work') THEN 1 ELSE 0 END) as present_count,
    SUM(CASE WHEN ar.status = 'late' THEN 1 ELSE 0 END) as late_count,
    SUM(CASE WHEN ar.status = 'absent' THEN 1 ELSE 0 END) as absent_count,
    ROUND(
        (SUM(CASE WHEN ar.status IN ('present', 'late', 'remote_work') THEN 1 ELSE 0 END) / 
         NULLIF(COUNT(ar.id), 0)) * 100, 2
    ) as attendance_rate,
    AVG(ar.productivity_score) as avg_productivity_score
FROM workers w
LEFT JOIN attendance_records ar ON w.id = ar.worker_id 
    AND ar.attendance_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
WHERE w.status = 'active'
GROUP BY w.department;

COMMIT;

-- ============================================================================
-- IMPORT COMPLETE
-- ============================================================================
