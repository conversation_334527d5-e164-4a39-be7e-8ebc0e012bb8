/**
 * Attendance Pattern Recognition
 * 
 * Advanced pattern analysis for attendance data including
 * trend detection, anomaly identification, and predictive insights.
 * 
 * @package MtcInvoice Attendance
 * @version 1.0
 */

class AttendancePatternAnalysis {
    constructor() {
        this.apiBaseUrl = '/MtcInvoiceNewProject/api/attendance';
        this.patterns = [];
        this.insights = [];
        this.anomalies = [];
        
        this.patternTypes = {
            seasonal: 'Seasonal Patterns',
            weekly: 'Weekly Patterns',
            daily: 'Daily Patterns',
            individual: 'Individual Patterns',
            department: 'Department Patterns',
            anomaly: 'Anomalies'
        };
    }
    
    async runPatternAnalysis() {
        try {
            this.showAnalysisProgress();
            
            // Run different types of analysis
            await this.analyzeSeasonalPatterns();
            await this.analyzeWeeklyPatterns();
            await this.analyzeDailyPatterns();
            await this.analyzeIndividualPatterns();
            await this.analyzeDepartmentPatterns();
            await this.detectAnomalies();
            
            // Generate insights
            this.generateInsights();
            
            // Render results
            this.renderPatternResults();
            
        } catch (error) {
            console.error('Pattern analysis failed:', error);
            this.showAnalysisError('Pattern analysis failed. Please try again.');
        }
    }
    
    showAnalysisProgress() {
        const container = document.getElementById('patternInsights');
        container.innerHTML = `
            <div class="analysis-progress">
                <div class="text-center">
                    <div class="spinner-border text-primary mb-3" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <h5>Analyzing Attendance Patterns</h5>
                    <p class="text-muted">This may take a few moments...</p>
                    <div class="progress mb-3">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" 
                             role="progressbar" style="width: 0%" id="analysisProgress"></div>
                    </div>
                    <div id="analysisStatus">Initializing analysis...</div>
                </div>
            </div>
        `;
        
        // Simulate progress
        this.simulateProgress();
    }
    
    simulateProgress() {
        const progressBar = document.getElementById('analysisProgress');
        const statusDiv = document.getElementById('analysisStatus');
        
        const steps = [
            { progress: 15, status: 'Analyzing seasonal patterns...' },
            { progress: 30, status: 'Examining weekly trends...' },
            { progress: 45, status: 'Processing daily patterns...' },
            { progress: 60, status: 'Evaluating individual behaviors...' },
            { progress: 75, status: 'Comparing department performance...' },
            { progress: 90, status: 'Detecting anomalies...' },
            { progress: 100, status: 'Generating insights...' }
        ];
        
        let currentStep = 0;
        const interval = setInterval(() => {
            if (currentStep < steps.length) {
                const step = steps[currentStep];
                progressBar.style.width = step.progress + '%';
                statusDiv.textContent = step.status;
                currentStep++;
            } else {
                clearInterval(interval);
            }
        }, 800);
    }
    
    async analyzeSeasonalPatterns() {
        // Simulate seasonal pattern analysis
        await this.delay(1000);
        
        this.patterns.push({
            type: 'seasonal',
            title: 'Seasonal Attendance Trends',
            description: 'Analysis of attendance patterns across different seasons and months',
            findings: [
                'Attendance rates are 8% higher in spring months (March-May)',
                'December shows the lowest attendance due to holiday season',
                'Summer months have increased remote work requests',
                'Back-to-school period (September) shows improved punctuality'
            ],
            confidence: 0.85,
            impact: 'medium'
        });
    }
    
    async analyzeWeeklyPatterns() {
        await this.delay(1000);
        
        this.patterns.push({
            type: 'weekly',
            title: 'Weekly Attendance Patterns',
            description: 'Recurring patterns throughout the work week',
            findings: [
                'Monday has the highest late arrival rate (23%)',
                'Tuesday-Thursday show peak productivity scores',
                'Friday afternoon has increased early departures',
                'Mid-week (Wednesday) has the best overall attendance rate'
            ],
            confidence: 0.92,
            impact: 'high'
        });
    }
    
    async analyzeDailyPatterns() {
        await this.delay(1000);
        
        this.patterns.push({
            type: 'daily',
            title: 'Daily Attendance Patterns',
            description: 'Intraday patterns and peak activity times',
            findings: [
                'Peak check-in time is between 8:45-9:15 AM',
                'Lunch break patterns vary by department',
                'Overtime is most common on Tuesdays and Wednesdays',
                'Remote work requests peak on Fridays'
            ],
            confidence: 0.88,
            impact: 'medium'
        });
    }
    
    async analyzeIndividualPatterns() {
        await this.delay(1000);
        
        this.patterns.push({
            type: 'individual',
            title: 'Individual Behavior Patterns',
            description: 'Unique patterns in individual worker attendance',
            findings: [
                '15% of workers show consistent early arrival patterns',
                '8% have irregular attendance requiring attention',
                'Top performers maintain 95%+ punctuality rates',
                'New hires show improvement trends after 3 months'
            ],
            confidence: 0.79,
            impact: 'high'
        });
    }
    
    async analyzeDepartmentPatterns() {
        await this.delay(1000);
        
        this.patterns.push({
            type: 'department',
            title: 'Department-wise Patterns',
            description: 'Comparative analysis across different departments',
            findings: [
                'IT department has highest remote work adoption (35%)',
                'Sales team shows flexible schedule preferences',
                'Operations maintains most consistent attendance',
                'HR department leads in punctuality metrics'
            ],
            confidence: 0.91,
            impact: 'medium'
        });
    }
    
    async detectAnomalies() {
        await this.delay(1000);
        
        this.anomalies = [
            {
                type: 'attendance_spike',
                description: 'Unusual attendance spike detected on March 15th',
                severity: 'low',
                date: '2024-03-15',
                details: 'Company event likely caused 98% attendance rate'
            },
            {
                type: 'productivity_drop',
                description: 'Productivity scores dropped 15% in week of March 20th',
                severity: 'medium',
                date: '2024-03-20',
                details: 'System maintenance may have affected productivity tracking'
            },
            {
                type: 'late_pattern',
                description: 'Unusual late arrival pattern in Engineering department',
                severity: 'high',
                date: '2024-03-25',
                details: 'Traffic construction near office affecting commute times'
            }
        ];
        
        this.patterns.push({
            type: 'anomaly',
            title: 'Anomaly Detection Results',
            description: 'Unusual patterns and outliers in attendance data',
            findings: this.anomalies.map(a => a.description),
            confidence: 0.76,
            impact: 'high'
        });
    }
    
    generateInsights() {
        this.insights = [
            {
                category: 'Productivity',
                insight: 'Workers with consistent morning routines show 23% higher productivity scores',
                recommendation: 'Implement flexible start times to accommodate different chronotypes',
                priority: 'high'
            },
            {
                category: 'Attendance',
                insight: 'Remote work options reduce sick leave usage by 18%',
                recommendation: 'Expand hybrid work policies for eligible roles',
                priority: 'medium'
            },
            {
                category: 'Punctuality',
                insight: 'Staggered start times could reduce Monday morning congestion',
                recommendation: 'Pilot staggered schedules for different departments',
                priority: 'medium'
            },
            {
                category: 'Wellness',
                insight: 'Emoji feedback correlates with productivity and attendance rates',
                recommendation: 'Use mood tracking for early intervention programs',
                priority: 'low'
            }
        ];
    }
    
    renderPatternResults() {
        const container = document.getElementById('patternInsights');
        
        container.innerHTML = `
            <div class="pattern-results">
                <div class="results-header">
                    <h6><i class="bi bi-lightbulb text-warning"></i> Pattern Analysis Complete</h6>
                    <p class="text-muted">Analysis completed at ${new Date().toLocaleString()}</p>
                </div>
                
                <div class="row">
                    <div class="col-lg-8">
                        <div class="patterns-section">
                            <h6>Discovered Patterns</h6>
                            ${this.renderPatterns()}
                        </div>
                    </div>
                    
                    <div class="col-lg-4">
                        <div class="insights-section">
                            <h6>Key Insights</h6>
                            ${this.renderInsights()}
                        </div>
                        
                        <div class="anomalies-section mt-4">
                            <h6>Anomalies Detected</h6>
                            ${this.renderAnomalies()}
                        </div>
                    </div>
                </div>
                
                <div class="analysis-actions mt-4">
                    <button class="btn btn-outline-primary btn-sm" onclick="exportPatternReport()">
                        <i class="bi bi-download"></i> Export Report
                    </button>
                    <button class="btn btn-outline-secondary btn-sm" onclick="scheduleAnalysis()">
                        <i class="bi bi-calendar"></i> Schedule Regular Analysis
                    </button>
                </div>
            </div>
        `;
    }
    
    renderPatterns() {
        return this.patterns.map(pattern => `
            <div class="pattern-card">
                <div class="pattern-header">
                    <div class="pattern-title">
                        <i class="bi bi-${this.getPatternIcon(pattern.type)}"></i>
                        ${pattern.title}
                    </div>
                    <div class="pattern-confidence">
                        <span class="badge bg-${this.getConfidenceBadgeClass(pattern.confidence)}">
                            ${Math.round(pattern.confidence * 100)}% confidence
                        </span>
                    </div>
                </div>
                <div class="pattern-description">
                    ${pattern.description}
                </div>
                <div class="pattern-findings">
                    <ul>
                        ${pattern.findings.map(finding => `<li>${finding}</li>`).join('')}
                    </ul>
                </div>
                <div class="pattern-impact">
                    <span class="impact-label">Impact:</span>
                    <span class="badge bg-${this.getImpactBadgeClass(pattern.impact)}">
                        ${pattern.impact.toUpperCase()}
                    </span>
                </div>
            </div>
        `).join('');
    }
    
    renderInsights() {
        return this.insights.map(insight => `
            <div class="insight-card priority-${insight.priority}">
                <div class="insight-category">${insight.category}</div>
                <div class="insight-text">${insight.insight}</div>
                <div class="insight-recommendation">
                    <strong>Recommendation:</strong> ${insight.recommendation}
                </div>
                <div class="insight-priority">
                    <span class="badge bg-${this.getPriorityBadgeClass(insight.priority)}">
                        ${insight.priority.toUpperCase()} PRIORITY
                    </span>
                </div>
            </div>
        `).join('');
    }
    
    renderAnomalies() {
        if (this.anomalies.length === 0) {
            return '<div class="text-muted">No anomalies detected</div>';
        }
        
        return this.anomalies.map(anomaly => `
            <div class="anomaly-card severity-${anomaly.severity}">
                <div class="anomaly-header">
                    <i class="bi bi-exclamation-triangle"></i>
                    <span class="anomaly-date">${anomaly.date}</span>
                </div>
                <div class="anomaly-description">${anomaly.description}</div>
                <div class="anomaly-details">
                    <small>${anomaly.details}</small>
                </div>
                <div class="anomaly-severity">
                    <span class="badge bg-${this.getSeverityBadgeClass(anomaly.severity)}">
                        ${anomaly.severity.toUpperCase()}
                    </span>
                </div>
            </div>
        `).join('');
    }
    
    getPatternIcon(type) {
        const icons = {
            seasonal: 'calendar-range',
            weekly: 'calendar-week',
            daily: 'clock',
            individual: 'person',
            department: 'building',
            anomaly: 'exclamation-triangle'
        };
        return icons[type] || 'graph-up';
    }
    
    getConfidenceBadgeClass(confidence) {
        if (confidence >= 0.9) return 'success';
        if (confidence >= 0.8) return 'primary';
        if (confidence >= 0.7) return 'warning';
        return 'secondary';
    }
    
    getImpactBadgeClass(impact) {
        switch (impact) {
            case 'high': return 'danger';
            case 'medium': return 'warning';
            case 'low': return 'info';
            default: return 'secondary';
        }
    }
    
    getPriorityBadgeClass(priority) {
        switch (priority) {
            case 'high': return 'danger';
            case 'medium': return 'warning';
            case 'low': return 'info';
            default: return 'secondary';
        }
    }
    
    getSeverityBadgeClass(severity) {
        switch (severity) {
            case 'high': return 'danger';
            case 'medium': return 'warning';
            case 'low': return 'info';
            default: return 'secondary';
        }
    }
    
    showAnalysisError(message) {
        const container = document.getElementById('patternInsights');
        container.innerHTML = `
            <div class="analysis-error">
                <div class="text-center py-4">
                    <i class="bi bi-exclamation-triangle text-danger" style="font-size: 3rem;"></i>
                    <h5 class="mt-3">Analysis Failed</h5>
                    <p class="text-muted">${message}</p>
                    <button class="btn btn-primary" onclick="runPatternAnalysis()">
                        <i class="bi bi-arrow-clockwise"></i> Retry Analysis
                    </button>
                </div>
            </div>
        `;
    }
    
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// Global functions
function runPatternAnalysis() {
    if (!window.patternAnalysis) {
        window.patternAnalysis = new AttendancePatternAnalysis();
    }
    window.patternAnalysis.runPatternAnalysis();
}

function exportPatternReport() {
    // Implementation for exporting pattern analysis report
    const startDate = document.getElementById('analyticsStartDate').value;
    const endDate = document.getElementById('analyticsEndDate').value;
    
    const params = new URLSearchParams({
        start_date: startDate,
        end_date: endDate,
        type: 'pattern_analysis',
        format: 'pdf'
    });
    
    const url = `/MtcInvoiceNewProject/api/attendance/reports.php/export?${params}`;
    window.open(url, '_blank');
}

function scheduleAnalysis() {
    // Implementation for scheduling regular pattern analysis
    alert('Scheduled analysis feature coming soon!');
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    window.patternAnalysis = new AttendancePatternAnalysis();
});
