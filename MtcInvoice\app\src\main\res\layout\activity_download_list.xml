<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appBarLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@android:color/transparent"
        app:elevation="0dp">

        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/primary"
            app:title="@string/app_name"
            app:titleTextColor="@android:color/white"
            app:navigationIcon="@drawable/ic_arrow_back"
            app:popupTheme="@style/ThemeOverlay.AppCompat.Light"
            app:theme="@style/ThemeOverlay.MaterialComponents.Dark.ActionBar" />

        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="16dp"
            android:layout_marginTop="8dp"
            android:layout_marginBottom="8dp"
            app:cardCornerRadius="24dp"
            app:cardElevation="2dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@android:color/white"
                android:orientation="horizontal"
                android:padding="4dp">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="12dp"
                    android:src="@drawable/ic_search"
                    app:tint="@color/gray_500" />

                <EditText
                    android:id="@+id/searchEditText"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:layout_marginEnd="12dp"
                    android:background="@null"
                    android:hint="@string/search_pdfs"
                    android:imeOptions="actionSearch"
                    android:inputType="text"
                    android:maxLines="1"
                    android:paddingVertical="12dp"
                    android:singleLine="true" />
            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <HorizontalScrollView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingHorizontal="8dp"
            android:scrollbars="none">

            <com.google.android.material.chip.ChipGroup
                android:id="@+id/sortOptions"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:singleLine="true"
                app:singleSelection="true">

                <com.google.android.material.chip.Chip
                    android:id="@+id/sortByDate"
                    style="@style/Widget.Material3.Chip.Filter"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:checkable="true"
                    android:checked="true"
                    android:text="@string/newest"
                    app:chipIcon="@drawable/ic_sort"
                    app:chipIconTint="@color/white"
                    app:checkedIconVisible="false"
                    app:chipBackgroundColor="@color/primary"
                    app:chipStrokeColor="@color/primary_dark"
                    app:chipStrokeWidth="1dp"
                    app:rippleColor="@color/primary_light" />

                <com.google.android.material.chip.Chip
                    android:id="@+id/sortByName"
                    style="@style/Widget.Material3.Chip.Filter"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:checkable="true"
                    android:text="@string/name"
                    app:chipIcon="@drawable/ic_sort_by_alpha"
                    app:chipIconTint="@color/white"
                    app:checkedIconVisible="false"
                    app:chipBackgroundColor="@color/primary"
                    app:chipStrokeColor="@color/primary_dark"
                    app:chipStrokeWidth="1dp" />

                <com.google.android.material.chip.Chip
                    android:id="@+id/sortBySize"
                    style="@style/Widget.Material3.Chip.Filter"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:checkable="true"
                    android:text="@string/size"
                    app:chipIcon="@drawable/ic_storage"
                    app:chipIconTint="@color/white"
                    app:checkedIconVisible="false"
                    app:chipBackgroundColor="@color/primary"
                    app:chipStrokeColor="@color/primary_dark"
                    app:chipStrokeWidth="1dp" />
            </com.google.android.material.chip.ChipGroup>
        </HorizontalScrollView>
    </com.google.android.material.appbar.AppBarLayout>

    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipeRefreshLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:clipToPadding="false"
                android:paddingHorizontal="8dp"
                android:paddingTop="8dp"
                android:paddingBottom="88dp"
                android:scrollbars="vertical"
                android:overScrollMode="ifContentScrolls"
                android:scrollbarStyle="insideOverlay" />

            <ProgressBar
                android:id="@+id/loadingProgressBar"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:visibility="gone" />

            <LinearLayout
                android:id="@+id/emptyStateView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center"
                android:orientation="vertical"
                android:padding="32dp"
                android:visibility="gone">

                <ImageView
                    android:layout_width="120dp"
                    android:layout_height="120dp"
                    android:src="@drawable/ic_empty_folder"
                    android:contentDescription="@string/no_files_found"
                    app:tint="@color/gray_500" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:text="@string/no_files_found"
                    android:textColor="@color/gray_700"
                    android:textSize="18sp"
                    android:textStyle="bold" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:gravity="center"
                    android:text="@string/no_files_description"
                    android:textAlignment="center"
                    android:textColor="@color/gray_500"
                    android:textSize="14sp" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/errorStateView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center"
                android:orientation="vertical"
                android:padding="32dp"
                android:visibility="gone">

                <ImageView
                    android:layout_width="120dp"
                    android:layout_height="120dp"
                    android:src="@drawable/ic_error"
                    android:contentDescription="@string/error_occurred"
                    app:tint="@color/error" />

                <TextView
                    android:id="@+id/errorMessageTextView"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:text="@string/error_loading_files"
                    android:textColor="@color/gray_700"
                    android:textSize="18sp"
                    android:textStyle="bold" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/retryButton"
                    style="@style/Widget.Material3.Button.OutlinedButton"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="24dp"
                    android:text="@string/retry"
                    app:icon="@drawable/ic_refresh"
                    app:iconGravity="textStart"
                    app:iconTint="@color/primary"
                    app:strokeColor="@color/primary"
                    app:strokeWidth="1dp" />
            </LinearLayout>
        </FrameLayout>
    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
