// SelectionActivity.java
package com.official.invoicegenarator;

import android.content.Intent;
import android.os.Bundle;
import android.view.MenuItem;
import android.view.View;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.view.GravityCompat;
import androidx.drawerlayout.widget.DrawerLayout;

import com.google.android.material.appbar.MaterialToolbar;
import com.google.android.material.dialog.MaterialAlertDialogBuilder;
import com.google.android.material.navigation.NavigationView;

public class SelectionActivity extends AppCompatActivity {
    CustomButtonEffect customButtonEffect;
    private ImageView workerPresentationImageView, invoiceImageView,invoiceImageViewTwo,moneybagImageView,invoice_traker;
    private DrawerLayout drawerLayout;
    private NavigationView navigationView;
    private MaterialToolbar materialToolbar;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_selection);

        // Initialize UI components

        invoiceImageView = findViewById(R.id.iv_invoice);
        invoiceImageViewTwo = findViewById(R.id.iv_invoice_two);
        invoice_traker = findViewById(R.id.invoice_traker);

        // Initialize UI components

        drawerLayout = findViewById(R.id.drawer_layout);

        navigationView = findViewById(R.id.nav_view);

        materialToolbar = findViewById(R.id.toolbar);


        // Set up toolbar

        setSupportActionBar(materialToolbar);

        getSupportActionBar().setDisplayHomeAsUpEnabled(true);

        getSupportActionBar().setHomeAsUpIndicator(R.drawable.baseline_menu_24);
        customButtonEffect = new CustomButtonEffect(invoiceImageView);
        customButtonEffect = new CustomButtonEffect(invoiceImageViewTwo);
        customButtonEffect = new CustomButtonEffect(invoice_traker);
        // Set up click listeners

        invoiceImageView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(SelectionActivity.this, Home.class);
                startActivity(intent);
                overridePendingTransition(R.anim.fade_in, R.anim.fade_out);
            }
        });
        invoiceImageViewTwo.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(SelectionActivity.this, InvoiceTwo.class);
                startActivity(intent);
                overridePendingTransition(R.anim.fade_in, R.anim.fade_out);
            }
        });
        invoice_traker.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(SelectionActivity.this,InvoiceTraker.class);
                startActivity(intent);
                overridePendingTransition(R.anim.fade_in, R.anim.fade_out);
            }
        });
        // Set up navigation drawer

        navigationView.setNavigationItemSelectedListener(new NavigationView.OnNavigationItemSelectedListener() {

            @Override

            public boolean onNavigationItemSelected(@NonNull MenuItem item) {

                int itemId = item.getItemId();
                if (itemId == R.id.iv_invoice) {
                    // Handle invoice navigation item
                    Intent intent = new Intent(SelectionActivity.this, Home.class);
                    startActivity(intent);
                    overridePendingTransition(R.anim.fade_in, R.anim.fade_out);
                } else if (itemId == R.id.nav_settings) {
                    // Handle settings navigation item
                    Intent intent = new Intent(SelectionActivity.this, FingerprintSettingsActivity.class);
                    startActivity(intent);
                    overridePendingTransition(R.anim.fade_in, R.anim.fade_out);
                }

                drawerLayout.closeDrawer(GravityCompat.START);

                return true;

            }

        });
    }
    @Override

    public boolean onOptionsItemSelected(@NonNull MenuItem item) {

        if (item.getItemId() == android.R.id.home) {

            drawerLayout.openDrawer(GravityCompat.START);

            return true;

        }

        return super.onOptionsItemSelected(item);

    }
    @Override
    public void onBackPressed() {
        new MaterialAlertDialogBuilder(this)
                .setTitle("Exit")
                .setMessage("Are you sure you want to exit?")
                .setPositiveButton("Yes", (dialog, which) -> {
                    //super.onBackPressed(); // Exit the activity
                    finishAffinity();
                })
                .setNegativeButton("No", (dialog, which) -> dialog.dismiss())
                .show();
    }
    /*@Override
    public void onBackPressed() {
        super.onBackPressed();
        Intent intent = new Intent(SelectionActivity.this, VarifyActivity.class);
        startActivity(intent);
        overridePendingTransition(R.anim.rotate_out, R.anim.rotate_in);
    }*/
}