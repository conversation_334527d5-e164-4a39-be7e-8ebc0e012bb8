/**
 * Service Worker for Attendance Management System
 * Provides offline functionality, caching, and push notifications
 * 
 * @version 1.0
 */

const CACHE_NAME = 'attendance-system-v1.0.0';
const OFFLINE_URL = '/MtcInvoiceNewProject/offline.html';

// Files to cache for offline functionality
const STATIC_CACHE_URLS = [
    '/MtcInvoiceNewProject/',
    '/MtcInvoiceNewProject/admin/',
    '/MtcInvoiceNewProject/admin/attendance/',
    '/MtcInvoiceNewProject/offline.html',
    
    // CSS Files
    '/MtcInvoiceNewProject/assets/css/attendance/calendar.css',
    '/MtcInvoiceNewProject/assets/css/attendance/dashboard.css',
    '/MtcInvoiceNewProject/assets/css/attendance/admin.css',
    '/MtcInvoiceNewProject/assets/css/attendance/analytics.css',
    
    // JavaScript Files
    '/MtcInvoiceNewProject/assets/js/attendance/calendar.js',
    '/MtcInvoiceNewProject/assets/js/attendance/dashboard.js',
    '/MtcInvoiceNewProject/assets/js/attendance/admin.js',
    '/MtcInvoiceNewProject/assets/js/attendance/analytics-charts.js',
    '/MtcInvoiceNewProject/assets/js/attendance/analytics-heatmap.js',
    '/MtcInvoiceNewProject/assets/js/attendance/analytics-patterns.js',
    
    // External CDN Resources
    'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css',
    'https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css',
    'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js',
    'https://cdn.jsdelivr.net/npm/chart.js',
    'https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns/dist/chartjs-adapter-date-fns.bundle.min.js'
];

// API endpoints to cache
const API_CACHE_URLS = [
    '/MtcInvoiceNewProject/api/attendance/workers.php',
    '/MtcInvoiceNewProject/api/attendance/records.php',
    '/MtcInvoiceNewProject/api/attendance/analytics.php/dashboard',
    '/MtcInvoiceNewProject/api/attendance/alerts.php'
];

// Install event - cache static resources
self.addEventListener('install', event => {
    console.log('Service Worker: Installing...');
    
    event.waitUntil(
        caches.open(CACHE_NAME)
            .then(cache => {
                console.log('Service Worker: Caching static files');
                return cache.addAll(STATIC_CACHE_URLS);
            })
            .then(() => {
                console.log('Service Worker: Installation complete');
                return self.skipWaiting();
            })
            .catch(error => {
                console.error('Service Worker: Installation failed', error);
            })
    );
});

// Activate event - clean up old caches
self.addEventListener('activate', event => {
    console.log('Service Worker: Activating...');
    
    event.waitUntil(
        caches.keys()
            .then(cacheNames => {
                return Promise.all(
                    cacheNames.map(cacheName => {
                        if (cacheName !== CACHE_NAME) {
                            console.log('Service Worker: Deleting old cache', cacheName);
                            return caches.delete(cacheName);
                        }
                    })
                );
            })
            .then(() => {
                console.log('Service Worker: Activation complete');
                return self.clients.claim();
            })
    );
});

// Fetch event - serve cached content when offline
self.addEventListener('fetch', event => {
    const request = event.request;
    const url = new URL(request.url);
    
    // Handle different types of requests
    if (request.method === 'GET') {
        if (isStaticResource(url)) {
            // Static resources - cache first strategy
            event.respondWith(cacheFirst(request));
        } else if (isAPIRequest(url)) {
            // API requests - network first with cache fallback
            event.respondWith(networkFirstWithCache(request));
        } else if (isNavigationRequest(request)) {
            // Navigation requests - network first with offline fallback
            event.respondWith(navigationHandler(request));
        }
    } else if (request.method === 'POST' && isAPIRequest(url)) {
        // POST requests - handle offline queue
        event.respondWith(handleOfflinePost(request));
    }
});

// Cache first strategy for static resources
async function cacheFirst(request) {
    try {
        const cache = await caches.open(CACHE_NAME);
        const cachedResponse = await cache.match(request);
        
        if (cachedResponse) {
            // Update cache in background
            fetch(request).then(response => {
                if (response.ok) {
                    cache.put(request, response.clone());
                }
            }).catch(() => {
                // Ignore network errors for background updates
            });
            
            return cachedResponse;
        }
        
        // Not in cache, fetch from network
        const networkResponse = await fetch(request);
        if (networkResponse.ok) {
            cache.put(request, networkResponse.clone());
        }
        return networkResponse;
        
    } catch (error) {
        console.error('Cache first strategy failed:', error);
        return new Response('Offline - Resource not available', { status: 503 });
    }
}

// Network first with cache fallback for API requests
async function networkFirstWithCache(request) {
    try {
        const networkResponse = await fetch(request);
        
        if (networkResponse.ok) {
            // Cache successful API responses
            const cache = await caches.open(CACHE_NAME);
            cache.put(request, networkResponse.clone());
        }
        
        return networkResponse;
        
    } catch (error) {
        console.log('Network failed, trying cache:', request.url);
        
        const cache = await caches.open(CACHE_NAME);
        const cachedResponse = await cache.match(request);
        
        if (cachedResponse) {
            // Add offline indicator to cached API responses
            const data = await cachedResponse.json();
            data.offline = true;
            data.cached_at = new Date().toISOString();
            
            return new Response(JSON.stringify(data), {
                headers: { 'Content-Type': 'application/json' }
            });
        }
        
        // Return offline response for API requests
        return new Response(JSON.stringify({
            success: false,
            message: 'Offline - Data not available',
            offline: true
        }), {
            status: 503,
            headers: { 'Content-Type': 'application/json' }
        });
    }
}

// Navigation handler for page requests
async function navigationHandler(request) {
    try {
        const networkResponse = await fetch(request);
        return networkResponse;
        
    } catch (error) {
        console.log('Navigation offline, serving offline page');
        
        const cache = await caches.open(CACHE_NAME);
        const offlineResponse = await cache.match(OFFLINE_URL);
        
        return offlineResponse || new Response('Offline', { status: 503 });
    }
}

// Handle offline POST requests
async function handleOfflinePost(request) {
    try {
        return await fetch(request);
        
    } catch (error) {
        // Store POST request for later sync
        const requestData = {
            url: request.url,
            method: request.method,
            headers: Object.fromEntries(request.headers.entries()),
            body: await request.text(),
            timestamp: Date.now()
        };
        
        // Store in IndexedDB for background sync
        await storeOfflineRequest(requestData);
        
        return new Response(JSON.stringify({
            success: false,
            message: 'Request queued for when online',
            queued: true
        }), {
            status: 202,
            headers: { 'Content-Type': 'application/json' }
        });
    }
}

// Store offline requests in IndexedDB
async function storeOfflineRequest(requestData) {
    return new Promise((resolve, reject) => {
        const request = indexedDB.open('AttendanceOfflineDB', 1);
        
        request.onerror = () => reject(request.error);
        request.onsuccess = () => {
            const db = request.result;
            const transaction = db.transaction(['offlineRequests'], 'readwrite');
            const store = transaction.objectStore('offlineRequests');
            
            store.add(requestData);
            transaction.oncomplete = () => resolve();
            transaction.onerror = () => reject(transaction.error);
        };
        
        request.onupgradeneeded = () => {
            const db = request.result;
            if (!db.objectStoreNames.contains('offlineRequests')) {
                const store = db.createObjectStore('offlineRequests', { 
                    keyPath: 'id', 
                    autoIncrement: true 
                });
                store.createIndex('timestamp', 'timestamp');
            }
        };
    });
}

// Background sync for offline requests
self.addEventListener('sync', event => {
    if (event.tag === 'attendance-sync') {
        event.waitUntil(syncOfflineRequests());
    }
});

// Sync offline requests when back online
async function syncOfflineRequests() {
    try {
        const db = await openOfflineDB();
        const transaction = db.transaction(['offlineRequests'], 'readonly');
        const store = transaction.objectStore('offlineRequests');
        const requests = await getAllFromStore(store);
        
        for (const requestData of requests) {
            try {
                const response = await fetch(requestData.url, {
                    method: requestData.method,
                    headers: requestData.headers,
                    body: requestData.body
                });
                
                if (response.ok) {
                    // Remove successfully synced request
                    await removeOfflineRequest(requestData.id);
                    console.log('Synced offline request:', requestData.url);
                }
            } catch (error) {
                console.error('Failed to sync request:', error);
            }
        }
    } catch (error) {
        console.error('Background sync failed:', error);
    }
}

// Push notification handler
self.addEventListener('push', event => {
    if (!event.data) return;
    
    try {
        const data = event.data.json();
        const options = {
            body: data.body || 'New attendance alert',
            icon: '/MtcInvoiceNewProject/assets/icons/icon-192x192.png',
            badge: '/MtcInvoiceNewProject/assets/icons/badge-72x72.png',
            tag: data.tag || 'attendance-alert',
            data: data.data || {},
            actions: [
                {
                    action: 'view',
                    title: 'View Details',
                    icon: '/MtcInvoiceNewProject/assets/icons/view-icon.png'
                },
                {
                    action: 'dismiss',
                    title: 'Dismiss',
                    icon: '/MtcInvoiceNewProject/assets/icons/dismiss-icon.png'
                }
            ],
            requireInteraction: data.priority === 'high'
        };
        
        event.waitUntil(
            self.registration.showNotification(data.title || 'Attendance Alert', options)
        );
        
    } catch (error) {
        console.error('Push notification error:', error);
    }
});

// Notification click handler
self.addEventListener('notificationclick', event => {
    event.notification.close();
    
    if (event.action === 'view') {
        const urlToOpen = event.notification.data.url || '/MtcInvoiceNewProject/admin/attendance/';
        
        event.waitUntil(
            clients.matchAll({ type: 'window', includeUncontrolled: true })
                .then(clientList => {
                    // Check if attendance page is already open
                    for (const client of clientList) {
                        if (client.url.includes('/attendance/') && 'focus' in client) {
                            return client.focus();
                        }
                    }
                    
                    // Open new window
                    if (clients.openWindow) {
                        return clients.openWindow(urlToOpen);
                    }
                })
        );
    }
});

// Utility functions
function isStaticResource(url) {
    return url.pathname.match(/\.(css|js|png|jpg|jpeg|gif|svg|ico|woff|woff2|ttf)$/);
}

function isAPIRequest(url) {
    return url.pathname.includes('/api/');
}

function isNavigationRequest(request) {
    return request.mode === 'navigate' || 
           (request.method === 'GET' && request.headers.get('accept').includes('text/html'));
}

function openOfflineDB() {
    return new Promise((resolve, reject) => {
        const request = indexedDB.open('AttendanceOfflineDB', 1);
        request.onerror = () => reject(request.error);
        request.onsuccess = () => resolve(request.result);
    });
}

function getAllFromStore(store) {
    return new Promise((resolve, reject) => {
        const request = store.getAll();
        request.onerror = () => reject(request.error);
        request.onsuccess = () => resolve(request.result);
    });
}

async function removeOfflineRequest(id) {
    const db = await openOfflineDB();
    const transaction = db.transaction(['offlineRequests'], 'readwrite');
    const store = transaction.objectStore('offlineRequests');
    return store.delete(id);
}

console.log('Service Worker: Loaded successfully');
