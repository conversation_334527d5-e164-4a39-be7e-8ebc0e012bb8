<?php
/**
 * Attendance Management Dashboard
 * 
 * Main dashboard for attendance management with analytics,
 * calendar view, and quick actions.
 * 
 * @package MtcInvoice Attendance
 * @version 1.0
 */

session_start();
require_once '../../api/config/config.php';

// Check if user is logged in and has appropriate permissions
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit();
}

// Check user role
$user_role = $_SESSION['role'] ?? 'user';
if (!in_array($user_role, ['admin', 'manager'])) {
    header('Location: ../dashboard.php');
    exit();
}

$page_title = 'Attendance Management';
include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="page-title">Attendance Management</h1>
                        <p class="page-description">Monitor and manage worker attendance with real-time analytics</p>
                    </div>
                    <div class="page-actions">
                        <button class="btn btn-outline-primary me-2" data-bs-toggle="modal" data-bs-target="#bulkAttendanceModal">
                            <i class="bi bi-upload"></i> Bulk Import
                        </button>
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addWorkerModal">
                            <i class="bi bi-person-plus"></i> Add Worker
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Dashboard Analytics -->
    <div class="row mb-4">
        <div class="col-12">
            <div id="attendanceDashboard"></div>
        </div>
    </div>

    <!-- Navigation Tabs -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <ul class="nav nav-tabs card-header-tabs" id="attendanceTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="calendar-tab" data-bs-toggle="tab" data-bs-target="#calendar" type="button" role="tab">
                                <i class="bi bi-calendar3"></i> Calendar View
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="workers-tab" data-bs-toggle="tab" data-bs-target="#workers" type="button" role="tab">
                                <i class="bi bi-people"></i> Workers
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="reports-tab" data-bs-toggle="tab" data-bs-target="#reports" type="button" role="tab">
                                <i class="bi bi-graph-up"></i> Reports
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="alerts-tab" data-bs-toggle="tab" data-bs-target="#alerts" type="button" role="tab">
                                <i class="bi bi-exclamation-triangle"></i> Alerts
                                <span class="badge bg-danger ms-1" id="alertsCount">0</span>
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="settings-tab" data-bs-toggle="tab" data-bs-target="#settings" type="button" role="tab">
                                <i class="bi bi-gear"></i> Settings
                            </button>
                        </li>
                    </ul>
                </div>
                
                <div class="card-body">
                    <div class="tab-content" id="attendanceTabContent">
                        <!-- Calendar Tab -->
                        <div class="tab-pane fade show active" id="calendar" role="tabpanel">
                            <div id="attendanceCalendar"></div>
                        </div>
                        
                        <!-- Workers Tab -->
                        <div class="tab-pane fade" id="workers" role="tabpanel">
                            <div class="workers-management">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h5>Worker Management</h5>
                                    <div class="workers-actions">
                                        <div class="input-group me-3" style="width: 300px;">
                                            <input type="text" class="form-control" placeholder="Search workers..." id="workersSearch">
                                            <button class="btn btn-outline-secondary" type="button">
                                                <i class="bi bi-search"></i>
                                            </button>
                                        </div>
                                        <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addWorkerModal">
                                            <i class="bi bi-person-plus"></i> Add Worker
                                        </button>
                                    </div>
                                </div>
                                
                                <div class="table-responsive">
                                    <table class="table table-hover" id="workersTable">
                                        <thead>
                                            <tr>
                                                <th>Employee ID</th>
                                                <th>Name</th>
                                                <th>Department</th>
                                                <th>Position</th>
                                                <th>Attendance Rate</th>
                                                <th>Status</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <!-- Workers data will be loaded here -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Reports Tab -->
                        <div class="tab-pane fade" id="reports" role="tabpanel">
                            <div class="reports-section">
                                <div class="row">
                                    <div class="col-md-4 mb-3">
                                        <div class="report-card">
                                            <div class="report-icon">
                                                <i class="bi bi-calendar-day"></i>
                                            </div>
                                            <div class="report-content">
                                                <h6>Daily Report</h6>
                                                <p>Generate daily attendance summary</p>
                                                <button class="btn btn-primary btn-sm" onclick="generateReport('daily')">Generate</button>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-4 mb-3">
                                        <div class="report-card">
                                            <div class="report-icon">
                                                <i class="bi bi-calendar-week"></i>
                                            </div>
                                            <div class="report-content">
                                                <h6>Weekly Report</h6>
                                                <p>Generate weekly attendance analysis</p>
                                                <button class="btn btn-primary btn-sm" onclick="generateReport('weekly')">Generate</button>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-4 mb-3">
                                        <div class="report-card">
                                            <div class="report-icon">
                                                <i class="bi bi-calendar-month"></i>
                                            </div>
                                            <div class="report-content">
                                                <h6>Monthly Report</h6>
                                                <p>Generate monthly attendance report</p>
                                                <button class="btn btn-primary btn-sm" onclick="generateReport('monthly')">Generate</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="custom-report-section mt-4">
                                    <h5>Custom Report</h5>
                                    <div class="row">
                                        <div class="col-md-3">
                                            <label class="form-label">Start Date</label>
                                            <input type="date" class="form-control" id="reportStartDate">
                                        </div>
                                        <div class="col-md-3">
                                            <label class="form-label">End Date</label>
                                            <input type="date" class="form-control" id="reportEndDate">
                                        </div>
                                        <div class="col-md-3">
                                            <label class="form-label">Department</label>
                                            <select class="form-select" id="reportDepartment">
                                                <option value="">All Departments</option>
                                            </select>
                                        </div>
                                        <div class="col-md-3">
                                            <label class="form-label">Format</label>
                                            <select class="form-select" id="reportFormat">
                                                <option value="pdf">PDF</option>
                                                <option value="excel">Excel</option>
                                                <option value="csv">CSV</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="mt-3">
                                        <button class="btn btn-success" onclick="generateCustomReport()">
                                            <i class="bi bi-download"></i> Generate Custom Report
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Alerts Tab -->
                        <div class="tab-pane fade" id="alerts" role="tabpanel">
                            <div class="alerts-section">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h5>Attendance Alerts</h5>
                                    <div class="alerts-actions">
                                        <button class="btn btn-outline-primary me-2" onclick="analyzeAllPatterns()">
                                            <i class="bi bi-search"></i> Analyze Patterns
                                        </button>
                                        <button class="btn btn-outline-secondary" onclick="refreshAlerts()">
                                            <i class="bi bi-arrow-clockwise"></i> Refresh
                                        </button>
                                    </div>
                                </div>
                                
                                <div class="alerts-filters mb-3">
                                    <div class="row">
                                        <div class="col-md-3">
                                            <select class="form-select" id="alertSeverityFilter">
                                                <option value="">All Severities</option>
                                                <option value="critical">Critical</option>
                                                <option value="high">High</option>
                                                <option value="medium">Medium</option>
                                                <option value="low">Low</option>
                                            </select>
                                        </div>
                                        <div class="col-md-3">
                                            <select class="form-select" id="alertTypeFilter">
                                                <option value="">All Types</option>
                                                <option value="consecutive_absences">Consecutive Absences</option>
                                                <option value="frequent_late_arrivals">Frequent Late Arrivals</option>
                                                <option value="productivity_drop">Productivity Drop</option>
                                                <option value="excessive_overtime">Excessive Overtime</option>
                                                <option value="low_attendance_rate">Low Attendance Rate</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                
                                <div id="alertsList">
                                    <!-- Alerts will be loaded here -->
                                </div>
                            </div>
                        </div>
                        
                        <!-- Settings Tab -->
                        <div class="tab-pane fade" id="settings" role="tabpanel">
                            <div class="settings-section">
                                <h5>Attendance Settings</h5>
                                <form id="attendanceSettingsForm">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Default Work Start Time</label>
                                                <input type="time" class="form-control" id="workStartTime" value="09:00">
                                            </div>
                                            
                                            <div class="mb-3">
                                                <label class="form-label">Default Work End Time</label>
                                                <input type="time" class="form-control" id="workEndTime" value="17:00">
                                            </div>
                                            
                                            <div class="mb-3">
                                                <label class="form-label">Late Threshold (minutes)</label>
                                                <input type="number" class="form-control" id="lateThreshold" value="15" min="1" max="60">
                                            </div>
                                            
                                            <div class="mb-3">
                                                <label class="form-label">Half Day Hours</label>
                                                <input type="number" class="form-control" id="halfDayHours" value="4" min="1" max="8">
                                            </div>
                                        </div>
                                        
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Overtime Threshold (hours)</label>
                                                <input type="number" class="form-control" id="overtimeThreshold" value="8" min="6" max="12">
                                            </div>
                                            
                                            <div class="mb-3">
                                                <label class="form-label">Consecutive Absence Alert</label>
                                                <input type="number" class="form-control" id="consecutiveAbsenceAlert" value="3" min="1" max="10">
                                            </div>
                                            
                                            <div class="mb-3">
                                                <label class="form-label">Monthly Working Days</label>
                                                <input type="number" class="form-control" id="monthlyWorkingDays" value="22" min="15" max="31">
                                            </div>
                                            
                                            <div class="mb-3">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="requireApproval">
                                                    <label class="form-check-label" for="requireApproval">
                                                        Require Approval for Attendance Changes
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="mt-3">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="bi bi-check-lg"></i> Save Settings
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Worker Modal -->
<div class="modal fade" id="addWorkerModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add New Worker</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addWorkerForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Employee ID *</label>
                                <input type="text" class="form-control" name="employee_id" required>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">Full Name *</label>
                                <input type="text" class="form-control" name="name" required>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">Email</label>
                                <input type="email" class="form-control" name="email">
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">Phone</label>
                                <input type="tel" class="form-control" name="phone">
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Department *</label>
                                <select class="form-select" name="department" required>
                                    <option value="">Select Department</option>
                                    <option value="IT">IT</option>
                                    <option value="HR">HR</option>
                                    <option value="Finance">Finance</option>
                                    <option value="Operations">Operations</option>
                                    <option value="Marketing">Marketing</option>
                                    <option value="Sales">Sales</option>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">Position *</label>
                                <input type="text" class="form-control" name="position" required>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">Hire Date</label>
                                <input type="date" class="form-control" name="hire_date">
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">Hourly Rate</label>
                                <input type="number" class="form-control" name="hourly_rate" step="0.01" min="0">
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="saveWorker()">Save Worker</button>
            </div>
        </div>
    </div>
</div>

<!-- Bulk Attendance Modal -->
<div class="modal fade" id="bulkAttendanceModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Bulk Attendance Import</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label class="form-label">Upload CSV File</label>
                    <input type="file" class="form-control" id="bulkAttendanceFile" accept=".csv">
                    <div class="form-text">
                        CSV format: employee_id, date, status, check_in, check_out, notes
                    </div>
                </div>
                
                <div class="mb-3">
                    <a href="#" class="btn btn-outline-info btn-sm" onclick="downloadTemplate()">
                        <i class="bi bi-download"></i> Download Template
                    </a>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="uploadBulkAttendance()">Upload</button>
            </div>
        </div>
    </div>
</div>

<script src="../../assets/js/attendance/calendar.js"></script>
<script src="../../assets/js/attendance/dashboard.js"></script>
<script src="../../assets/js/attendance/admin.js"></script>

<link rel="stylesheet" href="../../assets/css/attendance/calendar.css">
<link rel="stylesheet" href="../../assets/css/attendance/dashboard.css">
<link rel="stylesheet" href="../../assets/css/attendance/admin.css">

<?php include '../includes/footer.php'; ?>
