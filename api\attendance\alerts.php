<?php
/**
 * Alerts API Endpoint
 * 
 * @package MtcInvoice Attendance
 * @version 1.0
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, PATCH, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once '../config/config.php';
require_once '../../classes/AlertSystem.php';

// Get request method and path
$method = $_SERVER['REQUEST_METHOD'];
$path = $_SERVER['REQUEST_URI'];
$segments = explode('/', trim(parse_url($path, PHP_URL_PATH), '/'));

// Remove 'api/attendance/alerts.php' from segments
$segments = array_slice($segments, 3);

/**
 * Send JSON response
 */
function sendJsonResponse($status, $data = null, $message = null) {
    http_response_code($status);
    echo json_encode([
        'success' => $status >= 200 && $status < 300,
        'data' => $data,
        'message' => $message,
        'timestamp' => date('Y-m-d H:i:s')
    ]);
    exit();
}

// Initialize AlertSystem
try {
    $alertSystem = new AlertSystem();
} catch (Exception $e) {
    sendJsonResponse(500, null, 'Service initialization failed');
}

// Handle the request based on method and endpoint
try {
    switch ($method) {
        case 'GET':
            if (isset($segments[0]) && $segments[0] === 'statistics') {
                // Get alert statistics
                getAlertStatistics();
            } elseif (isset($segments[0]) && $segments[0] === 'analyze' && isset($segments[1])) {
                // Analyze patterns for specific worker
                analyzeWorkerPatterns($segments[1]);
            } else {
                // Get active alerts
                getActiveAlerts();
            }
            break;
            
        case 'PUT':
        case 'PATCH':
            // Resolve alert
            if (isset($segments[0]) && is_numeric($segments[0]) && isset($segments[1]) && $segments[1] === 'resolve') {
                resolveAlert($segments[0]);
            } else {
                sendJsonResponse(400, null, 'Invalid alert resolution endpoint');
            }
            break;
            
        case 'POST':
            // Trigger pattern analysis for all workers or specific worker
            if (isset($segments[0]) && $segments[0] === 'analyze') {
                if (isset($segments[1]) && is_numeric($segments[1])) {
                    analyzeWorkerPatterns($segments[1]);
                } else {
                    analyzeAllWorkers();
                }
            } else {
                sendJsonResponse(400, null, 'Invalid endpoint');
            }
            break;
            
        default:
            sendJsonResponse(405, null, 'Method not allowed');
    }
    
} catch (Exception $e) {
    error_log("Alerts API error: " . $e->getMessage());
    sendJsonResponse(500, null, 'Internal server error');
}

/**
 * Get active alerts with filtering
 */
function getActiveAlerts() {
    global $alertSystem;
    
    try {
        $filters = [];
        
        if (isset($_GET['worker_id']) && !empty($_GET['worker_id'])) {
            $filters['worker_id'] = $_GET['worker_id'];
        }
        
        if (isset($_GET['severity']) && !empty($_GET['severity'])) {
            $filters['severity'] = $_GET['severity'];
        }
        
        if (isset($_GET['alert_type']) && !empty($_GET['alert_type'])) {
            $filters['alert_type'] = $_GET['alert_type'];
        }
        
        $alerts = $alertSystem->getActiveAlerts($filters);
        
        // Group alerts by severity for easier display
        $groupedAlerts = [
            'critical' => [],
            'high' => [],
            'medium' => [],
            'low' => []
        ];
        
        foreach ($alerts as $alert) {
            $groupedAlerts[$alert['severity']][] = $alert;
        }
        
        sendJsonResponse(200, [
            'alerts' => $alerts,
            'grouped_alerts' => $groupedAlerts,
            'total_count' => count($alerts),
            'severity_counts' => [
                'critical' => count($groupedAlerts['critical']),
                'high' => count($groupedAlerts['high']),
                'medium' => count($groupedAlerts['medium']),
                'low' => count($groupedAlerts['low'])
            ]
        ]);
        
    } catch (Exception $e) {
        error_log("Get active alerts error: " . $e->getMessage());
        sendJsonResponse(500, null, 'Failed to retrieve active alerts');
    }
}

/**
 * Get alert statistics
 */
function getAlertStatistics() {
    global $alertSystem;
    
    try {
        $filters = [];
        
        if (isset($_GET['start_date']) && !empty($_GET['start_date'])) {
            $filters['start_date'] = $_GET['start_date'];
        }
        
        if (isset($_GET['end_date']) && !empty($_GET['end_date'])) {
            $filters['end_date'] = $_GET['end_date'];
        }
        
        $statistics = $alertSystem->getAlertStatistics($filters);
        
        // Process statistics for better presentation
        $processedStats = [
            'total_alerts' => 0,
            'active_alerts' => 0,
            'severity_breakdown' => [
                'critical' => 0,
                'high' => 0,
                'medium' => 0,
                'low' => 0
            ],
            'type_breakdown' => []
        ];
        
        foreach ($statistics as $stat) {
            $processedStats['total_alerts'] += $stat['total_alerts'];
            $processedStats['active_alerts'] += $stat['active_alerts'];
            $processedStats['severity_breakdown']['critical'] += $stat['critical_alerts'];
            $processedStats['severity_breakdown']['high'] += $stat['high_alerts'];
            $processedStats['severity_breakdown']['medium'] += $stat['medium_alerts'];
            $processedStats['severity_breakdown']['low'] += $stat['low_alerts'];
            
            $processedStats['type_breakdown'][] = [
                'alert_type' => $stat['alert_type'],
                'count' => $stat['type_count']
            ];
        }
        
        sendJsonResponse(200, $processedStats);
        
    } catch (Exception $e) {
        error_log("Get alert statistics error: " . $e->getMessage());
        sendJsonResponse(500, null, 'Failed to retrieve alert statistics');
    }
}

/**
 * Analyze patterns for specific worker
 */
function analyzeWorkerPatterns($workerId) {
    global $alertSystem;
    
    try {
        if (!is_numeric($workerId)) {
            sendJsonResponse(400, null, 'Invalid worker ID');
            return;
        }
        
        // Check if worker exists
        $pdo = (new Database())->getConnection();
        $stmt = $pdo->prepare("SELECT id, name FROM workers WHERE id = ? AND deleted_at IS NULL");
        $stmt->execute([$workerId]);
        $worker = $stmt->fetch();
        
        if (!$worker) {
            sendJsonResponse(404, null, 'Worker not found');
            return;
        }
        
        // Analyze patterns
        $alertSystem->analyzeWorkerPatterns($workerId);
        
        // Get any new alerts generated
        $newAlerts = $alertSystem->getActiveAlerts(['worker_id' => $workerId]);
        
        sendJsonResponse(200, [
            'worker' => $worker,
            'analysis_completed' => true,
            'new_alerts' => $newAlerts,
            'alert_count' => count($newAlerts)
        ], 'Pattern analysis completed for worker');
        
    } catch (Exception $e) {
        error_log("Analyze worker patterns error: " . $e->getMessage());
        sendJsonResponse(500, null, 'Failed to analyze worker patterns');
    }
}

/**
 * Analyze patterns for all active workers
 */
function analyzeAllWorkers() {
    global $alertSystem;
    
    try {
        // Get all active workers
        $pdo = (new Database())->getConnection();
        $stmt = $pdo->query("
            SELECT id, name FROM workers 
            WHERE status = 'active' AND deleted_at IS NULL
        ");
        $workers = $stmt->fetchAll();
        
        $analysisResults = [];
        $totalNewAlerts = 0;
        
        foreach ($workers as $worker) {
            try {
                $alertsBefore = count($alertSystem->getActiveAlerts(['worker_id' => $worker['id']]));
                
                // Analyze patterns for this worker
                $alertSystem->analyzeWorkerPatterns($worker['id']);
                
                $alertsAfter = count($alertSystem->getActiveAlerts(['worker_id' => $worker['id']]));
                $newAlertsForWorker = $alertsAfter - $alertsBefore;
                
                $analysisResults[] = [
                    'worker_id' => $worker['id'],
                    'worker_name' => $worker['name'],
                    'new_alerts' => $newAlertsForWorker,
                    'status' => 'completed'
                ];
                
                $totalNewAlerts += $newAlertsForWorker;
                
            } catch (Exception $e) {
                $analysisResults[] = [
                    'worker_id' => $worker['id'],
                    'worker_name' => $worker['name'],
                    'new_alerts' => 0,
                    'status' => 'failed',
                    'error' => $e->getMessage()
                ];
            }
        }
        
        sendJsonResponse(200, [
            'total_workers_analyzed' => count($workers),
            'total_new_alerts' => $totalNewAlerts,
            'analysis_results' => $analysisResults,
            'completed_at' => date('Y-m-d H:i:s')
        ], 'Pattern analysis completed for all workers');
        
    } catch (Exception $e) {
        error_log("Analyze all workers error: " . $e->getMessage());
        sendJsonResponse(500, null, 'Failed to analyze patterns for all workers');
    }
}

/**
 * Resolve an alert
 */
function resolveAlert($alertId) {
    global $alertSystem;
    
    try {
        if (!is_numeric($alertId)) {
            sendJsonResponse(400, null, 'Invalid alert ID');
            return;
        }
        
        $data = json_decode(file_get_contents('php://input'), true);
        $resolvedBy = $data['resolved_by'] ?? 1; // TODO: Get from authenticated user
        $notes = $data['notes'] ?? null;
        
        $success = $alertSystem->resolveAlert($alertId, $resolvedBy, $notes);
        
        if ($success) {
            sendJsonResponse(200, null, 'Alert resolved successfully');
        } else {
            sendJsonResponse(404, null, 'Alert not found or already resolved');
        }
        
    } catch (Exception $e) {
        error_log("Resolve alert error: " . $e->getMessage());
        sendJsonResponse(500, null, 'Failed to resolve alert');
    }
}
