<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cardInvoiceItem"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="12dp"
    android:layout_marginVertical="6dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="2dp"
    app:cardBackgroundColor="@android:color/white">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Header Section -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="12dp">

            <!-- Invoice Title -->
            <TextView
                android:id="@+id/textInvoiceTitle"
                style="@style/TextAppearance.MaterialComponents.Headline6"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:maxLines="1"
                android:ellipsize="end"
                android:textColor="@color/primary_dark"
                tools:text="Office Supplies Order" />

            <!-- Status Badge -->
            <TextView
                android:id="@+id/textInvoiceStatus"
                style="@style/TextAppearance.MaterialComponents.Caption"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/bg_status_pending"
                android:paddingHorizontal="8dp"
                android:paddingVertical="2dp"
                android:textAllCaps="true"
                android:textColor="@android:color/white"
                tools:text="PENDING" />
        </LinearLayout>

        <!-- Invoice Details Section -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginBottom="16dp">

            <!-- Invoice Number -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="4dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/label_invoice_number"
                    android:textColor="@color/text_secondary"
                    android:textStyle="bold"/>
                <TextView
                    android:id="@+id/textInvoiceNumber"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:textColor="@color/text_primary"
                    tools:text="INV-2023-001" />
            </LinearLayout>

            <!-- Description -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="4dp">
                <ImageView
                    android:layout_width="16dp"
                    android:layout_height="16dp"
                    android:layout_gravity="center_vertical"
                    android:src="@drawable/ic_file"
                    app:tint="@color/icon_tint" />
                <TextView
                    android:id="@+id/textDescription"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:textColor="@color/text_primary"
                    tools:text="Sample description" />
            </LinearLayout>

            <!-- Location -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="4dp">
                <ImageView
                    android:layout_width="16dp"
                    android:layout_height="16dp"
                    android:layout_gravity="center_vertical"
                    android:src="@drawable/ic_location"
                    app:tint="@color/icon_tint" />
                <TextView
                    android:id="@+id/textLocation"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:maxLines="1"
                    android:ellipsize="end"
                    android:textColor="@color/text_secondary"
                    tools:text="Main Warehouse, Building A" />
            </LinearLayout>

            <!-- QR Code -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">
                <ImageView
                    android:layout_width="16dp"
                    android:layout_height="16dp"
                    android:layout_gravity="center_vertical"
                    android:src="@drawable/ic_qr_code"
                    app:tint="@color/icon_tint" />
                <TextView
                    android:id="@+id/textQRCode"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:textColor="@color/primary"
                    android:textStyle="bold"
                    tools:text="QR-7X9P2K" />
            </LinearLayout>

            <!-- QR -->
            <TextView
                android:id="@+id/textQR"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:textSize="14sp"
                android:textColor="@color/text_secondary"
                tools:text="QR-CODE-123" />
        </LinearLayout>

        <!-- Amount and Date Section -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="16dp">

            <!-- Amount -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/label_amount"
                    android:textColor="@color/text_secondary"
                    style="@style/TextAppearance.MaterialComponents.Caption"/>
                <TextView
                    android:id="@+id/textAmount"
                    style="@style/TextAppearance.MaterialComponents.Subtitle1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/primary_dark"
                    android:textStyle="bold"
                    tools:text="$1,250.00" />
            </LinearLayout>

            <!-- Invoice Date -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="end"
                android:orientation="vertical">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/label_date"
                    android:textColor="@color/text_secondary"
                    style="@style/TextAppearance.MaterialComponents.Caption"/>
                <TextView
                    android:id="@+id/textInvoiceDate"
                    style="@style/TextAppearance.MaterialComponents.Body2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    tools:text="Jun 17, 2023" />
            </LinearLayout>
        </LinearLayout>

        <!-- Action Buttons -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="end"
            android:paddingTop="8dp">

            <!-- Edit Button -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnEdit"
                style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="8dp"
                android:text="@string/action_edit"
                app:strokeColor="@color/primary"
                app:strokeWidth="1dp"
                app:rippleColor="@color/primary_light"
                app:iconTint="@color/primary"
                app:icon="@drawable/ic_edit"
                app:iconGravity="textStart"
                android:textColor="@color/primary" />

            <!-- Delete Button -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnDelete"
                style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/action_delete"
                app:strokeColor="@color/error"
                app:strokeWidth="1dp"
                app:rippleColor="@color/error"
                app:iconTint="@color/error"
                app:icon="@drawable/ic_delete"
                app:iconGravity="textStart"
                android:textColor="@color/error" />
        </LinearLayout>
    </LinearLayout>
</androidx.cardview.widget.CardView>
