<?php
require_once __DIR__ . '/../../vendor/autoload.php';
require_once __DIR__ . '/config.php'; // Assuming this contains DB connection and other configs

// Extend TCPDF to customize header/footer and table handling
class InvoicePDF extends TCPDF {
    // Store cell heights for row synchronization
    protected $cellHeights = [];
    
    // Page header
    public function Header() {
        // Logo or header content
        $this->SetFont('helvetica', 'B', 20);
        $this->Cell(0, 15, 'QUOTATION', 0, false, 'C', 0, '', 0, false, 'M', 'M');
        $this->Ln(8);
    }

    // Page footer
    public function Footer() {
        // Position at 15 mm from bottom
        $this->SetY(-15);
        $this->SetFont('helvetica', 'I', 8);
        // Page number
        $this->Cell(0, 10, 'Page '.$this->getAliasNumPage().'/'.$this->getAliasNbPages(), 0, false, 'C', 0, '', 0, false, 'T', 'M');
    }
    
    // Draw a table row with automatic height adjustment
    public function drawTableRow($data, $widths, $aligns, $border = 1, $fill = false, $padding = 2) {
        // Calculate the number of lines needed for each cell
        $nb = 0;
        $cell_data = [];
        
        // Calculate the number of lines for each cell
        for ($i = 0; $i < count($data); $i++) {
            $cell_data[$i] = $this->prepareCellContent($data[$i]);
            $nb = max($nb, $this->getNumLines($cell_data[$i], $widths[$i]));
        }
        
        // Calculate the row height based on the maximum number of lines
        $h = 5 * $nb + 2 * $padding;
        
        // Check if we need a page break
        if ($this->checkPageBreak($h)) {
            $this->AddPage();
        }
        
        // Draw each cell
        $x = $this->GetX();
        $y = $this->GetY();
        
        for ($i = 0; $i < count($data); $i++) {
            $this->SetXY($x, $y);
            $this->MultiCell($widths[$i], $h, $cell_data[$i], $border, $aligns[$i], $fill, 0, '', '', true, 0, false, true, $h, 'M', false);
            $x += $widths[$i];
        }
        
        $this->Ln($h);
    }
    
    // Prepare cell content with proper line breaks
    protected function prepareCellContent($content) {
        // Replace HTML line breaks with newlines
        $content = str_replace(['<br>', '<br/>', '<br />'], "\n", $content);
        // Remove any remaining HTML tags
        $content = strip_tags($content);
        // Convert HTML entities
        $content = html_entity_decode($content, ENT_QUOTES, 'UTF-8');
        return $content;
    }
    
    // Calculate the number of lines a MultiCell of width w will take
    // Note: Method signature must match parent TCPDF class
    public function getNumLines($txt, $w = 0, $reseth = false, $autopadding = true, $cellpadding = '', $border = 0) {
        // Calculate the number of lines needed for the text
        $cw = &$this->CurrentFont['cw'];
        $cellMargin = $this->getCellMargins();
        $effectiveWidth = $w - ($cellMargin['L'] + $cellMargin['R']) * $this->k;
        $maxchars = ($effectiveWidth > 0) ? ($effectiveWidth * 1000 / $this->FontSize) : 0;
        $nb = 0;
        $lines = explode("\n", $txt);
        
        foreach ($lines as $line) {
            $line = rtrim($line);
            if ($line === '') {
                $nb++;
                continue;
            }
            
            $words = explode(' ', $line);
            $line_width = 0;
            
            foreach ($words as $word) {
                $word_width = 0;
                $word_length = mb_strlen($word, 'UTF-8');
                
                for ($i = 0; $i < $word_length; $i++) {
                    $char = mb_substr($word, $i, 1, 'UTF-8');
                    $char_code = ord($char);
                    $char_width = $cw[$char] ?? $cw[chr($char_code)] ?? 600;
                    $word_width += $char_width;
                }
                
                if ($line_width + $word_width > $maxchars) {
                    $nb++;
                    $line_width = $word_width + ($cw[32] ?? 300); // Add space width
                } else {
                    $line_width += $word_width + ($cw[32] ?? 300); // Add space width
                }
                
                // Add space width (space character)
                $line_width += $cw[' '] ?? 300;
            }
            
            $nb++;
        }
        
        return $nb;
    }
}

// Create new PDF document
$pdf = new InvoicePDF(PDF_PAGE_ORIENTATION, PDF_UNIT, PDF_PAGE_FORMAT, true, 'UTF-8', false);

// Set document information
$pdf->SetCreator(PDF_CREATOR);
$pdf->SetAuthor('MTC Invoice System');
$pdf->SetTitle('Invoice');

// Set default header data
$pdf->SetHeaderData('', 0, 'INVOICE', '');

// Set header and footer fonts
$pdf->setHeaderFont(Array(PDF_FONT_NAME_MAIN, '', PDF_FONT_SIZE_MAIN));
$pdf->setFooterFont(Array(PDF_FONT_NAME_DATA, '', PDF_FONT_SIZE_DATA));

// Set default monospaced font
$pdf->SetDefaultMonospacedFont(PDF_FONT_MONOSPACED);

// Set margins
$pdf->SetMargins(15, 25, 15);
$pdf->SetHeaderMargin(PDF_MARGIN_HEADER);
$pdf->SetFooterMargin(PDF_MARGIN_FOOTER);

// Set auto page breaks
$pdf->SetAutoPageBreak(TRUE, PDF_MARGIN_BOTTOM);

// Set image scale factor
$pdf->setImageScale(PDF_IMAGE_SCALE_RATIO);

// Add a page
$pdf->AddPage();

// Get invoice data from POST
$invoiceData = json_decode(file_get_contents('php://input'), true);

if (!$invoiceData) {
    http_response_code(400);
    echo json_encode(['error' => 'No data received']);
    exit;
}

try {
    // Set font
    $pdf->SetFont('helvetica', '', 10);
    
    // Add invoice header
    $pdf->SetFont('helvetica', 'B', 16);
    $pdf->Cell(0, 10, 'QUOTATION', 0, 1, 'C');
    $pdf->Ln(5);
    
    // Company info
    $pdf->SetFont('helvetica', '', 10);
    $pdf->Cell(0, 6, 'MTC INTERNATIONAL', 0, 1, 'L');
    $pdf->Cell(0, 6, 'House: 35, Road: 04, Block: C, Banani, Dhaka-1213, Bangladesh', 0, 1, 'L');
    $pdf->Cell(0, 6, 'Phone: +88 02 550 895 25, +88 02 550 895 26', 0, 1, 'L');
    $pdf->Cell(0, 6, 'Email: <EMAIL>', 0, 1, 'L');
    
    $pdf->Ln(5);
    
    // Client info
    $pdf->SetFont('helvetica', 'B', 10);
    $pdf->Cell(40, 6, 'TO:', 0, 0, 'L');
    $pdf->SetFont('helvetica', '', 10);
    $pdf->Cell(0, 6, $invoiceData['client_name'] ?? '', 0, 1, 'L');
    
    if (!empty($invoiceData['client_address'])) {
        $pdf->SetX(50);
        $pdf->MultiCell(0, 6, $invoiceData['client_address'], 0, 'L');
    }
    
    $pdf->Ln(5);
    
    // Quotation info
    $pdf->SetFont('helvetica', '', 10);
    $pdf->Cell(40, 6, 'Quotation #:', 0, 0, 'L');
    $pdf->Cell(60, 6, $invoiceData['invoice_number'] ?? '', 0, 0, 'L');
    
    $pdf->Cell(40, 6, 'Date:', 0, 0, 'R');
    $pdf->Cell(0, 6, date('d-m-Y'), 0, 1, 'L');
    
    $pdf->Ln(5);
    
    // Table header
    $pdf->SetFont('helvetica', 'B', 10);
    $header = ['DESCRIPTION', 'QTY', 'UOM', 'AMOUNT'];
    $w = [120, 20, 20, 30]; // Column widths
    $align = ['L', 'C', 'C', 'R'];
    
    // Draw header row
    for ($i = 0; $i < count($header); $i++) {
        $pdf->Cell($w[$i], 10, $header[$i], 1, 0, $align[$i], true);
    }
    $pdf->Ln();
    
    // Table data
    $pdf->SetFont('helvetica', '', 9);
    $subtotal = 0;
    
    if (!empty($invoiceData['items'])) {
        foreach ($invoiceData['items'] as $item) {
            $total = $item['quantity'] * $item['price'];
            $subtotal += $total;
            
            $row = [
                $item['description'] ?? '',
                $item['quantity'] ?? '',
                'PCS', // Default UOM
                number_format($total, 2)
            ];
            
            // Draw row with automatic height adjustment
            $pdf->drawTableRow($row, $w, $align, 1, false, 3);
        }
    }
    
    // Draw totals
    $pdf->SetFont('helvetica', 'B', 10);
    $pdf->Cell($w[0] + $w[1] + $w[2], 10, 'Subtotal:', 1, 0, 'R', false);
    $pdf->Cell($w[3], 10, number_format($subtotal, 2), 1, 1, 'R', false);
    
    // Add tax if applicable
    if (!empty($invoiceData['tax_rate']) && $invoiceData['tax_rate'] > 0) {
        $tax = $subtotal * ($invoiceData['tax_rate'] / 100);
        $pdf->Cell($w[0] + $w[1] + $w[2], 10, 'VAT (' . $invoiceData['tax_rate'] . '%):', 1, 0, 'R', false);
        $pdf->Cell($w[3], 10, number_format($tax, 2), 1, 1, 'R', false);
        $total = $subtotal + $tax;
    } else {
        $total = $subtotal;
    }
    
    // Draw total
    $pdf->SetFont('helvetica', 'B', 11);
    $pdf->Cell($w[0] + $w[1] + $w[2], 12, 'TOTAL:', 1, 0, 'R', true);
    $pdf->Cell($w[3], 12, number_format($total, 2), 1, 1, 'R', true);
    
    // Add notes if any
    if (!empty($invoiceData['notes'])) {
        $pdf->Ln(8);
        $pdf->SetFont('helvetica', 'B', 10);
        $pdf->Cell(0, 6, 'Notes:', 0, 1, 'L');
        $pdf->SetFont('helvetica', '', 10);
        $pdf->MultiCell(0, 6, $invoiceData['notes'], 0, 'L');
    }
    
    // Close and output PDF document
    $pdf->Output('invoice_' . ($invoiceData['invoice_number'] ?? '') . '.pdf', 'I');
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'PDF generation failed: ' . $e->getMessage()]);
    exit;
}
