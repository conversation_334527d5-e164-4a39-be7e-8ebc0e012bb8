/**
 * PWA Manager for Attendance Management System
 * Handles service worker registration, app installation, and push notifications
 * 
 * @version 1.0
 */

class PWAManager {
    constructor() {
        this.deferredPrompt = null;
        this.isInstalled = false;
        this.swRegistration = null;
        this.notificationPermission = 'default';
        
        this.init();
    }
    
    async init() {
        try {
            // Check if PWA is already installed
            this.checkInstallStatus();
            
            // Register service worker
            await this.registerServiceWorker();
            
            // Setup installation prompt
            this.setupInstallPrompt();
            
            // Setup push notifications
            await this.setupPushNotifications();
            
            // Setup offline detection
            this.setupOfflineDetection();
            
            // Setup background sync
            this.setupBackgroundSync();
            
            console.log('PWA Manager initialized successfully');
            
        } catch (error) {
            console.error('PWA Manager initialization failed:', error);
        }
    }
    
    // Register service worker
    async registerServiceWorker() {
        if ('serviceWorker' in navigator) {
            try {
                this.swRegistration = await navigator.serviceWorker.register('/MtcInvoiceNewProject/sw.js', {
                    scope: '/MtcInvoiceNewProject/'
                });
                
                console.log('Service Worker registered:', this.swRegistration);
                
                // Handle service worker updates
                this.swRegistration.addEventListener('updatefound', () => {
                    const newWorker = this.swRegistration.installing;
                    
                    newWorker.addEventListener('statechange', () => {
                        if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                            this.showUpdateAvailable();
                        }
                    });
                });
                
                return this.swRegistration;
                
            } catch (error) {
                console.error('Service Worker registration failed:', error);
                throw error;
            }
        } else {
            throw new Error('Service Worker not supported');
        }
    }
    
    // Check if app is installed
    checkInstallStatus() {
        // Check if running in standalone mode
        this.isInstalled = window.matchMedia('(display-mode: standalone)').matches ||
                          window.navigator.standalone ||
                          document.referrer.includes('android-app://');
        
        if (this.isInstalled) {
            console.log('PWA is installed');
            this.hideInstallPrompt();
        }
    }
    
    // Setup installation prompt
    setupInstallPrompt() {
        // Listen for beforeinstallprompt event
        window.addEventListener('beforeinstallprompt', (e) => {
            console.log('Install prompt available');
            
            // Prevent the mini-infobar from appearing
            e.preventDefault();
            
            // Store the event for later use
            this.deferredPrompt = e;
            
            // Show custom install button
            this.showInstallPrompt();
        });
        
        // Listen for app installed event
        window.addEventListener('appinstalled', () => {
            console.log('PWA was installed');
            this.isInstalled = true;
            this.hideInstallPrompt();
            this.showInstallSuccess();
        });
    }
    
    // Show install prompt
    showInstallPrompt() {
        if (this.isInstalled) return;
        
        // Create install banner if it doesn't exist
        let installBanner = document.getElementById('pwa-install-banner');
        
        if (!installBanner) {
            installBanner = document.createElement('div');
            installBanner.id = 'pwa-install-banner';
            installBanner.className = 'pwa-install-banner';
            installBanner.innerHTML = `
                <div class="install-banner-content">
                    <div class="install-banner-icon">
                        <i class="bi bi-download"></i>
                    </div>
                    <div class="install-banner-text">
                        <h6>Install Attendance Manager</h6>
                        <p>Get quick access and work offline</p>
                    </div>
                    <div class="install-banner-actions">
                        <button class="btn btn-primary btn-sm" onclick="pwaManager.installApp()">
                            Install
                        </button>
                        <button class="btn btn-outline-secondary btn-sm" onclick="pwaManager.dismissInstallPrompt()">
                            Later
                        </button>
                    </div>
                </div>
            `;
            
            document.body.appendChild(installBanner);
        }
        
        // Show banner with animation
        setTimeout(() => {
            installBanner.classList.add('show');
        }, 1000);
    }
    
    // Install app
    async installApp() {
        if (!this.deferredPrompt) {
            console.log('No install prompt available');
            return;
        }
        
        try {
            // Show the install prompt
            this.deferredPrompt.prompt();
            
            // Wait for the user to respond
            const { outcome } = await this.deferredPrompt.userChoice;
            
            console.log(`User response to install prompt: ${outcome}`);
            
            if (outcome === 'accepted') {
                this.hideInstallPrompt();
            }
            
            // Clear the deferred prompt
            this.deferredPrompt = null;
            
        } catch (error) {
            console.error('App installation failed:', error);
        }
    }
    
    // Dismiss install prompt
    dismissInstallPrompt() {
        this.hideInstallPrompt();
        
        // Remember user dismissed (don't show again for 7 days)
        localStorage.setItem('pwa-install-dismissed', Date.now() + (7 * 24 * 60 * 60 * 1000));
    }
    
    // Hide install prompt
    hideInstallPrompt() {
        const installBanner = document.getElementById('pwa-install-banner');
        if (installBanner) {
            installBanner.classList.remove('show');
            setTimeout(() => {
                installBanner.remove();
            }, 300);
        }
    }
    
    // Show install success message
    showInstallSuccess() {
        this.showNotification('App installed successfully! You can now access it from your home screen.', 'success');
    }
    
    // Show update available notification
    showUpdateAvailable() {
        const updateBanner = document.createElement('div');
        updateBanner.className = 'pwa-update-banner';
        updateBanner.innerHTML = `
            <div class="update-banner-content">
                <div class="update-banner-text">
                    <h6>Update Available</h6>
                    <p>A new version of the app is ready</p>
                </div>
                <div class="update-banner-actions">
                    <button class="btn btn-primary btn-sm" onclick="pwaManager.updateApp()">
                        Update
                    </button>
                    <button class="btn btn-outline-secondary btn-sm" onclick="this.parentElement.parentElement.parentElement.remove()">
                        Later
                    </button>
                </div>
            </div>
        `;
        
        document.body.appendChild(updateBanner);
        
        setTimeout(() => {
            updateBanner.classList.add('show');
        }, 100);
    }
    
    // Update app
    updateApp() {
        if (this.swRegistration && this.swRegistration.waiting) {
            this.swRegistration.waiting.postMessage({ type: 'SKIP_WAITING' });
            
            // Reload page to activate new service worker
            window.location.reload();
        }
    }
    
    // Setup push notifications
    async setupPushNotifications() {
        if (!('Notification' in window) || !('serviceWorker' in navigator)) {
            console.log('Push notifications not supported');
            return;
        }
        
        // Check current permission
        this.notificationPermission = Notification.permission;
        
        if (this.notificationPermission === 'default') {
            // Show notification permission prompt after user interaction
            this.showNotificationPermissionPrompt();
        } else if (this.notificationPermission === 'granted') {
            await this.subscribeToPushNotifications();
        }
    }
    
    // Show notification permission prompt
    showNotificationPermissionPrompt() {
        // Only show if user hasn't dismissed recently
        const dismissed = localStorage.getItem('notification-permission-dismissed');
        if (dismissed && Date.now() < parseInt(dismissed)) {
            return;
        }
        
        setTimeout(() => {
            const permissionBanner = document.createElement('div');
            permissionBanner.className = 'pwa-notification-banner';
            permissionBanner.innerHTML = `
                <div class="notification-banner-content">
                    <div class="notification-banner-icon">
                        <i class="bi bi-bell"></i>
                    </div>
                    <div class="notification-banner-text">
                        <h6>Stay Updated</h6>
                        <p>Get notified about attendance alerts and updates</p>
                    </div>
                    <div class="notification-banner-actions">
                        <button class="btn btn-primary btn-sm" onclick="pwaManager.requestNotificationPermission()">
                            Allow
                        </button>
                        <button class="btn btn-outline-secondary btn-sm" onclick="pwaManager.dismissNotificationPrompt()">
                            Not Now
                        </button>
                    </div>
                </div>
            `;
            
            document.body.appendChild(permissionBanner);
            
            setTimeout(() => {
                permissionBanner.classList.add('show');
            }, 100);
        }, 5000); // Show after 5 seconds
    }
    
    // Request notification permission
    async requestNotificationPermission() {
        try {
            const permission = await Notification.requestPermission();
            this.notificationPermission = permission;
            
            if (permission === 'granted') {
                await this.subscribeToPushNotifications();
                this.showNotification('Notifications enabled! You\'ll receive attendance alerts.', 'success');
            }
            
            this.hideNotificationPrompt();
            
        } catch (error) {
            console.error('Notification permission request failed:', error);
        }
    }
    
    // Dismiss notification prompt
    dismissNotificationPrompt() {
        this.hideNotificationPrompt();
        
        // Remember user dismissed (don't show again for 30 days)
        localStorage.setItem('notification-permission-dismissed', Date.now() + (30 * 24 * 60 * 60 * 1000));
    }
    
    // Hide notification prompt
    hideNotificationPrompt() {
        const notificationBanner = document.querySelector('.pwa-notification-banner');
        if (notificationBanner) {
            notificationBanner.classList.remove('show');
            setTimeout(() => {
                notificationBanner.remove();
            }, 300);
        }
    }
    
    // Subscribe to push notifications
    async subscribeToPushNotifications() {
        if (!this.swRegistration) {
            console.log('Service worker not registered');
            return;
        }
        
        try {
            // Check if already subscribed
            let subscription = await this.swRegistration.pushManager.getSubscription();
            
            if (!subscription) {
                // Create new subscription
                const vapidPublicKey = 'YOUR_VAPID_PUBLIC_KEY'; // Replace with actual VAPID key
                
                subscription = await this.swRegistration.pushManager.subscribe({
                    userVisibleOnly: true,
                    applicationServerKey: this.urlBase64ToUint8Array(vapidPublicKey)
                });
            }
            
            // Send subscription to server
            await this.sendSubscriptionToServer(subscription);
            
            console.log('Push notification subscription successful');
            
        } catch (error) {
            console.error('Push notification subscription failed:', error);
        }
    }
    
    // Send subscription to server
    async sendSubscriptionToServer(subscription) {
        try {
            const response = await fetch('/MtcInvoiceNewProject/api/attendance/notifications.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: 'subscribe',
                    subscription: subscription
                })
            });
            
            if (!response.ok) {
                throw new Error('Failed to send subscription to server');
            }
            
        } catch (error) {
            console.error('Error sending subscription to server:', error);
        }
    }
    
    // Setup offline detection
    setupOfflineDetection() {
        window.addEventListener('online', () => {
            this.showNotification('You\'re back online! Syncing data...', 'success');
            this.syncOfflineData();
        });
        
        window.addEventListener('offline', () => {
            this.showNotification('You\'re offline. Changes will be synced when you reconnect.', 'warning');
        });
    }
    
    // Setup background sync
    setupBackgroundSync() {
        if ('serviceWorker' in navigator && 'sync' in window.ServiceWorkerRegistration.prototype) {
            // Register for background sync when data is queued
            window.addEventListener('attendance-data-queued', () => {
                navigator.serviceWorker.ready.then(registration => {
                    return registration.sync.register('attendance-sync');
                });
            });
        }
    }
    
    // Sync offline data
    async syncOfflineData() {
        if ('serviceWorker' in navigator && 'sync' in window.ServiceWorkerRegistration.prototype) {
            try {
                const registration = await navigator.serviceWorker.ready;
                await registration.sync.register('attendance-sync');
                console.log('Background sync registered');
            } catch (error) {
                console.error('Background sync registration failed:', error);
            }
        }
    }
    
    // Utility functions
    urlBase64ToUint8Array(base64String) {
        const padding = '='.repeat((4 - base64String.length % 4) % 4);
        const base64 = (base64String + padding)
            .replace(/-/g, '+')
            .replace(/_/g, '/');
        
        const rawData = window.atob(base64);
        const outputArray = new Uint8Array(rawData.length);
        
        for (let i = 0; i < rawData.length; ++i) {
            outputArray[i] = rawData.charCodeAt(i);
        }
        
        return outputArray;
    }
    
    // Show notification
    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `pwa-notification pwa-notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <div class="notification-icon">
                    <i class="bi bi-${this.getNotificationIcon(type)}"></i>
                </div>
                <div class="notification-message">${message}</div>
                <button class="notification-close" onclick="this.parentElement.parentElement.remove()">
                    <i class="bi bi-x"></i>
                </button>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        // Show with animation
        setTimeout(() => {
            notification.classList.add('show');
        }, 100);
        
        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (notification.parentElement) {
                notification.classList.remove('show');
                setTimeout(() => {
                    notification.remove();
                }, 300);
            }
        }, 5000);
    }
    
    // Get notification icon
    getNotificationIcon(type) {
        const icons = {
            success: 'check-circle',
            warning: 'exclamation-triangle',
            error: 'x-circle',
            info: 'info-circle'
        };
        
        return icons[type] || icons.info;
    }
    
    // Check if app should show install prompt
    shouldShowInstallPrompt() {
        if (this.isInstalled) return false;
        
        const dismissed = localStorage.getItem('pwa-install-dismissed');
        if (dismissed && Date.now() < parseInt(dismissed)) {
            return false;
        }
        
        return true;
    }
}

// Initialize PWA Manager when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.pwaManager = new PWAManager();
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PWAManager;
}
