package com.official.invoicegenarator.adapter;

import android.graphics.Color;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.cardview.widget.CardView;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.official.invoicegenarator.R;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

public class InvoiceDataAdapter extends RecyclerView.Adapter<InvoiceDataAdapter.ViewHolder> {

    private JSONArray items;
    private OnItemClickListener listener;

    public interface OnItemClickListener {
        void onEditClick(JSONObject item);
        void onDeleteClick(String id);
    }


    public InvoiceDataAdapter(JSONArray items, OnItemClickListener listener) {
        this.items = items != null ? items : new JSONArray();
        this.listener = listener;
    }

    public void updateData(JSONArray newItems) {
        this.items = newItems != null ? newItems : new JSONArray();
        notifyDataSetChanged();
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_invoice_data, parent, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        try {
            JSONObject item = items.getJSONObject(position);
            holder.bind(item, listener);
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    @Override
    public int getItemCount() {
        return items.length();
    }

    static class ViewHolder extends RecyclerView.ViewHolder {
        private final CardView cardInvoiceItem;
        private final TextView textInvoiceTitle;
        private final TextView textInvoiceStatus;
        private final TextView textInvoiceNumber;
        private final TextView textDescription;
        private final TextView textLocation;
        private final TextView textQRCode;
        private final TextView textQR;
        private final TextView textAmount;
        private final TextView textInvoiceDate;
        private final com.google.android.material.button.MaterialButton btnEdit;
        private final com.google.android.material.button.MaterialButton btnDelete;

        ViewHolder(View itemView) {
            super(itemView);
            cardInvoiceItem = itemView.findViewById(R.id.cardInvoiceItem);
            textInvoiceTitle = itemView.findViewById(R.id.textInvoiceTitle);
            textInvoiceStatus = itemView.findViewById(R.id.textInvoiceStatus);
            textInvoiceNumber = itemView.findViewById(R.id.textInvoiceNumber);
            textDescription = itemView.findViewById(R.id.textDescription);
            textLocation = itemView.findViewById(R.id.textLocation);
            textQRCode = itemView.findViewById(R.id.textQRCode);
            textQR = itemView.findViewById(R.id.textQR);
            textAmount = itemView.findViewById(R.id.textAmount);
            textInvoiceDate = itemView.findViewById(R.id.textInvoiceDate);
            btnEdit = itemView.findViewById(R.id.btnEdit);
            btnDelete = itemView.findViewById(R.id.btnDelete);
        }

        void bind(final JSONObject item, final OnItemClickListener listener) {
            try {
                // Set basic information
                textInvoiceTitle.setText(item.optString("title", "No Title"));
                textInvoiceNumber.setText(item.optString("invoice_number", "N/A"));
                textDescription.setText(item.optString("description", "No description"));
                textLocation.setText(item.optString("location", "No location"));
                textQRCode.setText(item.optString("qr_code", "QR-CODE"));
                textQR.setText(item.optString("qr_text", "Scan QR for details"));
                
                // Format and set amount
                double amount = item.optDouble("amount", 0.0);
                NumberFormat currencyFormat = NumberFormat.getCurrencyInstance(Locale.US);
                textAmount.setText(currencyFormat.format(amount));
                
                // Format and set date
                String dateStr = item.optString("date", "");
                if (!dateStr.isEmpty()) {
                    try {
                        SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy-MM-dd", Locale.US);
                        SimpleDateFormat outputFormat = new SimpleDateFormat("MMM dd, yyyy", Locale.US);
                        Date date = inputFormat.parse(dateStr);
                        textInvoiceDate.setText(outputFormat.format(date));
                    } catch (Exception e) {
                        textInvoiceDate.setText(dateStr);
                    }
                } else {
                    textInvoiceDate.setText("No date");
                }
                
                // Set status with appropriate color
                String status = item.optString("status", "PENDING").toUpperCase();
                textInvoiceStatus.setText(status);
                
                // Set status background color based on status
                int statusColor;
                switch (status) {
                    case "PAID":
                        statusColor = R.drawable.bg_status_paid;
                        break;
                    case "OVERDUE":
                        statusColor = R.drawable.bg_status_overdue;
                        break;
                    default: // PENDING
                        statusColor = R.drawable.bg_status_pending;
                }
                textInvoiceStatus.setBackgroundResource(statusColor);
                
                // Set click listeners
                btnEdit.setOnClickListener(v -> {
                    if (listener != null) {
                        listener.onEditClick(item);
                    }
                });

                btnDelete.setOnClickListener(v -> {
                    if (listener != null) {
                        listener.onDeleteClick(item.optString("id"));
                    }
                });
                
                // Set card click listener
                itemView.setOnClickListener(v -> {
                    // Handle item click if needed
                });
                
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }
}
