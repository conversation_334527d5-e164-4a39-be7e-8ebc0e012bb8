package com.official.invoicegenarator;

import androidx.appcompat.app.AlertDialog;
import com.google.android.material.dialog.MaterialAlertDialogBuilder;
import java.util.Iterator;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.ProgressBar;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.airbnb.lottie.LottieAnimationView;
import com.google.android.material.dialog.MaterialAlertDialogBuilder;
import com.official.invoicegenarator.adapter.InvoiceDataAdapter;
import com.official.invoicegenarator.utils.ApiClient;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class UpdateDeleteFragment extends Fragment implements 
        InvoiceDataAdapter.OnItemClickListener,
        SwipeRefreshLayout.OnRefreshListener {

    private RecyclerView recyclerView;
    private ProgressBar progressBar;
    private EditText searchBar;
    private LottieAnimationView lottieAnimationView;
    private SwipeRefreshLayout swipeRefreshLayout;
    
    private InvoiceDataAdapter adapter;
    private ApiClient apiClient;
    private List<JSONObject> allItems = new ArrayList<>();

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_update_delete, container, false);

        // Initialize API client
        apiClient = ApiClient.getInstance(requireContext());
        
        // Initialize views
        initializeViews(view);
        setupRecyclerView();
        setupSearch();
        
        // Load data
        loadData();
        
        return view;
    }
    
    private void initializeViews(View view) {
        recyclerView = view.findViewById(R.id.recyclerView);
        progressBar = view.findViewById(R.id.progressBar);
        searchBar = view.findViewById(R.id.search_bar);
        lottieAnimationView = view.findViewById(R.id.lottieAnimationView);
        swipeRefreshLayout = view.findViewById(R.id.swipeRefreshLayout);
        
        // Setup swipe to refresh
        swipeRefreshLayout.setOnRefreshListener(this);
    }
    
    private void setupRecyclerView() {
        recyclerView.setLayoutManager(new LinearLayoutManager(getContext()));
        adapter = new InvoiceDataAdapter(null, this);
        recyclerView.setAdapter(adapter);
    }
    
    private void setupSearch() {
        searchBar.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {}

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                filterItems(s.toString());
            }

            @Override
            public void afterTextChanged(Editable s) {}
        });
    }
    
    private void filterItems(String query) {
        if (query.isEmpty()) {
            updateAdapter(new JSONArray(allItems));
            return;
        }
        
        List<JSONObject> filteredList = new ArrayList<>();
        String lowerQuery = query.toLowerCase();
        
        for (JSONObject item : allItems) {
            try {
                String description = item.optString("description", "").toLowerCase();
                String location = item.optString("location", "").toLowerCase();
                String qr = item.optString("qr", "").toLowerCase();
                
                if (description.contains(lowerQuery) || 
                    location.contains(lowerQuery) || 
                    qr.contains(lowerQuery)) {
                    filteredList.add(item);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        
        updateAdapter(new JSONArray(filteredList));
    }
    
    private void updateAdapter(JSONArray items) {
        adapter.updateData(items);
        updateEmptyState();
    }
    
    private void updateEmptyState() {
        if (adapter.getItemCount() == 0) {
            lottieAnimationView.setVisibility(View.VISIBLE);
            recyclerView.setVisibility(View.GONE);
        } else {
            lottieAnimationView.setVisibility(View.GONE);
            recyclerView.setVisibility(View.VISIBLE);
        }
    }
    
    private void loadData() {
        showLoading(true);
        
        apiClient.getInvoiceDataItems(new ApiClient.ApiResponseListener() {
            @Override
            public void onSuccess(JSONObject response) {
                requireActivity().runOnUiThread(() -> {
                    try {
                        JSONArray data = response.getJSONArray("data");
                        allItems.clear();
                        
                        // Convert JSONArray to List<JSONObject>
                        for (int i = 0; i < data.length(); i++) {
                            allItems.add(data.getJSONObject(i));
                        }
                        
                        updateAdapter(data);
                        showLoading(false);
                    } catch (JSONException e) {
                        showError("Error parsing data");
                    }
                });
            }

            @Override
            public void onError(String message) {
                requireActivity().runOnUiThread(() -> showError(message));
            }
        });
    }
    
    private void showLoading(boolean show) {
        if (swipeRefreshLayout != null && swipeRefreshLayout.isRefreshing()) {
            swipeRefreshLayout.setRefreshing(show);
        } else {
            progressBar.setVisibility(show ? View.VISIBLE : View.GONE);
            recyclerView.setVisibility(show ? View.GONE : View.VISIBLE);
        }
    }
    
    private void showError(String message) {
        showLoading(false);
        Toast.makeText(requireContext(), "Error: " + message, Toast.LENGTH_LONG).show();
    }

    @Override
    public void onRefresh() {
        loadData();
    }

    @Override
    public void onEditClick(JSONObject item) {
        showEditDialog(item);
    }

    @Override
    public void onDeleteClick(String id) {
        showDeleteConfirmation(id);
    }
    
    private void showEditDialog(JSONObject item) {
        try {
            View dialogView = getLayoutInflater().inflate(R.layout.dialog_edit_invoice, null);
            
            // Initialize views
            EditText etDescription = dialogView.findViewById(R.id.etDescription);
            EditText etLocation = dialogView.findViewById(R.id.etLocation);
            EditText etQR = dialogView.findViewById(R.id.etQR);
            EditText etLPO = dialogView.findViewById(R.id.etLPO);
            EditText etINB = dialogView.findViewById(R.id.etINB);
            EditText etAmount = dialogView.findViewById(R.id.etAmount);
            EditText etWA = dialogView.findViewById(R.id.etWA);
            EditText etPaymentStatus = dialogView.findViewById(R.id.etPaymentStatus);
            
            // Set current values
            etDescription.setText(item.optString("description", ""));
            etLocation.setText(item.optString("location", ""));
            etQR.setText(item.optString("qr", ""));
            etLPO.setText(item.optString("lpo", ""));
            etINB.setText(item.optString("inb", ""));
            etAmount.setText(item.optString("amount", ""));
            etWA.setText(item.optString("w_a", ""));
            etPaymentStatus.setText(item.optString("payment_status", ""));
            
            AlertDialog dialog = new MaterialAlertDialogBuilder(requireContext())
                    .setTitle("Edit Invoice Item")
                    .setView(dialogView)
                    .setPositiveButton("Update", (d, which) -> {
                        // Validate required fields
                        String description = etDescription.getText().toString().trim();
                        String location = etLocation.getText().toString().trim();
                        String qr = etQR.getText().toString().trim();
                        
                        if (description.isEmpty() || location.isEmpty() || qr.isEmpty()) {
                            Toast.makeText(requireContext(), "Please fill in all required fields", Toast.LENGTH_SHORT).show();
                            return;
                        }
                    })
                    .setNegativeButton("Cancel", null)
                    .create();
                    
            dialog.show();
        } catch (Exception e) {
            e.printStackTrace();
            Toast.makeText(requireContext(), "Error showing edit dialog", Toast.LENGTH_SHORT).show();
        }
    }
    
    private void updateItem(String id, JSONObject data) {
        showLoading(true);
        
        apiClient.updateInvoiceDataItem(Integer.parseInt(id), dataToMap(data), new ApiClient.ApiResponseListener() {
            @Override
            public void onSuccess(JSONObject response) {
                requireActivity().runOnUiThread(() -> {
                    Toast.makeText(requireContext(), "Item updated successfully", Toast.LENGTH_SHORT).show();
                    loadData(); // Refresh the list
                });
            }

            @Override
            public void onError(String message) {
                requireActivity().runOnUiThread(() -> {
                    showLoading(false);
                    Toast.makeText(requireContext(), "Update failed: " + message, Toast.LENGTH_LONG).show();
                });
            }
        });
    }
    
    private void showDeleteConfirmation(String id) {
        new MaterialAlertDialogBuilder(requireContext())
                .setTitle("Delete Item")
                .setMessage("Are you sure you want to delete this item?")
                .setPositiveButton("Delete", (dialog, which) -> deleteItem(id))
                .setNegativeButton("Cancel", null)
                .show();
    }
    
    private void deleteItem(String id) {
        showLoading(true);
        
        apiClient.deleteInvoiceDataItem(Integer.parseInt(id), new ApiClient.ApiResponseListener() {
            @Override
            public void onSuccess(JSONObject response) {
                requireActivity().runOnUiThread(() -> {
                    Toast.makeText(requireContext(), "Item deleted successfully", Toast.LENGTH_SHORT).show();
                    loadData(); // Refresh the list
                });
            }

            @Override
            public void onError(String message) {
                requireActivity().runOnUiThread(() -> {
                    showLoading(false);
                    Toast.makeText(requireContext(), "Delete failed: " + message, Toast.LENGTH_LONG).show();
                });
            }
        });
    }
    
    private Map<String, String> dataToMap(JSONObject jsonObject) {
        Map<String, String> map = new HashMap<>();
        if (jsonObject != null) {
            Iterator<String> keys = jsonObject.keys();
            while (keys.hasNext()) {
                String key = keys.next();
                try {
                    map.put(key, jsonObject.getString(key));
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }
        }
        return map;
    }
}
