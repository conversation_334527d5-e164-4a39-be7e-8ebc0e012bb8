<?php
/**
 * Workers API Endpoint
 * 
 * @package MtcInvoice Attendance
 * @version 1.0
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once '../config/config.php';
require_once '../../classes/WorkerManager.php';

// Get request method and path
$method = $_SERVER['REQUEST_METHOD'];
$path = $_SERVER['REQUEST_URI'];
$segments = explode('/', trim(parse_url($path, PHP_URL_PATH), '/'));

// Remove 'api/attendance/workers.php' from segments
$segments = array_slice($segments, 3);

/**
 * Send JSON response
 */
function sendJsonResponse($status, $data = null, $message = null) {
    http_response_code($status);
    echo json_encode([
        'success' => $status >= 200 && $status < 300,
        'data' => $data,
        'message' => $message,
        'timestamp' => date('Y-m-d H:i:s')
    ]);
    exit();
}

/**
 * Validate required fields
 */
function validateRequired($data, $fields) {
    $missing = [];
    foreach ($fields as $field) {
        if (!isset($data[$field]) || empty(trim($data[$field]))) {
            $missing[] = $field;
        }
    }
    return $missing;
}

/**
 * Sanitize input data
 */
function sanitizeInput($data) {
    if (is_array($data)) {
        return array_map('sanitizeInput', $data);
    }
    return htmlspecialchars(strip_tags(trim($data)), ENT_QUOTES, 'UTF-8');
}

// Initialize WorkerManager
try {
    $workerManager = new WorkerManager();
} catch (Exception $e) {
    sendJsonResponse(500, null, 'Service initialization failed');
}

// Handle the request based on method and endpoint
try {
    switch ($method) {
        case 'GET':
            if (isset($segments[0]) && is_numeric($segments[0])) {
                // Get single worker
                getWorker($segments[0]);
            } elseif (isset($segments[0]) && $segments[0] === 'departments') {
                // Get departments list
                getDepartments();
            } elseif (isset($segments[0]) && $segments[0] === 'managers') {
                // Get managers list
                getManagers();
            } else {
                // Get workers list
                getWorkers();
            }
            break;
            
        case 'POST':
            // Create new worker
            createWorker();
            break;
            
        case 'PUT':
        case 'PATCH':
            // Update existing worker
            if (isset($segments[0]) && is_numeric($segments[0])) {
                updateWorker($segments[0]);
            } else {
                sendJsonResponse(400, null, 'Worker ID is required for update');
            }
            break;
            
        case 'DELETE':
            // Delete worker
            if (isset($segments[0]) && is_numeric($segments[0])) {
                deleteWorker($segments[0]);
            } else {
                sendJsonResponse(400, null, 'Worker ID is required for deletion');
            }
            break;
            
        default:
            sendJsonResponse(405, null, 'Method not allowed');
    }
} catch (Exception $e) {
    error_log("Workers API error: " . $e->getMessage());
    sendJsonResponse(500, null, 'Internal server error');
}

/**
 * Get all workers with optional filtering
 */
function getWorkers() {
    global $workerManager;
    
    try {
        $filters = [];
        
        // Apply filters from query parameters
        if (isset($_GET['department']) && !empty($_GET['department'])) {
            $filters['department'] = $_GET['department'];
        }
        
        if (isset($_GET['status']) && !empty($_GET['status'])) {
            $filters['status'] = $_GET['status'];
        }
        
        if (isset($_GET['manager_id']) && !empty($_GET['manager_id'])) {
            $filters['manager_id'] = $_GET['manager_id'];
        }
        
        if (isset($_GET['search']) && !empty($_GET['search'])) {
            $filters['search'] = $_GET['search'];
        }
        
        // Pagination
        if (isset($_GET['page']) && !empty($_GET['page'])) {
            $filters['page'] = $_GET['page'];
        }
        
        if (isset($_GET['limit']) && !empty($_GET['limit'])) {
            $filters['limit'] = $_GET['limit'];
        }
        
        $result = $workerManager->getWorkers($filters);
        sendJsonResponse(200, $result);
        
    } catch (Exception $e) {
        error_log("Get workers error: " . $e->getMessage());
        sendJsonResponse(500, null, 'Failed to retrieve workers');
    }
}

/**
 * Get single worker by ID
 */
function getWorker($id) {
    global $workerManager;
    
    try {
        $worker = $workerManager->getWorkerById($id);
        
        if (!$worker) {
            sendJsonResponse(404, null, 'Worker not found');
            return;
        }
        
        sendJsonResponse(200, $worker);
        
    } catch (Exception $e) {
        error_log("Get worker error: " . $e->getMessage());
        sendJsonResponse(500, null, 'Failed to retrieve worker');
    }
}

/**
 * Get departments list
 */
function getDepartments() {
    global $workerManager;
    
    try {
        $departments = $workerManager->getDepartments();
        sendJsonResponse(200, $departments);
        
    } catch (Exception $e) {
        error_log("Get departments error: " . $e->getMessage());
        sendJsonResponse(500, null, 'Failed to retrieve departments');
    }
}

/**
 * Get managers list
 */
function getManagers() {
    global $workerManager;
    
    try {
        $managers = $workerManager->getManagers();
        sendJsonResponse(200, $managers);
        
    } catch (Exception $e) {
        error_log("Get managers error: " . $e->getMessage());
        sendJsonResponse(500, null, 'Failed to retrieve managers');
    }
}

/**
 * Create new worker
 */
function createWorker() {
    global $workerManager;
    
    try {
        $data = json_decode(file_get_contents('php://input'), true);
        
        if (empty($data)) {
            sendJsonResponse(400, null, 'No data provided');
            return;
        }
        
        // Validate required fields
        $required = ['employee_id', 'name', 'department', 'position'];
        $missing = validateRequired($data, $required);
        if (!empty($missing)) {
            sendJsonResponse(400, null, 'Missing required fields: ' . implode(', ', $missing));
            return;
        }
        
        // Sanitize input
        $data = sanitizeInput($data);
        
        // Add created_by from session (TODO: implement proper authentication)
        $data['created_by'] = 1;
        
        $worker = $workerManager->createWorker($data);
        sendJsonResponse(201, $worker, 'Worker created successfully');
        
    } catch (Exception $e) {
        error_log("Create worker error: " . $e->getMessage());
        
        if (strpos($e->getMessage(), 'Employee ID already exists') !== false) {
            sendJsonResponse(409, null, $e->getMessage());
        } else {
            sendJsonResponse(500, null, 'Failed to create worker');
        }
    }
}

/**
 * Update existing worker
 */
function updateWorker($id) {
    global $workerManager;
    
    try {
        $data = json_decode(file_get_contents('php://input'), true);

        if (empty($data)) {
            sendJsonResponse(400, null, 'No data provided for update');
            return;
        }

        // Sanitize input
        $data = sanitizeInput($data);

        $worker = $workerManager->updateWorker($id, $data);
        sendJsonResponse(200, $worker, 'Worker updated successfully');

    } catch (Exception $e) {
        error_log("Update worker error: " . $e->getMessage());
        
        if (strpos($e->getMessage(), 'Worker not found') !== false) {
            sendJsonResponse(404, null, $e->getMessage());
        } elseif (strpos($e->getMessage(), 'Employee ID already exists') !== false) {
            sendJsonResponse(409, null, $e->getMessage());
        } else {
            sendJsonResponse(500, null, 'Failed to update worker');
        }
    }
}

/**
 * Delete worker (soft delete)
 */
function deleteWorker($id) {
    global $workerManager;
    
    try {
        $success = $workerManager->deleteWorker($id);
        
        if ($success) {
            sendJsonResponse(200, null, 'Worker deleted successfully');
        } else {
            sendJsonResponse(404, null, 'Worker not found');
        }

    } catch (Exception $e) {
        error_log("Delete worker error: " . $e->getMessage());
        sendJsonResponse(500, null, 'Failed to delete worker');
    }
}
