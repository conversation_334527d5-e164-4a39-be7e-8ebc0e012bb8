<?php
/**
 * Database Setup for Attendance System
 * Run this once to create the attendance tables
 */

require_once '../config/config.php';

try {
    $pdo = getDbConnection();
    
    // Read and execute the SQL schema
    $sql = file_get_contents('../../database/attendance_schema.sql');
    
    // Split SQL into individual statements
    $statements = array_filter(
        array_map('trim', explode(';', $sql)),
        function($stmt) {
            return !empty($stmt) && 
                   !preg_match('/^(--|\/\*|\s*$)/', $stmt) &&
                   !preg_match('/^(DELIMITER|CREATE EVENT|SET GLOBAL)/', $stmt);
        }
    );
    
    echo "<h2>Setting up Attendance Database Tables</h2>";
    echo "<div style='font-family: monospace; background: #f5f5f5; padding: 20px; border-radius: 5px;'>";
    
    $success_count = 0;
    $error_count = 0;
    
    foreach ($statements as $statement) {
        try {
            $pdo->exec($statement);
            $success_count++;
            
            // Extract table/view name for display
            if (preg_match('/CREATE\s+(TABLE|VIEW|PROCEDURE)\s+(?:IF\s+NOT\s+EXISTS\s+)?(?:OR\s+REPLACE\s+)?`?(\w+)`?/i', $statement, $matches)) {
                echo "<p style='color: green;'>✓ Created {$matches[1]}: {$matches[2]}</p>";
            } elseif (preg_match('/INSERT\s+INTO\s+`?(\w+)`?/i', $statement, $matches)) {
                echo "<p style='color: blue;'>✓ Inserted data into: {$matches[1]}</p>";
            } elseif (preg_match('/CREATE\s+INDEX\s+(\w+)/i', $statement, $matches)) {
                echo "<p style='color: orange;'>✓ Created index: {$matches[1]}</p>";
            } else {
                echo "<p style='color: gray;'>✓ Executed statement</p>";
            }
            
        } catch (PDOException $e) {
            $error_count++;
            echo "<p style='color: red;'>✗ Error: " . $e->getMessage() . "</p>";
            echo "<p style='color: red; margin-left: 20px; font-size: 12px;'>" . substr($statement, 0, 100) . "...</p>";
        }
    }
    
    echo "</div>";
    
    echo "<h3>Setup Summary</h3>";
    echo "<p><strong>Successful operations:</strong> $success_count</p>";
    echo "<p><strong>Errors:</strong> $error_count</p>";
    
    if ($error_count == 0) {
        echo "<div style='background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h4>✓ Database setup completed successfully!</h4>";
        echo "<p>You can now use the attendance management system.</p>";
        echo "<p><a href='index.php' style='color: #155724; font-weight: bold;'>Go to Attendance Dashboard</a></p>";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h4>⚠ Setup completed with errors</h4>";
        echo "<p>Some operations failed. Please check the errors above and try again.</p>";
        echo "</div>";
    }
    
    // Test the setup by checking tables
    echo "<h3>Verification</h3>";
    echo "<div style='font-family: monospace; background: #f5f5f5; padding: 20px; border-radius: 5px;'>";
    
    $tables = ['workers', 'attendance_records', 'attendance_alerts', 'departments', 'attendance_settings'];
    
    foreach ($tables as $table) {
        try {
            $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM `$table`");
            $stmt->execute();
            $count = $stmt->fetch()['count'];
            echo "<p style='color: green;'>✓ Table '$table': $count records</p>";
        } catch (PDOException $e) {
            echo "<p style='color: red;'>✗ Table '$table': " . $e->getMessage() . "</p>";
        }
    }
    
    echo "</div>";
    
} catch (Exception $e) {
    echo "<h3 style='color: red;'>Setup Failed</h3>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
    echo "<p>Please check your database configuration and try again.</p>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    background-color: #f8f9fa;
}

h2, h3 {
    color: #333;
    border-bottom: 2px solid #007bff;
    padding-bottom: 10px;
}

p {
    margin: 5px 0;
    line-height: 1.4;
}
</style>
