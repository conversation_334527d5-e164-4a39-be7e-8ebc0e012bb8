<?php
/**
 * Attendance Export API
 * 
 * @package MtcInvoice API
 * @version 1.0
 */

require_once '../../config/config.php';

// Set headers for API response
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

// Require authentication
requireAuth();

try {
    $method = $_SERVER['REQUEST_METHOD'];
    
    if ($method !== 'GET') {
        throw new Exception('Method not allowed', 405);
    }
    
    // Get parameters
    $format = $_GET['format'] ?? 'csv';
    $start_date = $_GET['start_date'] ?? date('Y-m-01');
    $end_date = $_GET['end_date'] ?? date('Y-m-t');
    $department = $_GET['department'] ?? '';
    $date = $_GET['date'] ?? '';
    
    // Validate format
    if (!in_array($format, ['csv', 'excel', 'pdf'])) {
        throw new Exception('Invalid export format');
    }
    
    // Get attendance data based on parameters
    if ($date) {
        // Single date export
        $query_params = ['date' => $date];
        if ($department) $query_params['department'] = $department;
        
        $query_string = http_build_query($query_params);
        $response = makeApiRequest("/api/attendance/attendance.php?{$query_string}");
        $attendance_data = $response['data']['attendance'] ?? [];
        $filename = "attendance_" . $date;
        
    } else {
        // Date range export
        $query_params = [
            'start_date' => $start_date,
            'end_date' => $end_date
        ];
        if ($department) $query_params['department'] = $department;
        
        $query_string = http_build_query($query_params);
        $response = makeApiRequest("/api/attendance/attendance.php/report?{$query_string}");
        $report_data = $response['data'] ?? [];
        $attendance_data = $report_data['worker_statistics'] ?? [];
        $filename = "attendance_report_" . $start_date . "_to_" . $end_date;
    }
    
    if (empty($attendance_data)) {
        throw new Exception('No data found for export');
    }
    
    // Export based on format
    switch ($format) {
        case 'csv':
            exportCSV($attendance_data, $filename, $date ? 'daily' : 'report');
            break;
        case 'excel':
            exportExcel($attendance_data, $filename, $date ? 'daily' : 'report');
            break;
        case 'pdf':
            exportPDF($attendance_data, $filename, $date ? 'daily' : 'report');
            break;
    }
    
} catch (Exception $e) {
    http_response_code($e->getCode() ?: 500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

function exportCSV($data, $filename, $type) {
    header('Content-Type: text/csv');
    header('Content-Disposition: attachment; filename="' . $filename . '.csv"');
    
    $output = fopen('php://output', 'w');
    
    if ($type === 'daily') {
        // Daily attendance export
        fputcsv($output, [
            'Employee ID',
            'Worker Name',
            'Department',
            'Date',
            'Status',
            'Check In',
            'Check Out',
            'Break Duration',
            'Overtime Hours',
            'Notes'
        ]);
        
        foreach ($data as $record) {
            fputcsv($output, [
                $record['employee_id'] ?? '',
                $record['worker_name'] ?? '',
                $record['department'] ?? '',
                $record['attendance_date'] ?? '',
                $record['status'] ?? '',
                $record['check_in_time'] ?? '',
                $record['check_out_time'] ?? '',
                $record['break_duration'] ?? '',
                $record['overtime_hours'] ?? '',
                $record['notes'] ?? ''
            ]);
        }
    } else {
        // Report export
        fputcsv($output, [
            'Employee ID',
            'Worker Name',
            'Department',
            'Total Days',
            'Present Days',
            'Absent Days',
            'Late Days',
            'Half Days',
            'Total Overtime',
            'Attendance Percentage'
        ]);
        
        foreach ($data as $worker) {
            fputcsv($output, [
                $worker['employee_id'] ?? '',
                $worker['name'] ?? '',
                $worker['department'] ?? '',
                $worker['total_days'] ?? 0,
                $worker['present_days'] ?? 0,
                $worker['absent_days'] ?? 0,
                $worker['late_days'] ?? 0,
                $worker['half_days'] ?? 0,
                $worker['total_overtime'] ?? 0,
                $worker['attendance_percentage'] ?? 0
            ]);
        }
    }
    
    fclose($output);
    exit;
}

function exportExcel($data, $filename, $type) {
    // For now, export as CSV with Excel MIME type
    // In a real implementation, you would use a library like PhpSpreadsheet
    header('Content-Type: application/vnd.ms-excel');
    header('Content-Disposition: attachment; filename="' . $filename . '.xls"');
    
    exportCSV($data, $filename, $type);
}

function exportPDF($data, $filename, $type) {
    // For now, return a simple HTML that can be printed as PDF
    // In a real implementation, you would use a library like TCPDF or DOMPDF
    header('Content-Type: text/html');
    
    echo '<!DOCTYPE html>
    <html>
    <head>
        <title>' . htmlspecialchars($filename) . '</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            table { width: 100%; border-collapse: collapse; margin-top: 20px; }
            th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
            th { background-color: #f2f2f2; font-weight: bold; }
            .header { text-align: center; margin-bottom: 20px; }
            .print-only { display: none; }
            @media print {
                .print-only { display: block; }
                .no-print { display: none; }
            }
        </style>
    </head>
    <body>
        <div class="header">
            <h1>Attendance Report</h1>
            <p>Generated on: ' . date('Y-m-d H:i:s') . '</p>
        </div>
        
        <div class="no-print">
            <button onclick="window.print()">Print / Save as PDF</button>
        </div>
        
        <table>';
    
    if ($type === 'daily') {
        echo '<thead>
            <tr>
                <th>Employee ID</th>
                <th>Worker Name</th>
                <th>Department</th>
                <th>Date</th>
                <th>Status</th>
                <th>Check In</th>
                <th>Check Out</th>
                <th>Overtime</th>
            </tr>
        </thead>
        <tbody>';
        
        foreach ($data as $record) {
            echo '<tr>
                <td>' . htmlspecialchars($record['employee_id'] ?? '') . '</td>
                <td>' . htmlspecialchars($record['worker_name'] ?? '') . '</td>
                <td>' . htmlspecialchars($record['department'] ?? '') . '</td>
                <td>' . htmlspecialchars($record['attendance_date'] ?? '') . '</td>
                <td>' . htmlspecialchars($record['status'] ?? '') . '</td>
                <td>' . htmlspecialchars($record['check_in_time'] ?? '') . '</td>
                <td>' . htmlspecialchars($record['check_out_time'] ?? '') . '</td>
                <td>' . htmlspecialchars($record['overtime_hours'] ?? '') . '</td>
            </tr>';
        }
    } else {
        echo '<thead>
            <tr>
                <th>Employee ID</th>
                <th>Worker Name</th>
                <th>Department</th>
                <th>Total Days</th>
                <th>Present</th>
                <th>Absent</th>
                <th>Late</th>
                <th>Half Days</th>
                <th>Attendance %</th>
            </tr>
        </thead>
        <tbody>';
        
        foreach ($data as $worker) {
            echo '<tr>
                <td>' . htmlspecialchars($worker['employee_id'] ?? '') . '</td>
                <td>' . htmlspecialchars($worker['name'] ?? '') . '</td>
                <td>' . htmlspecialchars($worker['department'] ?? '') . '</td>
                <td>' . ($worker['total_days'] ?? 0) . '</td>
                <td>' . ($worker['present_days'] ?? 0) . '</td>
                <td>' . ($worker['absent_days'] ?? 0) . '</td>
                <td>' . ($worker['late_days'] ?? 0) . '</td>
                <td>' . ($worker['half_days'] ?? 0) . '</td>
                <td>' . ($worker['attendance_percentage'] ?? 0) . '%</td>
            </tr>';
        }
    }
    
    echo '</tbody>
        </table>
    </body>
    </html>';
    
    exit;
}

// Helper function to make internal API requests
function makeApiRequest($endpoint, $method = 'GET', $data = null) {
    $base_url = 'http://' . $_SERVER['HTTP_HOST'];
    $url = $base_url . $endpoint;
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
    
    if ($data && in_array($method, ['POST', 'PUT', 'PATCH'])) {
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
    }
    
    // Copy session cookies for authentication
    if (isset($_COOKIE[session_name()])) {
        curl_setopt($ch, CURLOPT_COOKIE, session_name() . '=' . $_COOKIE[session_name()]);
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($httpCode >= 400) {
        throw new Exception('API request failed with status ' . $httpCode);
    }
    
    return json_decode($response, true);
}
?>
