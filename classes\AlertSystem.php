<?php
/**
 * Alert System Class
 * 
 * Handles attendance pattern detection, alert generation, and notifications.
 * Monitors worker attendance patterns and generates intelligent alerts.
 * 
 * @package MtcInvoice Attendance
 * @version 1.0
 */

require_once __DIR__ . '/../api/config/config.php';

class AlertSystem {
    private $pdo;
    private $settings;
    
    public function __construct() {
        $database = new Database();
        $this->pdo = $database->getConnection();
        
        if (!$this->pdo) {
            throw new Exception('Database connection failed');
        }
        
        $this->loadSettings();
    }
    
    /**
     * Load attendance settings
     */
    private function loadSettings() {
        try {
            $stmt = $this->pdo->query("SELECT setting_key, setting_value FROM attendance_settings");
            $this->settings = [];
            while ($row = $stmt->fetch()) {
                $this->settings[$row['setting_key']] = $row['setting_value'];
            }
        } catch (Exception $e) {
            error_log("AlertSystem::loadSettings error: " . $e->getMessage());
            $this->settings = [];
        }
    }
    
    /**
     * Analyze worker patterns and generate alerts
     */
    public function analyzeWorkerPatterns($workerId) {
        try {
            $this->checkConsecutiveAbsences($workerId);
            $this->checkLateArrivalPattern($workerId);
            $this->checkProductivityDrop($workerId);
            $this->checkOvertimePattern($workerId);
            $this->checkAttendanceRate($workerId);
            
        } catch (Exception $e) {
            error_log("AlertSystem::analyzeWorkerPatterns error: " . $e->getMessage());
        }
    }
    
    /**
     * Check for consecutive absences
     */
    private function checkConsecutiveAbsences($workerId) {
        try {
            $threshold = $this->settings['consecutive_absence_alert'] ?? 3;
            
            $stmt = $this->pdo->prepare("
                SELECT attendance_date, status
                FROM attendance_records 
                WHERE worker_id = ? 
                AND attendance_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
                ORDER BY attendance_date DESC
                LIMIT 10
            ");
            $stmt->execute([$workerId]);
            $records = $stmt->fetchAll();
            
            $consecutiveAbsences = 0;
            $absenceDates = [];
            
            foreach ($records as $record) {
                if (in_array($record['status'], ['absent', 'sick_leave'])) {
                    $consecutiveAbsences++;
                    $absenceDates[] = $record['attendance_date'];
                } else {
                    break; // Break on first non-absence
                }
            }
            
            if ($consecutiveAbsences >= $threshold) {
                $this->createAlert($workerId, 'consecutive_absences', [
                    'count' => $consecutiveAbsences,
                    'dates' => $absenceDates,
                    'threshold' => $threshold
                ], 'high');
            }
            
        } catch (Exception $e) {
            error_log("AlertSystem::checkConsecutiveAbsences error: " . $e->getMessage());
        }
    }
    
    /**
     * Check for late arrival patterns
     */
    private function checkLateArrivalPattern($workerId) {
        try {
            $stmt = $this->pdo->prepare("
                SELECT COUNT(*) as late_count
                FROM attendance_records 
                WHERE worker_id = ? 
                AND status = 'late'
                AND attendance_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
            ");
            $stmt->execute([$workerId]);
            $result = $stmt->fetch();
            
            $lateCount = $result['late_count'];
            $threshold = 5; // 5 late arrivals in 30 days
            
            if ($lateCount >= $threshold) {
                $this->createAlert($workerId, 'frequent_late_arrivals', [
                    'count' => $lateCount,
                    'period' => '30 days',
                    'threshold' => $threshold
                ], 'medium');
            }
            
        } catch (Exception $e) {
            error_log("AlertSystem::checkLateArrivalPattern error: " . $e->getMessage());
        }
    }
    
    /**
     * Check for productivity drop
     */
    private function checkProductivityDrop($workerId) {
        try {
            $stmt = $this->pdo->prepare("
                SELECT 
                    AVG(CASE WHEN attendance_date >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) THEN productivity_score END) as recent_avg,
                    AVG(CASE WHEN attendance_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY) AND attendance_date < DATE_SUB(CURDATE(), INTERVAL 7 DAY) THEN productivity_score END) as previous_avg
                FROM attendance_records 
                WHERE worker_id = ? 
                AND productivity_score IS NOT NULL
                AND attendance_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
            ");
            $stmt->execute([$workerId]);
            $result = $stmt->fetch();
            
            if ($result['recent_avg'] && $result['previous_avg']) {
                $dropPercentage = (($result['previous_avg'] - $result['recent_avg']) / $result['previous_avg']) * 100;
                
                if ($dropPercentage >= 20) { // 20% drop in productivity
                    $this->createAlert($workerId, 'productivity_drop', [
                        'recent_avg' => round($result['recent_avg'], 2),
                        'previous_avg' => round($result['previous_avg'], 2),
                        'drop_percentage' => round($dropPercentage, 2)
                    ], 'medium');
                }
            }
            
        } catch (Exception $e) {
            error_log("AlertSystem::checkProductivityDrop error: " . $e->getMessage());
        }
    }
    
    /**
     * Check for excessive overtime pattern
     */
    private function checkOvertimePattern($workerId) {
        try {
            $stmt = $this->pdo->prepare("
                SELECT 
                    SUM(overtime_minutes) as total_overtime,
                    COUNT(*) as days_with_overtime
                FROM attendance_records 
                WHERE worker_id = ? 
                AND overtime_minutes > 0
                AND attendance_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
            ");
            $stmt->execute([$workerId]);
            $result = $stmt->fetch();
            
            $totalOvertimeHours = ($result['total_overtime'] ?? 0) / 60;
            $daysWithOvertime = $result['days_with_overtime'] ?? 0;
            
            // Alert if more than 40 hours overtime in 30 days or overtime on more than 15 days
            if ($totalOvertimeHours > 40 || $daysWithOvertime > 15) {
                $severity = $totalOvertimeHours > 60 ? 'high' : 'medium';
                
                $this->createAlert($workerId, 'excessive_overtime', [
                    'total_overtime_hours' => round($totalOvertimeHours, 2),
                    'days_with_overtime' => $daysWithOvertime,
                    'period' => '30 days'
                ], $severity);
            }
            
        } catch (Exception $e) {
            error_log("AlertSystem::checkOvertimePattern error: " . $e->getMessage());
        }
    }
    
    /**
     * Check overall attendance rate
     */
    private function checkAttendanceRate($workerId) {
        try {
            $stmt = $this->pdo->prepare("
                SELECT 
                    COUNT(*) as total_days,
                    COUNT(CASE WHEN status IN ('present', 'late', 'half_day') THEN 1 END) as attended_days
                FROM attendance_records 
                WHERE worker_id = ? 
                AND attendance_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
                AND status NOT IN ('holiday', 'vacation')
            ");
            $stmt->execute([$workerId]);
            $result = $stmt->fetch();
            
            if ($result['total_days'] > 0) {
                $attendanceRate = ($result['attended_days'] / $result['total_days']) * 100;
                
                if ($attendanceRate < 80) { // Less than 80% attendance
                    $severity = $attendanceRate < 60 ? 'critical' : 'high';
                    
                    $this->createAlert($workerId, 'low_attendance_rate', [
                        'attendance_rate' => round($attendanceRate, 2),
                        'attended_days' => $result['attended_days'],
                        'total_days' => $result['total_days'],
                        'period' => '30 days'
                    ], $severity);
                }
            }
            
        } catch (Exception $e) {
            error_log("AlertSystem::checkAttendanceRate error: " . $e->getMessage());
        }
    }
    
    /**
     * Create an alert
     */
    private function createAlert($workerId, $alertType, $triggerData, $severity = 'medium') {
        try {
            // Check if similar alert already exists and is unresolved
            $stmt = $this->pdo->prepare("
                SELECT id FROM attendance_alerts 
                WHERE worker_id = ? 
                AND alert_type = ? 
                AND is_resolved = 0
                AND created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
            ");
            $stmt->execute([$workerId, $alertType]);
            
            if ($stmt->fetch()) {
                return; // Alert already exists
            }
            
            // Generate alert message
            $message = $this->generateAlertMessage($alertType, $triggerData);
            
            // Insert new alert
            $stmt = $this->pdo->prepare("
                INSERT INTO attendance_alerts (
                    worker_id, alert_type, alert_message, severity, trigger_data
                ) VALUES (?, ?, ?, ?, ?)
            ");
            
            $stmt->execute([
                $workerId,
                $alertType,
                $message,
                $severity,
                json_encode($triggerData)
            ]);
            
            // Create notification
            $this->createNotification($workerId, $alertType, $message, $severity);
            
        } catch (Exception $e) {
            error_log("AlertSystem::createAlert error: " . $e->getMessage());
        }
    }
    
    /**
     * Generate alert message based on type and data
     */
    private function generateAlertMessage($alertType, $triggerData) {
        switch ($alertType) {
            case 'consecutive_absences':
                return "Worker has been absent for {$triggerData['count']} consecutive days. Threshold: {$triggerData['threshold']} days.";
                
            case 'frequent_late_arrivals':
                return "Worker has {$triggerData['count']} late arrivals in the last {$triggerData['period']}. Threshold: {$triggerData['threshold']}.";
                
            case 'productivity_drop':
                return "Productivity dropped by {$triggerData['drop_percentage']}% (from {$triggerData['previous_avg']} to {$triggerData['recent_avg']}).";
                
            case 'excessive_overtime':
                return "Excessive overtime: {$triggerData['total_overtime_hours']} hours over {$triggerData['days_with_overtime']} days in the last {$triggerData['period']}.";
                
            case 'low_attendance_rate':
                return "Low attendance rate: {$triggerData['attendance_rate']}% ({$triggerData['attended_days']}/{$triggerData['total_days']} days) in the last {$triggerData['period']}.";
                
            default:
                return "Attendance pattern detected that requires attention.";
        }
    }
    
    /**
     * Create notification for alert
     */
    private function createNotification($workerId, $alertType, $message, $severity) {
        try {
            // Get worker info
            $stmt = $this->pdo->prepare("
                SELECT w.name, w.manager_id, m.user_id as manager_user_id
                FROM workers w
                LEFT JOIN workers m ON w.manager_id = m.id
                WHERE w.id = ?
            ");
            $stmt->execute([$workerId]);
            $worker = $stmt->fetch();
            
            if (!$worker) return;
            
            $title = ucfirst(str_replace('_', ' ', $alertType));
            $notificationMessage = "Alert for {$worker['name']}: $message";
            
            // Notify manager if exists
            if ($worker['manager_user_id']) {
                $this->insertNotification($worker['manager_user_id'], $workerId, $alertType, $title, $notificationMessage);
            }
            
            // Notify admin users
            $stmt = $this->pdo->query("SELECT id FROM users WHERE role = 'admin' AND status = 1");
            while ($admin = $stmt->fetch()) {
                $this->insertNotification($admin['id'], $workerId, $alertType, $title, $notificationMessage);
            }
            
        } catch (Exception $e) {
            error_log("AlertSystem::createNotification error: " . $e->getMessage());
        }
    }
    
    /**
     * Insert notification record
     */
    private function insertNotification($userId, $workerId, $notificationType, $title, $message) {
        try {
            $stmt = $this->pdo->prepare("
                INSERT INTO attendance_notifications (
                    user_id, worker_id, notification_type, title, message
                ) VALUES (?, ?, ?, ?, ?)
            ");
            
            $stmt->execute([$userId, $workerId, $notificationType, $title, $message]);
            
        } catch (Exception $e) {
            error_log("AlertSystem::insertNotification error: " . $e->getMessage());
        }
    }
    
    /**
     * Get active alerts
     */
    public function getActiveAlerts($filters = []) {
        try {
            $where = ['aa.is_resolved = 0'];
            $params = [];
            
            if (!empty($filters['worker_id'])) {
                $where[] = 'aa.worker_id = ?';
                $params[] = $filters['worker_id'];
            }
            
            if (!empty($filters['severity'])) {
                $where[] = 'aa.severity = ?';
                $params[] = $filters['severity'];
            }
            
            if (!empty($filters['alert_type'])) {
                $where[] = 'aa.alert_type = ?';
                $params[] = $filters['alert_type'];
            }
            
            $stmt = $this->pdo->prepare("
                SELECT aa.*, w.employee_id, w.name as worker_name, w.department
                FROM attendance_alerts aa
                JOIN workers w ON aa.worker_id = w.id
                WHERE " . implode(' AND ', $where) . "
                ORDER BY aa.severity DESC, aa.created_at DESC
            ");
            $stmt->execute($params);
            
            $alerts = $stmt->fetchAll();
            
            // Decode trigger data
            foreach ($alerts as &$alert) {
                if ($alert['trigger_data']) {
                    $alert['trigger_data'] = json_decode($alert['trigger_data'], true);
                }
            }
            
            return $alerts;
            
        } catch (Exception $e) {
            error_log("AlertSystem::getActiveAlerts error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Resolve an alert
     */
    public function resolveAlert($alertId, $resolvedBy, $notes = null) {
        try {
            $stmt = $this->pdo->prepare("
                UPDATE attendance_alerts 
                SET is_resolved = 1, resolved_by = ?, resolved_at = NOW(), resolved_notes = ?
                WHERE id = ?
            ");
            
            return $stmt->execute([$resolvedBy, $notes, $alertId]);
            
        } catch (Exception $e) {
            error_log("AlertSystem::resolveAlert error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get alert statistics
     */
    public function getAlertStatistics($filters = []) {
        try {
            $where = ['1=1'];
            $params = [];
            
            if (!empty($filters['start_date'])) {
                $where[] = 'aa.created_at >= ?';
                $params[] = $filters['start_date'];
            } else {
                $where[] = 'aa.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)';
            }
            
            if (!empty($filters['end_date'])) {
                $where[] = 'aa.created_at <= ?';
                $params[] = $filters['end_date'];
            }
            
            $stmt = $this->pdo->prepare("
                SELECT 
                    COUNT(*) as total_alerts,
                    COUNT(CASE WHEN is_resolved = 0 THEN 1 END) as active_alerts,
                    COUNT(CASE WHEN severity = 'critical' THEN 1 END) as critical_alerts,
                    COUNT(CASE WHEN severity = 'high' THEN 1 END) as high_alerts,
                    COUNT(CASE WHEN severity = 'medium' THEN 1 END) as medium_alerts,
                    COUNT(CASE WHEN severity = 'low' THEN 1 END) as low_alerts,
                    alert_type,
                    COUNT(*) as type_count
                FROM attendance_alerts aa
                WHERE " . implode(' AND ', $where) . "
                GROUP BY alert_type
                ORDER BY type_count DESC
            ");
            $stmt->execute($params);
            
            return $stmt->fetchAll();
            
        } catch (Exception $e) {
            error_log("AlertSystem::getAlertStatistics error: " . $e->getMessage());
            return [];
        }
    }
}
