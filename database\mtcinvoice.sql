

--
-- Database: `mtcinvoice_db`
--

-- --------------------------------------------------------

--
-- Table structure for table `documents`
--

CREATE TABLE documents (
    id INT AUTO_INCREMENT PRIMARY KEY,
    file_name VARCHAR(255) NOT NULL,
    file_type VARCHAR(100) NOT NULL,
    file_size_kb FLOAT NOT NULL,
    uploaded_by VARCHAR(100) NOT NULL,
    upload_date DATETIME NOT NULL,
    download_count INT DEFAULT 0,
    status ENUM('Available', 'Unavailable') DEFAULT 'Available',
    file_path TEXT -- Optional: if you want to store path to the file on server
)ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;;




-- --------------------------------------------------------

--
-- Table structure for table `invoice_data_items`
--

CREATE TABLE `invoice_data_items` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `description` text NOT NULL,
  `location` varchar(255) NOT NULL,
  `qr` varchar(255) NOT NULL,
  `lpo` varchar(255) DEFAULT NULL,
  `inb` varchar(255) DEFAULT NULL,
  `amount` decimal(10,2) DEFAULT 0.00,
  `w_a` varchar(255) DEFAULT NULL,
  `payment_status` enum('pending','paid','overdue') DEFAULT 'pending',
  `timestamp` bigint(20) DEFAULT NULL,
  `current_date_time` varchar(50) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `email_verified_at` timestamp NULL DEFAULT NULL,
  `password` varchar(255) NOT NULL,
  `role` enum('admin','user') NOT NULL DEFAULT 'user',
  `status` tinyint(1) NOT NULL DEFAULT 1,
  `last_login` datetime DEFAULT NULL,
  `remember_token` varchar(100) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `users_email_unique` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `users`
--

-- <NAME_EMAIL> is 'admin123' (plain text)
INSERT INTO `users` (`id`, `name`, `email`, `password`, `role`, `status`, `remember_token`, `created_at`, `updated_at`, `deleted_at`) VALUES
(1, 'Admin User', '<EMAIL>', 'admin123', 'admin', 1, NULL, '2025-06-15 07:25:33', '2025-06-15 07:25:33', NULL)
ON DUPLICATE KEY UPDATE 
    `password` = VALUES(`password`),
    `updated_at` = NOW();

CREATE TABLE `storage_usage` (
  `user_id` int(11) NOT NULL,
  `total_used` bigint(20) NOT NULL DEFAULT 0,
  `document_count` int(11) NOT NULL DEFAULT 0,
  `last_updated` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;



INSERT INTO `storage_usage` (`user_id`, `total_used`, `document_count`, `last_updated`) VALUES
(1, 600782, 6, '2025-06-17 01:18:07');

-- --------------------------------------------------------

--
-- Table structure for table `workers`
--

CREATE TABLE `workers` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `employee_id` varchar(50) NOT NULL UNIQUE,
  `name` varchar(255) NOT NULL,
  `email` varchar(255) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `department` varchar(100) DEFAULT NULL,
  `position` varchar(100) DEFAULT NULL,
  `hire_date` date DEFAULT NULL,
  `status` enum('active','inactive','terminated') NOT NULL DEFAULT 'active',
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `workers_employee_id_unique` (`employee_id`),
  KEY `workers_status_index` (`status`),
  KEY `workers_department_index` (`department`),
  KEY `workers_created_by_foreign` (`created_by`),
  CONSTRAINT `workers_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `worker_attendance`
--

CREATE TABLE `worker_attendance` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `worker_id` int(11) NOT NULL,
  `attendance_date` date NOT NULL,
  `status` enum('present','absent','late','half_day','holiday') NOT NULL DEFAULT 'present',
  `check_in_time` time DEFAULT NULL,
  `check_out_time` time DEFAULT NULL,
  `break_duration` int(11) DEFAULT 0 COMMENT 'Break duration in minutes',
  `overtime_hours` decimal(4,2) DEFAULT 0.00,
  `notes` text DEFAULT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `approved_by` bigint(20) UNSIGNED DEFAULT NULL,
  `approved_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `worker_attendance_unique` (`worker_id`, `attendance_date`),
  KEY `worker_attendance_date_index` (`attendance_date`),
  KEY `worker_attendance_status_index` (`status`),
  KEY `worker_attendance_created_by_foreign` (`created_by`),
  KEY `worker_attendance_approved_by_foreign` (`approved_by`),
  CONSTRAINT `worker_attendance_worker_id_foreign` FOREIGN KEY (`worker_id`) REFERENCES `workers` (`id`) ON DELETE CASCADE,
  CONSTRAINT `worker_attendance_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  CONSTRAINT `worker_attendance_approved_by_foreign` FOREIGN KEY (`approved_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `holidays`
--

CREATE TABLE `holidays` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `holiday_date` date NOT NULL,
  `type` enum('public','company','optional') NOT NULL DEFAULT 'public',
  `description` text DEFAULT NULL,
  `is_recurring` tinyint(1) NOT NULL DEFAULT 0,
  `status` enum('active','inactive') NOT NULL DEFAULT 'active',
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `holidays_date_index` (`holiday_date`),
  KEY `holidays_type_index` (`type`),
  KEY `holidays_status_index` (`status`),
  KEY `holidays_created_by_foreign` (`created_by`),
  CONSTRAINT `holidays_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `attendance_settings`
--

CREATE TABLE `attendance_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `setting_key` varchar(100) NOT NULL UNIQUE,
  `setting_value` text NOT NULL,
  `description` text DEFAULT NULL,
  `updated_by` bigint(20) UNSIGNED DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `attendance_settings_key_unique` (`setting_key`),
  KEY `attendance_settings_updated_by_foreign` (`updated_by`),
  CONSTRAINT `attendance_settings_updated_by_foreign` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Default attendance settings
--

INSERT INTO `attendance_settings` (`setting_key`, `setting_value`, `description`, `updated_by`) VALUES
('work_start_time', '09:00:00', 'Default work start time', 1),
('work_end_time', '17:00:00', 'Default work end time', 1),
('late_threshold_minutes', '15', 'Minutes after start time to mark as late', 1),
('half_day_hours', '4', 'Minimum hours for half day', 1),
('overtime_threshold_hours', '8', 'Hours after which overtime is calculated', 1),
('require_approval', '0', 'Whether attendance requires approval (0=no, 1=yes)', 1),
('consecutive_absence_alert', '3', 'Alert after this many consecutive absences', 1),
('monthly_working_days', '22', 'Expected working days per month', 1);

--
-- Sample workers data
--

INSERT INTO `workers` (`employee_id`, `name`, `email`, `phone`, `department`, `position`, `hire_date`, `status`, `created_by`) VALUES
('EMP001', 'John Smith', '<EMAIL>', '+**********', 'IT', 'Software Developer', '2024-01-15', 'active', 1),
('EMP002', 'Sarah Johnson', '<EMAIL>', '+**********', 'HR', 'HR Manager', '2024-02-01', 'active', 1),
('EMP003', 'Mike Wilson', '<EMAIL>', '+**********', 'Finance', 'Accountant', '2024-03-10', 'active', 1),
('EMP004', 'Lisa Brown', '<EMAIL>', '+**********', 'IT', 'System Administrator', '2024-04-05', 'active', 1),
('EMP005', 'David Lee', '<EMAIL>', '+**********', 'Operations', 'Operations Manager', '2024-05-20', 'active', 1);

--
-- Sample holidays data
--

INSERT INTO `holidays` (`name`, `holiday_date`, `type`, `description`, `is_recurring`, `status`, `created_by`) VALUES
('New Year Day', '2025-01-01', 'public', 'New Year celebration', 1, 'active', 1),
('Independence Day', '2025-07-04', 'public', 'Independence Day celebration', 1, 'active', 1),
('Christmas Day', '2025-12-25', 'public', 'Christmas celebration', 1, 'active', 1),
('Company Foundation Day', '2025-06-15', 'company', 'Company anniversary', 1, 'active', 1);

--
-- Update users table to include staff role for worker management
--

ALTER TABLE `users` MODIFY COLUMN `role` enum('admin','user','staff','manager') NOT NULL DEFAULT 'user';