

--
-- Database: `mtcinvoice_db`
--

-- --------------------------------------------------------

--
-- Table structure for table `documents`
--

CREATE TABLE documents (
    id INT AUTO_INCREMENT PRIMARY KEY,
    file_name VARCHAR(255) NOT NULL,
    file_type VARCHAR(100) NOT NULL,
    file_size_kb FLOAT NOT NULL,
    uploaded_by VARCHAR(100) NOT NULL,
    upload_date DATETIME NOT NULL,
    download_count INT DEFAULT 0,
    status ENUM('Available', 'Unavailable') DEFAULT 'Available',
    file_path TEXT -- Optional: if you want to store path to the file on server
)ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;;




-- --------------------------------------------------------

--
-- Table structure for table `invoice_data_items`
--

CREATE TABLE `invoice_data_items` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `description` text NOT NULL,
  `location` varchar(255) NOT NULL,
  `qr` varchar(255) NOT NULL,
  `lpo` varchar(255) DEFAULT NULL,
  `inb` varchar(255) DEFAULT NULL,
  `amount` decimal(10,2) DEFAULT 0.00,
  `w_a` varchar(255) DEFAULT NULL,
  `payment_status` enum('pending','paid','overdue') DEFAULT 'pending',
  `timestamp` bigint(20) DEFAULT NULL,
  `current_date_time` varchar(50) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `email_verified_at` timestamp NULL DEFAULT NULL,
  `password` varchar(255) NOT NULL,
  `role` enum('admin','user') NOT NULL DEFAULT 'user',
  `status` tinyint(1) NOT NULL DEFAULT 1,
  `last_login` datetime DEFAULT NULL,
  `remember_token` varchar(100) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `users_email_unique` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `users`
--

-- <NAME_EMAIL> is 'admin123' (plain text)
INSERT INTO `users` (`id`, `name`, `email`, `password`, `role`, `status`, `remember_token`, `created_at`, `updated_at`, `deleted_at`) VALUES
(1, 'Admin User', '<EMAIL>', 'admin123', 'admin', 1, NULL, '2025-06-15 07:25:33', '2025-06-15 07:25:33', NULL)
ON DUPLICATE KEY UPDATE 
    `password` = VALUES(`password`),
    `updated_at` = NOW();

CREATE TABLE `storage_usage` (
  `user_id` int(11) NOT NULL,
  `total_used` bigint(20) NOT NULL DEFAULT 0,
  `document_count` int(11) NOT NULL DEFAULT 0,
  `last_updated` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;



INSERT INTO `storage_usage` (`user_id`, `total_used`, `document_count`, `last_updated`) VALUES
(1, 600782, 6, '2025-06-17 01:18:07');



ALTER TABLE `users` MODIFY COLUMN `role` enum('admin','user','staff','manager') NOT NULL DEFAULT 'user';

-- --------------------------------------------------------

--
-- Enhanced Workers table with comprehensive employee information
--

ALTER TABLE `workers`
ADD COLUMN `user_id` bigint(20) UNSIGNED NULL AFTER `employee_id`,
ADD COLUMN `first_name` varchar(100) NOT NULL DEFAULT '' AFTER `name`,
ADD COLUMN `last_name` varchar(100) NOT NULL DEFAULT '' AFTER `first_name`,
ADD COLUMN `work_schedule` JSON NULL COMMENT 'Flexible work schedules' AFTER `hire_date`,
ADD COLUMN `hourly_rate` decimal(10,2) DEFAULT 0.00 AFTER `work_schedule`,
ADD COLUMN `manager_id` int(11) NULL AFTER `hourly_rate`,
ADD COLUMN `profile_image` varchar(255) NULL AFTER `manager_id`,
ADD COLUMN `emergency_contact` JSON NULL COMMENT 'Emergency contact information' AFTER `profile_image`,
ADD FOREIGN KEY `workers_user_id_foreign` (`user_id`) REFERENCES `users`(`id`) ON DELETE SET NULL,
ADD FOREIGN KEY `workers_manager_id_foreign` (`manager_id`) REFERENCES `workers`(`id`) ON DELETE SET NULL;

-- Update existing workers to split name into first_name and last_name
UPDATE `workers` SET
    `first_name` = SUBSTRING_INDEX(`name`, ' ', 1),
    `last_name` = CASE
        WHEN LOCATE(' ', `name`) > 0 THEN SUBSTRING(`name`, LOCATE(' ', `name`) + 1)
        ELSE ''
    END
WHERE `first_name` = '' AND `last_name` = '';

-- --------------------------------------------------------

--
-- Enhanced Attendance Records table with analytics support
--

ALTER TABLE `worker_attendance`
RENAME TO `attendance_records`;

ALTER TABLE `attendance_records`
ADD COLUMN `break_duration_minutes` int DEFAULT 0 COMMENT 'Break duration in minutes' AFTER `break_duration`,
ADD COLUMN `overtime_minutes` int DEFAULT 0 COMMENT 'Overtime in minutes' AFTER `overtime_hours`,
ADD COLUMN `emoji_tags` JSON NULL COMMENT 'Emoji-based context tags' AFTER `notes`,
ADD COLUMN `location_checkin` varchar(255) NULL COMMENT 'GPS or office location' AFTER `emoji_tags`,
ADD COLUMN `ip_address` varchar(45) NULL COMMENT 'IP address for security tracking' AFTER `location_checkin`,
ADD COLUMN `weather_condition` varchar(100) NULL COMMENT 'Weather at check-in time' AFTER `ip_address`,
ADD COLUMN `productivity_score` tinyint(1) NULL COMMENT 'Daily productivity score 1-5' AFTER `weather_condition`,
MODIFY COLUMN `status` enum('present','absent','late','half_day','holiday','sick_leave','vacation','remote_work','business_trip') NOT NULL DEFAULT 'present',
MODIFY COLUMN `break_duration` int(11) DEFAULT 0 COMMENT 'Deprecated - use break_duration_minutes',
MODIFY COLUMN `overtime_hours` decimal(4,2) DEFAULT 0.00 COMMENT 'Deprecated - use overtime_minutes',
ADD INDEX `idx_date_status` (`attendance_date`, `status`),
ADD INDEX `idx_worker_date_range` (`worker_id`, `attendance_date`),
ADD INDEX `idx_productivity_score` (`productivity_score`);

-- --------------------------------------------------------

--
-- Attendance Analytics and Alerts table
--

CREATE TABLE `attendance_alerts` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `worker_id` int(11) NOT NULL,
  `alert_type` varchar(50) NOT NULL,
  `alert_message` text NOT NULL,
  `severity` enum('low','medium','high','critical') DEFAULT 'medium',
  `trigger_data` JSON NULL COMMENT 'Data that triggered the alert',
  `is_resolved` boolean DEFAULT FALSE,
  `resolved_by` bigint(20) UNSIGNED NULL,
  `resolved_at` timestamp NULL,
  `resolved_notes` text NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  FOREIGN KEY (`worker_id`) REFERENCES `workers`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`resolved_by`) REFERENCES `users`(`id`) ON DELETE SET NULL,
  INDEX `idx_worker_alert_type` (`worker_id`, `alert_type`),
  INDEX `idx_severity_resolved` (`severity`, `is_resolved`),
  INDEX `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Attendance Patterns table for analytics
--

CREATE TABLE `attendance_patterns` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `worker_id` int(11) NOT NULL,
  `pattern_type` varchar(50) NOT NULL COMMENT 'e.g., late_arrivals, frequent_absences',
  `pattern_data` JSON NOT NULL COMMENT 'Pattern analysis data',
  `confidence_score` decimal(3,2) NOT NULL COMMENT 'Pattern confidence 0.00-1.00',
  `first_detected` date NOT NULL,
  `last_updated` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `status` enum('active','resolved','monitoring') DEFAULT 'active',
  PRIMARY KEY (`id`),
  FOREIGN KEY (`worker_id`) REFERENCES `workers`(`id`) ON DELETE CASCADE,
  UNIQUE KEY `worker_pattern_unique` (`worker_id`, `pattern_type`),
  INDEX `idx_pattern_type_status` (`pattern_type`, `status`),
  INDEX `idx_confidence_score` (`confidence_score`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Work Schedules table for flexible scheduling
--

CREATE TABLE `work_schedules` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `worker_id` int(11) NOT NULL,
  `schedule_name` varchar(100) NOT NULL,
  `schedule_type` enum('fixed','flexible','shift','remote') DEFAULT 'fixed',
  `monday_start` time NULL,
  `monday_end` time NULL,
  `tuesday_start` time NULL,
  `tuesday_end` time NULL,
  `wednesday_start` time NULL,
  `wednesday_end` time NULL,
  `thursday_start` time NULL,
  `thursday_end` time NULL,
  `friday_start` time NULL,
  `friday_end` time NULL,
  `saturday_start` time NULL,
  `saturday_end` time NULL,
  `sunday_start` time NULL,
  `sunday_end` time NULL,
  `break_duration_minutes` int DEFAULT 60,
  `is_active` boolean DEFAULT TRUE,
  `effective_from` date NOT NULL,
  `effective_to` date NULL,
  `created_by` bigint(20) UNSIGNED NOT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  FOREIGN KEY (`worker_id`) REFERENCES `workers`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`created_by`) REFERENCES `users`(`id`),
  INDEX `idx_worker_active` (`worker_id`, `is_active`),
  INDEX `idx_effective_dates` (`effective_from`, `effective_to`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Attendance Reports table for saved reports
--

CREATE TABLE `attendance_reports` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `report_name` varchar(255) NOT NULL,
  `report_type` enum('daily','weekly','monthly','custom','analytics') NOT NULL,
  `report_config` JSON NOT NULL COMMENT 'Report configuration and filters',
  `generated_by` bigint(20) UNSIGNED NOT NULL,
  `generated_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `file_path` varchar(500) NULL COMMENT 'Path to generated report file',
  `file_size` bigint(20) NULL,
  `download_count` int DEFAULT 0,
  `is_scheduled` boolean DEFAULT FALSE,
  `schedule_config` JSON NULL COMMENT 'Scheduled report configuration',
  `last_generated` timestamp NULL,
  `status` enum('pending','completed','failed','archived') DEFAULT 'pending',
  PRIMARY KEY (`id`),
  FOREIGN KEY (`generated_by`) REFERENCES `users`(`id`),
  INDEX `idx_report_type_status` (`report_type`, `status`),
  INDEX `idx_generated_by_date` (`generated_by`, `generated_at`),
  INDEX `idx_scheduled_reports` (`is_scheduled`, `status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Attendance Notifications table for push notifications
--

CREATE TABLE `attendance_notifications` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `worker_id` int(11) NULL COMMENT 'Related worker if applicable',
  `notification_type` varchar(50) NOT NULL,
  `title` varchar(255) NOT NULL,
  `message` text NOT NULL,
  `data` JSON NULL COMMENT 'Additional notification data',
  `is_read` boolean DEFAULT FALSE,
  `is_sent` boolean DEFAULT FALSE,
  `sent_at` timestamp NULL,
  `read_at` timestamp NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`worker_id`) REFERENCES `workers`(`id`) ON DELETE SET NULL,
  INDEX `idx_user_read_status` (`user_id`, `is_read`),
  INDEX `idx_notification_type` (`notification_type`),
  INDEX `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Sample data for enhanced attendance system
--

-- Sample workers data (recreate the original workers table first)
CREATE TABLE IF NOT EXISTS `workers` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `employee_id` varchar(50) NOT NULL UNIQUE,
  `name` varchar(255) NOT NULL,
  `email` varchar(255) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `department` varchar(100) DEFAULT NULL,
  `position` varchar(100) DEFAULT NULL,
  `hire_date` date DEFAULT NULL,
  `status` enum('active','inactive','terminated') NOT NULL DEFAULT 'active',
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `workers_employee_id_unique` (`employee_id`),
  KEY `workers_status_index` (`status`),
  KEY `workers_department_index` (`department`),
  KEY `workers_created_by_foreign` (`created_by`),
  CONSTRAINT `workers_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Sample workers data
INSERT INTO `workers` (`employee_id`, `name`, `email`, `phone`, `department`, `position`, `hire_date`, `status`, `created_by`) VALUES
('EMP001', 'John Smith', '<EMAIL>', '+**********', 'IT', 'Software Developer', '2024-01-15', 'active', 1),
('EMP002', 'Sarah Johnson', '<EMAIL>', '+**********', 'HR', 'HR Manager', '2024-02-01', 'active', 1),
('EMP003', 'Mike Wilson', '<EMAIL>', '+**********', 'Finance', 'Accountant', '2024-03-10', 'active', 1),
('EMP004', 'Lisa Brown', '<EMAIL>', '+**********', 'IT', 'System Administrator', '2024-04-05', 'active', 1),
('EMP005', 'David Lee', '<EMAIL>', '+**********', 'Operations', 'Operations Manager', '2024-05-20', 'active', 1)
ON DUPLICATE KEY UPDATE
    `name` = VALUES(`name`),
    `email` = VALUES(`email`),
    `updated_at` = NOW();

-- Sample holidays data
INSERT INTO `holidays` (`name`, `holiday_date`, `type`, `description`, `is_recurring`, `status`, `created_by`) VALUES
('New Year Day', '2025-01-01', 'public', 'New Year celebration', 1, 'active', 1),
('Independence Day', '2025-07-04', 'public', 'Independence Day celebration', 1, 'active', 1),
('Christmas Day', '2025-12-25', 'public', 'Christmas celebration', 1, 'active', 1),
('Company Foundation Day', '2025-06-15', 'company', 'Company anniversary', 1, 'active', 1)
ON DUPLICATE KEY UPDATE
    `description` = VALUES(`description`),
    `updated_at` = NOW();

-- Default attendance settings (enhanced)
INSERT INTO `attendance_settings` (`setting_key`, `setting_value`, `description`, `updated_by`) VALUES
('work_start_time', '09:00:00', 'Default work start time', 1),
('work_end_time', '17:00:00', 'Default work end time', 1),
('late_threshold_minutes', '15', 'Minutes after start time to mark as late', 1),
('half_day_hours', '4', 'Minimum hours for half day', 1),
('overtime_threshold_hours', '8', 'Hours after which overtime is calculated', 1),
('require_approval', '0', 'Whether attendance requires approval (0=no, 1=yes)', 1),
('consecutive_absence_alert', '3', 'Alert after this many consecutive absences', 1),
('monthly_working_days', '22', 'Expected working days per month', 1),
('enable_emoji_tags', '1', 'Enable emoji-based status tagging', 1),
('enable_location_tracking', '1', 'Enable location tracking for check-ins', 1),
('enable_productivity_scoring', '1', 'Enable daily productivity scoring', 1),
('auto_break_deduction', '60', 'Automatic break deduction in minutes', 1)
ON DUPLICATE KEY UPDATE
    `setting_value` = VALUES(`setting_value`),
    `updated_at` = NOW();

-- Sample work schedules
INSERT INTO `work_schedules` (`worker_id`, `schedule_name`, `schedule_type`, `monday_start`, `monday_end`, `tuesday_start`, `tuesday_end`, `wednesday_start`, `wednesday_end`, `thursday_start`, `thursday_end`, `friday_start`, `friday_end`, `saturday_start`, `saturday_end`, `sunday_start`, `sunday_end`, `break_duration_minutes`, `is_active`, `effective_from`, `created_by`) VALUES
((SELECT id FROM workers WHERE employee_id = 'EMP001'), 'Standard Schedule', 'fixed', '09:00:00', '17:00:00', '09:00:00', '17:00:00', '09:00:00', '17:00:00', '09:00:00', '17:00:00', '09:00:00', '17:00:00', NULL, NULL, NULL, NULL, 60, TRUE, '2024-01-15', 1),
((SELECT id FROM workers WHERE employee_id = 'EMP002'), 'Flexible Schedule', 'flexible', '08:00:00', '18:00:00', '08:00:00', '18:00:00', '08:00:00', '18:00:00', '08:00:00', '18:00:00', '08:00:00', '16:00:00', NULL, NULL, NULL, NULL, 45, TRUE, '2024-02-01', 1),
((SELECT id FROM workers WHERE employee_id = 'EMP003'), 'Early Schedule', 'fixed', '08:30:00', '16:30:00', '08:30:00', '16:30:00', '08:30:00', '16:30:00', '08:30:00', '16:30:00', '08:30:00', '16:30:00', NULL, NULL, NULL, NULL, 45, TRUE, '2024-03-10', 1),
((SELECT id FROM workers WHERE employee_id = 'EMP004'), 'Shift Schedule', 'shift', '09:00:00', '17:00:00', '09:00:00', '17:00:00', '13:00:00', '21:00:00', '13:00:00', '21:00:00', '09:00:00', '17:00:00', NULL, NULL, NULL, NULL, 60, TRUE, '2024-04-05', 1),
((SELECT id FROM workers WHERE employee_id = 'EMP005'), 'Management Schedule', 'flexible', '08:00:00', '18:00:00', '08:00:00', '18:00:00', '08:00:00', '18:00:00', '08:00:00', '18:00:00', '08:00:00', '16:00:00', NULL, NULL, NULL, NULL, 60, TRUE, '2024-05-20', 1);

-- Sample attendance records for the past week
INSERT INTO `attendance_records` (`worker_id`, `attendance_date`, `status`, `check_in_time`, `check_out_time`, `break_duration_minutes`, `overtime_minutes`, `emoji_tags`, `notes`, `location_checkin`, `productivity_score`, `created_by`) VALUES
-- John Smith (EMP001) - Good attendance
((SELECT id FROM workers WHERE employee_id = 'EMP001'), CURDATE() - INTERVAL 6 DAY, 'present', '08:55:00', '17:05:00', 60, 5, JSON_ARRAY('😊', '🎯'), 'Productive day, completed all tasks', 'Office - Floor 2', 4, 1),
((SELECT id FROM workers WHERE employee_id = 'EMP001'), CURDATE() - INTERVAL 5 DAY, 'present', '09:02:00', '17:00:00', 55, 0, JSON_ARRAY('😊', '💻'), 'Working on new features', 'Office - Floor 2', 5, 1),
((SELECT id FROM workers WHERE employee_id = 'EMP001'), CURDATE() - INTERVAL 4 DAY, 'present', '08:58:00', '17:10:00', 60, 10, JSON_ARRAY('🚧', '⏰'), 'Meeting heavy day', 'Office - Floor 2', 3, 1),
((SELECT id FROM workers WHERE employee_id = 'EMP001'), CURDATE() - INTERVAL 3 DAY, 'present', '09:00:00', '17:00:00', 60, 0, JSON_ARRAY('😊'), 'Regular day', 'Office - Floor 2', 4, 1),
((SELECT id FROM workers WHERE employee_id = 'EMP001'), CURDATE() - INTERVAL 2 DAY, 'present', '09:05:00', '17:15:00', 65, 15, JSON_ARRAY('📈', '🎯'), 'High productivity day', 'Office - Floor 2', 5, 1),

-- Sarah Johnson (EMP002) - HR Manager with some flexibility
((SELECT id FROM workers WHERE employee_id = 'EMP002'), CURDATE() - INTERVAL 6 DAY, 'present', '08:30:00', '16:45:00', 45, 0, JSON_ARRAY('👨‍👩‍👧‍👦', '🏢'), 'HR interviews scheduled', 'Office - Floor 1', 4, 1),
((SELECT id FROM workers WHERE employee_id = 'EMP002'), CURDATE() - INTERVAL 5 DAY, 'present', '08:45:00', '17:00:00', 45, 0, JSON_ARRAY('😊', '📋'), 'Policy review meeting', 'Office - Floor 1', 4, 1),
((SELECT id FROM workers WHERE employee_id = 'EMP002'), CURDATE() - INTERVAL 4 DAY, 'remote_work', NULL, NULL, 0, 0, JSON_ARRAY('🏠', '💻'), 'Working from home - training prep', 'Remote', 4, 1),
((SELECT id FROM workers WHERE employee_id = 'EMP002'), CURDATE() - INTERVAL 3 DAY, 'present', '09:00:00', '17:30:00', 45, 30, JSON_ARRAY('🎯', '📈'), 'Performance reviews', 'Office - Floor 1', 5, 1),
((SELECT id FROM workers WHERE employee_id = 'EMP002'), CURDATE() - INTERVAL 2 DAY, 'present', '08:15:00', '16:30:00', 45, 0, JSON_ARRAY('😊'), 'Early start for interviews', 'Office - Floor 1', 4, 1),

-- Mike Wilson (EMP003) - Some late arrivals
((SELECT id FROM workers WHERE employee_id = 'EMP003'), CURDATE() - INTERVAL 6 DAY, 'late', '09:20:00', '17:30:00', 45, 10, JSON_ARRAY('⏰', '🚗'), 'Traffic delay', 'Office - Floor 3', 3, 1),
((SELECT id FROM workers WHERE employee_id = 'EMP003'), CURDATE() - INTERVAL 5 DAY, 'present', '08:30:00', '16:30:00', 45, 0, JSON_ARRAY('😊', '💰'), 'Month-end closing', 'Office - Floor 3', 4, 1),
((SELECT id FROM workers WHERE employee_id = 'EMP003'), CURDATE() - INTERVAL 4 DAY, 'late', '09:15:00', '17:15:00', 45, 0, JSON_ARRAY('⏰', '☕'), 'Overslept', 'Office - Floor 3', 3, 1),
((SELECT id FROM workers WHERE employee_id = 'EMP003'), CURDATE() - INTERVAL 3 DAY, 'present', '08:25:00', '16:35:00', 45, 10, JSON_ARRAY('😊', '📊'), 'Financial reports', 'Office - Floor 3', 4, 1),
((SELECT id FROM workers WHERE employee_id = 'EMP003'), CURDATE() - INTERVAL 2 DAY, 'sick_leave', NULL, NULL, 0, 0, JSON_ARRAY('😷'), 'Flu symptoms', NULL, NULL, 1),

-- Lisa Brown (EMP004) - IT Admin with varied schedule
((SELECT id FROM workers WHERE employee_id = 'EMP004'), CURDATE() - INTERVAL 6 DAY, 'present', '09:00:00', '17:00:00', 60, 0, JSON_ARRAY('💻', '🔧'), 'Server maintenance', 'Office - Server Room', 4, 1),
((SELECT id FROM workers WHERE employee_id = 'EMP004'), CURDATE() - INTERVAL 5 DAY, 'present', '13:00:00', '21:00:00', 60, 0, JSON_ARRAY('🌙', '💻'), 'Evening shift', 'Office - Server Room', 4, 1),
((SELECT id FROM workers WHERE employee_id = 'EMP004'), CURDATE() - INTERVAL 4 DAY, 'present', '13:00:00', '21:30:00', 60, 30, JSON_ARRAY('🚧', '⚡'), 'Emergency system fix', 'Office - Server Room', 5, 1),
((SELECT id FROM workers WHERE employee_id = 'EMP004'), CURDATE() - INTERVAL 3 DAY, 'present', '09:00:00', '17:00:00', 60, 0, JSON_ARRAY('😊', '💻'), 'Regular maintenance', 'Office - Server Room', 4, 1),
((SELECT id FROM workers WHERE employee_id = 'EMP004'), CURDATE() - INTERVAL 2 DAY, 'present', '08:45:00', '17:15:00', 60, 15, JSON_ARRAY('🎯', '🔧'), 'Network upgrades', 'Office - Server Room', 4, 1),

-- David Lee (EMP005) - Operations Manager
((SELECT id FROM workers WHERE employee_id = 'EMP005'), CURDATE() - INTERVAL 6 DAY, 'present', '08:00:00', '17:30:00', 60, 30, JSON_ARRAY('📈', '🎯'), 'Operations review', 'Office - Floor 1', 5, 1),
((SELECT id FROM workers WHERE employee_id = 'EMP005'), CURDATE() - INTERVAL 5 DAY, 'business_trip', NULL, NULL, 0, 0, JSON_ARRAY('✈️', '🏢'), 'Client meeting in another city', 'Client Site', NULL, 1),
((SELECT id FROM workers WHERE employee_id = 'EMP005'), CURDATE() - INTERVAL 4 DAY, 'business_trip', NULL, NULL, 0, 0, JSON_ARRAY('✈️', '🏢'), 'Client meeting continued', 'Client Site', NULL, 1),
((SELECT id FROM workers WHERE employee_id = 'EMP005'), CURDATE() - INTERVAL 3 DAY, 'present', '08:15:00', '18:00:00', 60, 45, JSON_ARRAY('📈', '⏰'), 'Catch up after trip', 'Office - Floor 1', 4, 1),
((SELECT id FROM workers WHERE employee_id = 'EMP005'), CURDATE() - INTERVAL 2 DAY, 'present', '08:00:00', '16:30:00', 60, 0, JSON_ARRAY('😊', '📋'), 'Team planning session', 'Office - Floor 1', 4, 1);